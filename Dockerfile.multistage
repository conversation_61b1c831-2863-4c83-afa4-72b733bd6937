# 基础阶段 - 安装依赖 (使用 Node.js 20 以满足 react-router-dom@7.6.1 要求)
FROM node:20-alpine AS base
WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# 开发阶段
FROM base AS development
WORKDIR /app
COPY . .
ENV UMI_ENV=development
CMD ["yarn", "dev"]

# 测试阶段
FROM base AS test
WORKDIR /app
COPY . .
ENV UMI_ENV=test
RUN yarn build:test
CMD ["yarn", "start"]

# 构建阶段
FROM base AS builder
WORKDIR /app
COPY . .
ARG BUILD_ENV=production
ENV UMI_ENV=$BUILD_ENV
RUN if [ "$BUILD_ENV" = "production" ]; then \
      yarn build:prod; \
    elif [ "$BUILD_ENV" = "test" ]; then \
      yarn build:test; \
    else \
      yarn build; \
    fi

# 生产阶段
FROM nginx:alpine AS production
COPY --from=builder /app/dist /usr/share/nginx/html
COPY ./docker/nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
HEALTHCHECK --interval=30s --timeout=3s CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1
CMD ["nginx", "-g", "daemon off;"]
