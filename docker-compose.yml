version: '3.8'

services:
  # 开发环境
  front-logix-admin-dev:
    build:
      context: .
      dockerfile: Dockerfile.multistage
      target: development
    container_name: front-logix-admin-dev
    ports:
      - "8001:8001"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - UMI_ENV=development
      - API_URL=http://localhost:3000
    networks:
      - front-network

  # 测试环境
  front-logix-admin-test:
    build:
      context: .
      dockerfile: Dockerfile.multistage
      target: test
    container_name: front-logix-admin-test
    ports:
      - "8002:80"
    environment:
      - NODE_ENV=test
      - UMI_ENV=test
      - API_URL=https://test-api.starrier.org
    networks:
      - front-network

  # 生产环境
  front-logix-admin-prod:
    build:
      context: .
      dockerfile: Dockerfile.multistage
      target: production
      args:
        - BUILD_ENV=production
    container_name: front-logix-admin-prod
    ports:
      - "8003:80"
    environment:
      - NODE_ENV=production
      - API_URL=https://api.starrier.org
    networks:
      - front-network
    restart: always

networks:
  front-network:
    driver: bridge
