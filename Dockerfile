# 构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# 复制源代码
COPY . .

# 设置环境变量
ARG BUILD_ENV=production
ENV UMI_ENV=$BUILD_ENV
ENV NODE_ENV=$BUILD_ENV

# 显示环境信息
RUN echo "Building for environment: $BUILD_ENV" && \
    echo "Node version: $(node -v)" && \
    echo "Yarn version: $(yarn -v)" && \
    echo "Directory structure:" && \
    ls -la

# 检查构建脚本
RUN echo "Available scripts:" && \
    cat package.json | grep -A 20 '"scripts"'

# 构建应用
RUN set -ex && \
    if [ "$BUILD_ENV" = "production" ]; then \
      echo "Running production build..." && \
      yarn build:prod || { echo "Production build failed"; exit 1; }; \
    elif [ "$BUILD_ENV" = "test" ]; then \
      echo "Running test build..." && \
      yarn build:test || { echo "Test build failed"; exit 1; }; \
    else \
      echo "Running default build..." && \
      yarn build || { echo "Default build failed"; exit 1; }; \
    fi && \
    echo "Build completed successfully" && \
    ls -la dist

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置
COPY ./docker/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
