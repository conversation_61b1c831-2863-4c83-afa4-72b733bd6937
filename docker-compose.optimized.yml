version: '3.8'

services:
  # 前端应用 - 优化版本
  frontend:
    image: ${IMAGE_NAME:-front_logix_admin}:${ENV:-latest}
    container_name: ${IMAGE_NAME:-front_logix_admin}-${ENV:-latest}
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        BUILD_ENV: ${ENV:-production}
    ports:
      - "8080:80"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 10s
    environment:
      - API_URL=${API_URL:-http://localhost:3000}
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
