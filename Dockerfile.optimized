# syntax=docker/dockerfile:1.4

# 依赖阶段 - 只安装生产依赖
FROM node:18-alpine AS deps
WORKDIR /app
COPY package.json yarn.lock ./
# 只安装生产依赖
RUN yarn install --frozen-lockfile --production --network-timeout 600000 && \
    yarn cache clean

# 构建阶段 - 安装所有依赖并构建
FROM node:18-alpine AS builder
WORKDIR /app
# 复制生产依赖
COPY --from=deps /app/node_modules ./node_modules
# 复制项目文件
COPY . .
# 安装开发依赖
RUN yarn install --frozen-lockfile --network-timeout 600000 && \
    yarn cache clean

# 设置环境变量
ARG BUILD_ENV=production
ENV UMI_ENV=$BUILD_ENV
ENV NODE_ENV=$BUILD_ENV

# 构建应用
RUN yarn build:$BUILD_ENV && \
    # 清理构建缓存
    rm -rf /tmp/* /root/.npm /root/.yarn

# 生产阶段 - 最小化镜像
FROM nginx:alpine-slim AS production
# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html
# 复制 nginx 配置
COPY ./docker/nginx.conf /etc/nginx/conf.d/default.conf

# 设置非 root 用户
RUN addgroup -g 101 -S nginx \
    && adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx nginx \
    && chown -R nginx:nginx /usr/share/nginx/html \
    # 清理不必要的包
    && rm -rf /var/cache/apk/*

# 使用非 root 用户
USER nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
