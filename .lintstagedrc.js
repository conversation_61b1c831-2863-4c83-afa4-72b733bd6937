module.exports = {
  '*.{js,jsx,ts,tsx}': (filenames) => {
    const filtered = filenames
      .filter((f) => !f.includes('node_modules/ms'))
      .slice(0, 10); // 每次最多处理10个文件

    if (filtered.length === 0) return [];

    return [
      'node --max-old-space-size=4096 ./node_modules/prettier/bin-prettier.js --write',
      'node --max-old-space-size=4096 ./node_modules/eslint/bin/eslint.js --fix --max-warnings=0',
    ];
  },
  '*.{css,less,scss,sass}': [
    'node --max-old-space-size=4096 ./node_modules/prettier/bin-prettier.js --write',
  ],
  '*.{json,md}': [
    'node --max-old-space-size=4096 ./node_modules/prettier/bin-prettier.js --write',
  ],
};
