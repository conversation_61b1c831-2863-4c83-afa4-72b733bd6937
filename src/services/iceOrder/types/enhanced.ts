/**
 * 冰块订单管理系统增强类型定义
 */
import { OrderStatus } from '../types';

// 地理位置类型
export interface GeoLocation {
  longitude: number;
  latitude: number;
}

// 地址类型
export interface Address {
  province: string;
  city: string;
  district: string;
  street?: string;
  detail: string;
  location?: GeoLocation;
}

// 用户类型扩展
export interface EnhancedUser {
  id: number;
  username: string;
  email: string;
  phone: string;
  type: 'individual' | 'business' | 'factory';
  businessType?: string; // 例如：奶茶店、餐厅、工厂等
  creditScore?: number; // 信用评分
  address?: Address;
  createdAt: string;
  lastLoginAt?: string;
}

// 冰块类型扩展
export interface EnhancedIceType {
  id: number;
  name: string;
  description: string;
  category: 'industrial' | 'food' | 'cooling'; // 工业冰/食用冰/降温冰
  specifications: {
    shape: string; // 形状
    size: string; // 尺寸
    weight: string; // 重量
    temperatureRange: {
      min: number; // 最低温度
      max: number; // 最高温度
    };
    duration: number; // 持续时间(小时)
  };
  certifications: string[]; // 认证标识，如：食品级认证、工业标准认证等
  images: string[]; // 图片URL列表
  model3d?: string; // 3D模型URL
}

// 价格类型
export interface PriceInfo {
  basePrice: number; // 基础价格
  unit: string; // 单位，如：元/吨、元/块等
  bulkDiscounts: {
    // 批量折扣
    quantity: number; // 数量阈值
    discount: number; // 折扣比例
  }[];
  seasonalFactors: {
    // 季节性因素
    season: 'spring' | 'summer' | 'autumn' | 'winter';
    factor: number; // 价格系数
  }[];
  emergencyFactor: number; // 紧急情况价格系数
  historyPrices: {
    // 历史价格
    date: string;
    price: number;
  }[];
}

// 工厂信息
export interface Factory {
  id: number;
  name: string;
  address: Address;
  contactPerson: string;
  contactPhone: string;
  serviceArea: {
    center: GeoLocation;
    radius: number; // 服务半径(km)
    customArea?: GeoLocation[]; // 自定义服务区域边界点
  };
  capacity: {
    dailyProduction: number; // 日产能(吨)
    currentInventory: number; // 当前库存(吨)
    maxInventory: number; // 最大库存容量(吨)
  };
  productionLines: {
    id: number;
    name: string;
    status: 'active' | 'maintenance' | 'offline';
    capacity: number; // 产能(吨/天)
  }[];
  certifications: string[]; // 工厂认证
  rating: number; // 评分(1-5)
  createdAt: string;
}

// 供应商信息
export interface Supplier {
  id: number;
  factoryId: number;
  name: string;
  description: string;
  contactPerson: string;
  contactPhone: string;
  email: string;
  address: Address;
  businessLicense: string; // 营业执照号
  creditScore: number; // 信用评分(0-100)
  transactionVolume: number; // 交易量
  starRating: number; // 星级评分(1-5)
  onTimeDeliveryRate: number; // 准时交付率(0-100%)
  products: number[]; // 产品ID列表
  blacklisted: boolean; // 是否黑名单
  blacklistReason?: string; // 黑名单原因
  createdAt: string;
}

// 订单扩展
export interface EnhancedOrder {
  id: number;
  orderNumber: string; // 订单编号
  userId: number;
  username: string;
  supplierId: number;
  supplierName: string;
  items: {
    id: number;
    iceTypeId: number;
    iceTypeName: string;
    quantity: number;
    size: string;
    unitPrice: number;
    totalPrice: number;
  }[];
  totalAmount: number; // 总金额
  deliveryInfo: {
    type: 'self-pickup' | 'delivery'; // 自提/配送
    address?: Address; // 配送地址
    contactPerson: string;
    contactPhone: string;
    expectedDeliveryTime: string; // 预期送达时间
    actualDeliveryTime?: string; // 实际送达时间
  };
  urgencyLevel: 'normal' | 'urgent' | 'emergency'; // 紧急程度
  status: OrderStatus;
  paymentStatus: 'unpaid' | 'paid' | 'refunded';
  paymentMethod?: 'alipay' | 'wechat' | 'bank-transfer' | 'cash';
  logisticsInfo?: {
    provider: string; // 物流提供商
    trackingNumber: string; // 物流单号
    vehicleType: string; // 车辆类型
    temperature: number; // 运输温度
    currentLocation?: GeoLocation; // 当前位置
    estimatedArrivalTime: string; // 预计到达时间
  };
  insurance?: {
    provider: string; // 保险提供商
    policyNumber: string; // 保单号
    coverage: number; // 保险金额
    validUntil: string; // 有效期
  };
  contract?: {
    url: string; // 合同URL
    signedAt: string; // 签署时间
    validUntil: string; // 有效期
  };
  carbonFootprint?: {
    emissions: number; // 碳排放量(kg)
    saved: number; // 节省的碳排放量(kg)
  };
  notes: string; // 备注
  createdAt: string;
  updatedAt: string;
}

// 天气数据
export interface WeatherData {
  city: string;
  date: string;
  temperature: number;
  humidity: number;
  weatherType: string; // 晴、多云、雨等
  heatWarningLevel: 'none' | 'yellow' | 'orange' | 'red'; // 高温预警等级
}

// 热力图数据
export interface HeatMapData {
  city: string;
  location: GeoLocation;
  demandLevel: number; // 需求热度(1-10)
  updatedAt: string;
}

// 行业报告
export interface IndustryReport {
  id: number;
  title: string;
  quarter: string; // 如：2023Q1
  summary: string;
  pdfUrl: string;
  publishedAt: string;
  categories: {
    name: string;
    growthRate: number; // 增长率
    marketShare: number; // 市场份额
  }[];
  regions: {
    name: string;
    demandVolume: number; // 需求量
    growthRate: number; // 增长率
  }[];
}

// 系统通知
export interface SystemNotification {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'error' | 'success';
  targetUserIds?: number[]; // 目标用户ID，为空表示所有用户
  read: boolean;
  createdAt: string;
}

// 风控预警
export interface RiskAlert {
  id: number;
  type:
    | 'price-anomaly'
    | 'monopoly-risk'
    | 'malicious-bidding'
    | 'inventory-warning';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  affectedArea?: string[];
  affectedProducts?: number[];
  suggestedActions: string[];
  resolved: boolean;
  createdAt: string;
  resolvedAt?: string;
}

// 统计数据
export interface Statistics {
  totalOrders: number;
  totalRevenue: number;
  totalUsers: {
    total: number;
    individual: number;
    business: number;
    factory: number;
  };
  ordersByStatus: {
    [key in OrderStatus]: number;
  };
  revenueByMonth: {
    month: string;
    revenue: number;
  }[];
  topProducts: {
    id: number;
    name: string;
    sales: number;
    revenue: number;
  }[];
  topRegions: {
    name: string;
    orders: number;
    revenue: number;
  }[];
}
