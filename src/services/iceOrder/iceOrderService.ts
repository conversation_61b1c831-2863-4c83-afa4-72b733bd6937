import { ApiResponse } from '@/types/api';
import { IceOrder, OrderStatus } from './types';

/**
 * 获取订单列表
 */
export async function getOrderList(): Promise<ApiResponse<IceOrder[]>> {
  try {
    const response = await fetch('/api/ice-orders');
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '获取订单列表失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 获取订单详情
 */
export async function getOrderDetail(
  id: number,
): Promise<ApiResponse<IceOrder>> {
  try {
    const response = await fetch(`/api/ice-orders/${id}`);
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '获取订单详情失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 删除订单
 */
export async function deleteOrder(id: number): Promise<ApiResponse<void>> {
  try {
    const response = await fetch(`/api/ice-orders/${id}`, {
      method: 'DELETE',
    });
    const data = await response.json();
    return {
      status: response.status,
      error: response.ok ? undefined : data.message || '删除订单失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 创建新订单
 */
export async function createOrder(
  orderData: Omit<IceOrder, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<ApiResponse<IceOrder>> {
  try {
    const response = await fetch('/api/ice-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...orderData,
        // 确保 items 数组格式正确
        items: orderData.items.map((item) => ({
          iceTypeId: item.iceTypeId,
          quantity: item.quantity,
          size: item.size,
        })),
      }),
    });
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '创建订单失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 更新订单
 */
export async function updateOrder(
  id: number,
  orderData: Partial<IceOrder>,
): Promise<ApiResponse<IceOrder>> {
  try {
    const response = await fetch(`/api/ice-orders/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...orderData,
        // 确保 items 数组格式正确
        items: orderData.items?.map((item) => ({
          iceTypeId: item.iceTypeId,
          quantity: item.quantity,
          size: item.size,
        })),
      }),
    });
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '更新订单失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 更新订单状态
 */
export async function updateOrderStatus(
  id: number,
  status: OrderStatus,
): Promise<ApiResponse<IceOrder>> {
  try {
    const response = await fetch(`/api/ice-orders/${id}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    });
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '更新订单状态失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}
