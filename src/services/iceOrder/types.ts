/**
 * 冰块订单管理系统类型定义
 */

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  phone: string;
}

// 用户列表响应类型
export interface UserListResponse {
  status: number;
  message: string;
  data: User[];
}

// 用户详情响应类型
export interface UserDetailResponse {
  status: number;
  message: string;
  data: User;
}

// 冰块类型
export interface IceType {
  id: number;
  name: string;
  description: string;
}

// 冰块类型列表响应类型
export interface IceTypeListResponse {
  status: number;
  message: string;
  data: IceType[];
}

// 冰块类型详情响应类型
export interface IceTypeDetailResponse {
  status: number;
  message: string;
  data: IceType;
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'PENDING', // 待处理
  CONFIRMED = 'CONFIRMED', // 已确认
  PROCESSING = 'PROCESSING', // 处理中
  SHIPPED = 'SHIPPED', // 已发货
  DELIVERED = 'DELIVERED', // 已送达
  CANCELLED = 'CANCELLED', // 已取消
}

// 订单状态中文映射
export const OrderStatusMap: Record<OrderStatus, string> = {
  [OrderStatus.PENDING]: '待处理',
  [OrderStatus.CONFIRMED]: '已确认',
  [OrderStatus.PROCESSING]: '处理中',
  [OrderStatus.SHIPPED]: '已发货',
  [OrderStatus.DELIVERED]: '已送达',
  [OrderStatus.CANCELLED]: '已取消',
};

// 订单状态颜色映射
export const OrderStatusColorMap: Record<OrderStatus, string> = {
  [OrderStatus.PENDING]: 'blue',
  [OrderStatus.CONFIRMED]: 'green',
  [OrderStatus.PROCESSING]: 'purple',
  [OrderStatus.SHIPPED]: 'cyan',
  [OrderStatus.DELIVERED]: 'success',
  [OrderStatus.CANCELLED]: 'red',
};

// 订单项类型
/*export interface OrderItem {
  id: number;
  iceTypeId: number;
  iceTypeName: string;
  quantity: number;
  size: string;
}*/

// 订单类型
export interface IceOrder {
  id: number;
  userId: number;
  username: string;
  items: OrderItem[];
  deliveryDateTime: string;
  deliveryAddress: string;
  contactPerson: string;
  contactPhone: string;
  status: OrderStatus;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: number;
  iceTypeId: number;
  iceTypeName: string;
  size: string;
  quantity: number;
  price: number;
}

// 订单列表响应类型
export interface IceOrderListResponse {
  status: number;
  message: string;
  data: IceOrder[];
}

// 订单详情响应类型
export interface IceOrderDetailResponse {
  status: number;
  message: string;
  data: IceOrder;
}

// 通用响应类型
export interface CommonResponse {
  status: number;
  message: string;
  data: any;
}

// 用户表单值类型
export interface UserFormValues {
  username: string;
  email: string;
  phone: string;
}

// 表格列定义类型
export interface UserTableColumn {
  title: string;
  dataIndex: keyof User | 'action';
  key: string;
  render?: (text: any, record: User) => React.ReactNode;
}
