import { ApiResponse } from '@/types/api';
import apiClient from '@/utils/apiClient';

export interface User {
  id: number;
  username: string;
  email: string;
  phone: string;
  role: string;
  status: 'ACTIVE' | 'INACTIVE';
}

/**
 * 获取用户列表
 */
export async function getUserList(): Promise<ApiResponse<User[]>> {
  try {
    const response = await apiClient.get('/api/users');
    if (response.status !== 200) {
      throw new Error(response.data?.message || '获取用户列表失败');
    }
    if (!response.data || !Array.isArray(response.data.data)) {
      throw new Error('返回数据格式不正确');
    }
    return {
      status: response.status,
      data: response.data.data,
      error: undefined,
    };
  } catch (error: unknown) {
    const status =
      (error as { response?: { status?: number } })?.response?.status || 500;
    const message =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message ||
      (error as { message?: string })?.message ||
      '获取用户列表失败';
    return {
      status,
      error: message,
    };
  }
}

/**
 * 创建用户
 */
export async function createUser(
  userData: Omit<User, 'id'>,
): Promise<ApiResponse<User>> {
  try {
    const response = await apiClient.post('/api/users', userData);
    if (response.status !== 200) {
      throw new Error(response.data?.message || '创建用户失败');
    }
    if (!response.data) {
      throw new Error('返回数据格式不正确');
    }
    return {
      status: response.status,
      data: response.data,
      error: undefined,
    };
  } catch (error: unknown) {
    const status =
      (error as { response?: { status?: number } })?.response?.status || 500;
    const message =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message ||
      (error as { message?: string })?.message ||
      '创建用户失败';
    return {
      status,
      error: message,
    };
  }
}

/**
 * 更新用户
 */
export async function updateUser(
  id: number,
  userData: Partial<User>,
): Promise<ApiResponse<User>> {
  try {
    const response = await apiClient.put(`/api/users/${id}`, userData);
    if (response.status !== 200) {
      throw new Error(response.data?.message || '更新用户失败');
    }
    if (!response.data) {
      throw new Error('返回数据格式不正确');
    }
    return {
      status: response.status,
      data: response.data,
      error: undefined,
    };
  } catch (error: unknown) {
    const status =
      (error as { response?: { status?: number } })?.response?.status || 500;
    const message =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message ||
      (error as { message?: string })?.message ||
      '更新用户失败';
    return {
      status,
      error: message,
    };
  }
}

/**
 * 删除用户
 */
export async function deleteUser(id: number): Promise<ApiResponse<void>> {
  try {
    const response = await apiClient.delete(`/api/users/${id}`);
    if (response.status !== 200) {
      throw new Error(response.data?.message || '删除用户失败');
    }
    return {
      status: response.status,
      data: undefined,
      error: undefined,
    };
  } catch (error: unknown) {
    const status =
      (error as { response?: { status?: number } })?.response?.status || 500;
    const message =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message ||
      (error as { message?: string })?.message ||
      '删除用户失败';
    return {
      status,
      error: message,
    };
  }
}

/**
 * 获取用户详情
 */
export async function getUserDetail(id: number): Promise<ApiResponse<User>> {
  try {
    const response = await apiClient.get(`/api/users/${id}`);
    if (response.status !== 200) {
      throw new Error(response.data?.message || '获取用户详情失败');
    }
    if (!response.data) {
      throw new Error('返回数据格式不正确');
    }
    return {
      status: response.status,
      data: response.data,
      error: undefined,
    };
  } catch (error: unknown) {
    const status =
      (error as { response?: { status?: number } })?.response?.status || 500;
    const message =
      (error as { response?: { data?: { message?: string } } })?.response?.data
        ?.message ||
      (error as { message?: string })?.message ||
      '获取用户详情失败';
    return {
      status,
      error: message,
    };
  }
}
