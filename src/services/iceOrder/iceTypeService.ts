import { ApiResponse } from '@/types/api';

export interface IceType {
  id: number;
  name: string;
  description: string;
  sizes: IceTypeSize[];
}

export interface IceTypeSize {
  size: string;
  price: number;
}

/**
 * 获取冰块类型列表
 */
export async function getIceTypeList(): Promise<ApiResponse<IceType[]>> {
  try {
    const response = await fetch('/api/ice-types');
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '获取冰块类型列表失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 创建冰块类型
 */
export async function createIceType(
  iceType: Omit<IceType, 'id'>,
): Promise<ApiResponse<IceType>> {
  try {
    const response = await fetch('/api/ice-types', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(iceType),
    });
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '创建冰块类型失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 更新冰块类型
 */
export async function updateIceType(
  id: number,
  iceType: Partial<IceType>,
): Promise<ApiResponse<IceType>> {
  try {
    const response = await fetch(`/api/ice-types/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(iceType),
    });
    const data = await response.json();
    return {
      status: response.status,
      data: response.ok ? data : undefined,
      error: response.ok ? undefined : data.message || '更新冰块类型失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}

/**
 * 删除冰块类型
 */
export async function deleteIceType(id: number): Promise<ApiResponse<void>> {
  try {
    const response = await fetch(`/api/ice-types/${id}`, {
      method: 'DELETE',
    });
    const data = await response.json();
    return {
      status: response.status,
      data: undefined,
      error: response.ok ? undefined : data.message || '删除冰块类型失败',
    };
  } catch (error) {
    return {
      status: 500,
      error: '网络错误，请稍后重试',
    };
  }
}
