/**
 * 报告服务
 */
import { ApiResponse } from '@/types/api';
import { del, get, post, put } from '@/utils/apiClient';
import {
  HeatMapData,
  IndustryReport,
  Statistics,
  WeatherData,
} from '../types/enhanced';

/**
 * 获取行业报告列表
 */
export async function getIndustryReportList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<IndustryReport[]>> {
  return get('/api/reports/industry', params);
}

/**
 * 获取行业报告详情
 */
export async function getIndustryReportDetail(
  id: number,
): Promise<ApiResponse<IndustryReport>> {
  return get(`/api/reports/industry/${id}`);
}

/**
 * 创建行业报告
 */
export async function createIndustryReport(
  data: Omit<IndustryReport, 'id' | 'publishedAt'>,
): Promise<ApiResponse<IndustryReport>> {
  return post('/api/reports/industry', data);
}

/**
 * 更新行业报告
 */
export async function updateIndustryReport(
  id: number,
  data: Partial<IndustryReport>,
): Promise<ApiResponse<IndustryReport>> {
  return put(`/api/reports/industry/${id}`, data);
}

/**
 * 删除行业报告
 */
export async function deleteIndustryReport(
  id: number,
): Promise<ApiResponse<any>> {
  return del(`/api/reports/industry/${id}`);
}

/**
 * 获取统计数据
 */
export async function getStatistics(params?: {
  startDate?: string;
  endDate?: string;
}): Promise<ApiResponse<Statistics>> {
  return get('/api/reports/statistics', params);
}

/**
 * 获取天气数据
 */
export async function getWeatherData(
  city: string,
): Promise<ApiResponse<WeatherData>> {
  return get('/api/reports/weather', { city });
}

/**
 * 获取热力图数据
 */
export async function getHeatMapData(
  region?: string,
): Promise<ApiResponse<HeatMapData[]>> {
  return get('/api/reports/heat-map', { region });
}

/**
 * 获取碳足迹报告
 */
export async function getCarbonFootprintReport(params?: {
  startDate?: string;
  endDate?: string;
}): Promise<ApiResponse<any>> {
  return get('/api/reports/carbon-footprint', params);
}
