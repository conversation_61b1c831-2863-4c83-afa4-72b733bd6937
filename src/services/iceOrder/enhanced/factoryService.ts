/**
 * 工厂信息服务
 * 使用 axios 客户端替代 umi-request
 */
import { del, get, post, put } from '@/utils/apiClient';
import { ApiResponse, Factory } from '../types/enhanced';

/**
 * 获取工厂列表
 */
export async function getFactoryList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<Factory[]>> {
  return get('/api/factories', params);
}

/**
 * 获取工厂详情
 */
export async function getFactoryDetail(
  id: number,
): Promise<ApiResponse<Factory>> {
  return get(`/api/factories/${id}`);
}

/**
 * 创建工厂
 */
export async function createFactory(
  data: Omit<Factory, 'id' | 'createdAt'>,
): Promise<ApiResponse<Factory>> {
  return post('/api/factories', data);
}

/**
 * 更新工厂信息
 */
export async function updateFactory(
  id: number,
  data: Partial<Factory>,
): Promise<ApiResponse<Factory>> {
  return put(`/api/factories/${id}`, data);
}

/**
 * 删除工厂
 */
export async function deleteFactory(id: number): Promise<ApiResponse<any>> {
  return del(`/api/factories/${id}`);
}

/**
 * 更新工厂服务区域
 */
export async function updateFactoryServiceArea(
  id: number,
  serviceArea: Factory['serviceArea'],
): Promise<ApiResponse<Factory>> {
  return put(`/api/factories/${id}/service-area`, serviceArea);
}

/**
 * 更新工厂产能信息
 */
export async function updateFactoryCapacity(
  id: number,
  capacity: Factory['capacity'],
): Promise<ApiResponse<Factory>> {
  return put(`/api/factories/${id}/capacity`, capacity);
}

/**
 * 更新生产线状态
 */
export async function updateProductionLineStatus(
  factoryId: number,
  lineId: number,
  status: 'active' | 'maintenance' | 'offline',
): Promise<ApiResponse<Factory>> {
  return put(`/api/factories/${factoryId}/production-lines/${lineId}/status`, {
    status,
  });
}
