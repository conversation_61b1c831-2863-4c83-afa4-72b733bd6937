/**
 * 风控服务
 */
import { get, post, put } from '@/utils/apiClient';
import { ApiResponse, RiskAlert, SystemNotification } from '../types/enhanced';

/**
 * 获取风控预警列表
 */
export async function getRiskAlertList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<RiskAlert[]>> {
  return get('/api/risk-control/alerts', params);
}

/**
 * 获取风控预警详情
 */
export async function getRiskAlertDetail(
  id: number,
): Promise<ApiResponse<RiskAlert>> {
  return get(`/api/risk-control/alerts/${id}`);
}

/**
 * 创建风控预警
 */
export async function createRiskAlert(
  data: Omit<RiskAlert, 'id' | 'createdAt' | 'resolvedAt'>,
): Promise<ApiResponse<RiskAlert>> {
  return post('/api/risk-control/alerts', data);
}

/**
 * 更新风控预警
 */
export async function updateRiskAlert(
  id: number,
  data: Partial<RiskAlert>,
): Promise<ApiResponse<RiskAlert>> {
  return put(`/api/risk-control/alerts/${id}`, data);
}

/**
 * 解决风控预警
 */
export async function resolveRiskAlert(
  id: number,
): Promise<ApiResponse<RiskAlert>> {
  return put(`/api/risk-control/alerts/${id}/resolve`, {});
}

/**
 * 获取系统通知列表
 */
export async function getSystemNotificationList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<SystemNotification[]>> {
  return get('/api/risk-control/notifications', params);
}

/**
 * 创建系统通知
 */
export async function createSystemNotification(
  data: Omit<SystemNotification, 'id' | 'createdAt' | 'read'>,
): Promise<ApiResponse<SystemNotification>> {
  return post('/api/risk-control/notifications', data);
}

/**
 * 标记通知为已读
 */
export async function markNotificationAsRead(
  id: number,
): Promise<ApiResponse<SystemNotification>> {
  return put(`/api/risk-control/notifications/${id}/read`, {});
}

/**
 * 检测价格异常
 */
export async function detectPriceAnomaly(params?: {
  region?: string;
  productId?: number;
}): Promise<ApiResponse<any>> {
  return get('/api/risk-control/detect-price-anomaly', params);
}

/**
 * 检测区域垄断风险
 */
export async function detectMonopolyRisk(
  region: string,
): Promise<ApiResponse<any>> {
  return get('/api/risk-control/detect-monopoly-risk', { region });
}

/**
 * 检测恶意竞价
 */
export async function detectMaliciousBidding(
  supplierId?: number,
): Promise<ApiResponse<any>> {
  return get('/api/risk-control/detect-malicious-bidding', { supplierId });
}
