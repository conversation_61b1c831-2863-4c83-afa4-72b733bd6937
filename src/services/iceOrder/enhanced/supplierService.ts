/**
 * 供应商服务
 * 使用 axios 客户端替代 umi-request
 */
import { del, get, post, put } from '@/utils/apiClient';
import { ApiResponse, Supplier } from '../types/enhanced';

/**
 * 获取供应商列表
 */
export async function getSupplierList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<Supplier[]>> {
  return get('/api/suppliers', params);
}

/**
 * 获取供应商详情
 */
export async function getSupplierDetail(
  id: number,
): Promise<ApiResponse<Supplier>> {
  return get(`/api/suppliers/${id}`);
}

/**
 * 创建供应商
 */
export async function createSupplier(
  data: Omit<Supplier, 'id' | 'createdAt'>,
): Promise<ApiResponse<Supplier>> {
  return post('/api/suppliers', data);
}

/**
 * 更新供应商信息
 */
export async function updateSupplier(
  id: number,
  data: Partial<Supplier>,
): Promise<ApiResponse<Supplier>> {
  return put(`/api/suppliers/${id}`, data);
}

/**
 * 删除供应商
 */
export async function deleteSupplier(id: number): Promise<ApiResponse<any>> {
  return del(`/api/suppliers/${id}`);
}

/**
 * 更新供应商信用评分
 */
export async function updateSupplierCreditScore(
  id: number,
  creditScore: number,
): Promise<ApiResponse<Supplier>> {
  return put(`/api/suppliers/${id}/credit-score`, { creditScore });
}

/**
 * 将供应商加入黑名单
 */
export async function blacklistSupplier(
  id: number,
  reason: string,
): Promise<ApiResponse<Supplier>> {
  return put(`/api/suppliers/${id}/blacklist`, {
    blacklisted: true,
    blacklistReason: reason,
  });
}

/**
 * 将供应商从黑名单移除
 */
export async function removeSupplierFromBlacklist(
  id: number,
): Promise<ApiResponse<Supplier>> {
  return put(`/api/suppliers/${id}/blacklist`, {
    blacklisted: false,
    blacklistReason: '',
  });
}

/**
 * 获取供应商评价列表
 */
export async function getSupplierRatings(
  id: number,
): Promise<ApiResponse<any[]>> {
  return get(`/api/suppliers/${id}/ratings`);
}
