/**
 * 增强冰块类型服务
 */
import { ApiResponse } from '@/types/api';
import { del, get, post, put } from '@/utils/apiClient';
import { EnhancedIceType, PriceInfo } from '../types/enhanced';

/**
 * 获取增强冰块类型列表
 */
export async function getEnhancedIceTypeList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<EnhancedIceType[]>> {
  return get('/api/enhanced-ice-types', params);
}

/**
 * 获取增强冰块类型详情
 */
export async function getEnhancedIceTypeDetail(
  id: number,
): Promise<ApiResponse<EnhancedIceType>> {
  return get(`/api/enhanced-ice-types/${id}`);
}

/**
 * 创建增强冰块类型
 */
export async function createEnhancedIceType(
  data: Omit<EnhancedIceType, 'id'>,
): Promise<ApiResponse<EnhancedIceType>> {
  return post('/api/enhanced-ice-types', data);
}

/**
 * 更新增强冰块类型
 */
export async function updateEnhancedIceType(
  id: number,
  data: Partial<EnhancedIceType>,
): Promise<ApiResponse<EnhancedIceType>> {
  return put(`/api/enhanced-ice-types/${id}`, data);
}

/**
 * 删除增强冰块类型
 */
export async function deleteEnhancedIceType(
  id: number,
): Promise<ApiResponse<any>> {
  return del(`/api/enhanced-ice-types/${id}`);
}

/**
 * 获取冰块价格信息
 */
export async function getIceTypePrice(
  id: number,
): Promise<ApiResponse<PriceInfo>> {
  return get(`/api/enhanced-ice-types/${id}/price`);
}

/**
 * 更新冰块价格信息
 */
export async function updateIceTypePrice(
  id: number,
  priceInfo: PriceInfo,
): Promise<ApiResponse<PriceInfo>> {
  return put(`/api/enhanced-ice-types/${id}/price`, priceInfo);
}

/**
 * 获取冰块历史价格
 */
export async function getIceTypeHistoryPrices(
  id: number,
  params?: { startDate?: string; endDate?: string },
): Promise<ApiResponse<PriceInfo['historyPrices']>> {
  return get(`/api/enhanced-ice-types/${id}/history-prices`, params);
}

/**
 * 获取冰块3D模型
 */
export async function getIceType3DModel(
  id: number,
): Promise<ApiResponse<{ modelUrl: string }>> {
  return get(`/api/enhanced-ice-types/${id}/3d-model`);
}

/**
 * 上传冰块3D模型
 */
export async function uploadIceType3DModel(
  id: number,
  file: File,
): Promise<ApiResponse<{ modelUrl: string }>> {
  const formData = new FormData();
  formData.append('model', file);
  return post(`/api/enhanced-ice-types/${id}/3d-model`, formData);
}
