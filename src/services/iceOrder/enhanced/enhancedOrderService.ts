/**
 * 增强订单服务
 */
import { get, post, put } from '@/utils/apiClient';
import { OrderStatus } from '../types';
import { ApiResponse, EnhancedOrder } from '../types/enhanced';

/**
 * 获取增强订单列表
 */
export async function getEnhancedOrderList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<EnhancedOrder[]>> {
  return get('/api/enhanced-orders', params);
}

/**
 * 获取增强订单详情
 */
export async function getEnhancedOrderDetail(
  id: number,
): Promise<ApiResponse<EnhancedOrder>> {
  return get(`/api/enhanced-orders/${id}`);
}

/**
 * 创建增强订单
 */
export async function createEnhancedOrder(
  data: Omit<EnhancedOrder, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>,
): Promise<ApiResponse<EnhancedOrder>> {
  return post('/api/enhanced-orders', data);
}

/**
 * 更新增强订单
 */
export async function updateEnhancedOrder(
  id: number,
  data: Partial<EnhancedOrder>,
): Promise<ApiResponse<EnhancedOrder>> {
  return put(`/api/enhanced-orders/${id}`, data);
}

/**
 * 更新订单状态
 */
export async function updateEnhancedOrderStatus(
  id: number,
  status: OrderStatus,
): Promise<ApiResponse<EnhancedOrder>> {
  return put(`/api/enhanced-orders/${id}/status`, { status });
}

/**
 * 更新订单支付状态
 */
export async function updateOrderPaymentStatus(
  id: number,
  paymentStatus: 'unpaid' | 'paid' | 'refunded',
  paymentMethod?: 'alipay' | 'wechat' | 'bank-transfer' | 'cash',
): Promise<ApiResponse<EnhancedOrder>> {
  return put(`/api/enhanced-orders/${id}/payment`, {
    paymentStatus,
    paymentMethod,
  });
}

/**
 * 更新物流信息
 */
export async function updateOrderLogistics(
  id: number,
  logisticsInfo: EnhancedOrder['logisticsInfo'],
): Promise<ApiResponse<EnhancedOrder>> {
  return put(`/api/enhanced-orders/${id}/logistics`, logisticsInfo);
}

/**
 * 添加保险信息
 */
export async function addOrderInsurance(
  id: number,
  insurance: EnhancedOrder['insurance'],
): Promise<ApiResponse<EnhancedOrder>> {
  return put(`/api/enhanced-orders/${id}/insurance`, insurance);
}

/**
 * 生成电子合同
 */
export async function generateOrderContract(
  id: number,
): Promise<ApiResponse<EnhancedOrder['contract']>> {
  return post(`/api/enhanced-orders/${id}/contract`, {});
}

/**
 * 计算订单碳足迹
 */
export async function calculateOrderCarbonFootprint(
  id: number,
): Promise<ApiResponse<EnhancedOrder['carbonFootprint']>> {
  return get(`/api/enhanced-orders/${id}/carbon-footprint`);
}

/**
 * 获取订单统计数据
 */
export async function getOrderStatistics(params?: {
  startDate?: string;
  endDate?: string;
}): Promise<ApiResponse<any>> {
  return get('/api/enhanced-orders/statistics', params);
}
