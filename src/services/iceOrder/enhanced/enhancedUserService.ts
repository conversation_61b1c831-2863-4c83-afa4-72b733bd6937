/**
 * 增强用户服务
 * 使用 axios 客户端替代兼容层
 */
import { del, get, post, put } from '@/utils/apiClient';
import { ApiResponse, EnhancedUser } from '../types/enhanced';

/**
 * 获取增强用户列表
 */
export async function getEnhancedUserList(params?: {
  [key: string]: any;
}): Promise<ApiResponse<EnhancedUser[]>> {
  return get('/api/enhanced-users', params);
}

/**
 * 获取增强用户详情
 */
export async function getEnhancedUserDetail(
  id: number,
): Promise<ApiResponse<EnhancedUser>> {
  return get(`/api/enhanced-users/${id}`);
}

/**
 * 创建增强用户
 */
export async function createEnhancedUser(
  data: Omit<EnhancedUser, 'id' | 'createdAt' | 'lastLoginAt'>,
): Promise<ApiResponse<EnhancedUser>> {
  return post('/api/enhanced-users', data);
}

/**
 * 更新增强用户
 */
export async function updateEnhancedUser(
  id: number,
  data: Partial<EnhancedUser>,
): Promise<ApiResponse<EnhancedUser>> {
  return put(`/api/enhanced-users/${id}`, data);
}

/**
 * 删除增强用户
 */
export async function deleteEnhancedUser(
  id: number,
): Promise<ApiResponse<any>> {
  return del(`/api/enhanced-users/${id}`);
}

/**
 * 更新用户信用评分
 */
export async function updateUserCreditScore(
  id: number,
  creditScore: number,
): Promise<ApiResponse<EnhancedUser>> {
  return put(`/api/enhanced-users/${id}/credit-score`, { creditScore });
}

/**
 * 获取用户订单历史
 */
export async function getUserOrderHistory(
  id: number,
): Promise<ApiResponse<any[]>> {
  return get(`/api/enhanced-users/${id}/orders`);
}

/**
 * 获取用户地址
 */
export async function getUserAddress(
  id: number,
): Promise<ApiResponse<EnhancedUser['address']>> {
  return get(`/api/enhanced-users/${id}/address`);
}

/**
 * 更新用户地址
 */
export async function updateUserAddress(
  id: number,
  address: EnhancedUser['address'],
): Promise<ApiResponse<EnhancedUser>> {
  return put(`/api/enhanced-users/${id}/address`, address);
}
