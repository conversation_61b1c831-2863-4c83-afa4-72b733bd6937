import {
  OperationLog,
  OperationLogList,
  OperationLogQueryParams,
} from '@/services/operationLog/type';
import { ApiResponse } from '@/types/api';
import { get } from '@/utils';

/**
 * 获取操作日志列表
 * @param params 查询参数
 */
export async function getOperationLogs(
  params: OperationLogQueryParams,
): Promise<ApiResponse<OperationLogList>> {
  return get<OperationLogList>('/api/system/operation-logs', params);
}

/**
 * 获取操作日志详情
 * @param id 日志ID
 */
export async function getOperationLogDetail(
  id: string,
): Promise<ApiResponse<OperationLog>> {
  return get<OperationLog>(`/api/system/operation-logs/${id}`);
}

/**
 * 导出操作日志
 * @param params 查询参数
 */
export async function exportOperationLogs(
  params: Omit<OperationLogQueryParams, 'current' | 'pageSize'>,
): Promise<ApiResponse<Blob>> {
  return get<Blob>('/api/system/operation-logs/export', params, {
    responseType: 'blob',
  });
}
