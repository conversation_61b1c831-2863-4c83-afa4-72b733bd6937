// 操作日志查询参数接口
import { OPERATION_ACTION_TYPES } from '@/constants';

export interface OperationLogQueryParams {
  current?: number;
  pageSize?: number;
  startTime?: string;
  endTime?: string;
  username?: string;
  action?: string;
  module?: string;
  resourceType?: string;
}

// 操作日志接口
export interface OperationLog {
  id: string;
  createdAt: string;
  username: string;
  action: keyof typeof OPERATION_ACTION_TYPES;
  module: string;
  resourceId: string;
  resourceType: string;
  description: string;
  ipAddress: string;
  userAgent: string;
  details?: Record<string, any>;
}

export interface OperationLogList {
  list: OperationLog[];
  total: number;
  current: number;
  pageSize: number;
}
