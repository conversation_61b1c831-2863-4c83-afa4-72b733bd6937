import { apiClient } from '@/utils/apiClient';
import type {
  Message,
  MessageCreateData,
  MessageTemplate,
  PushConfig,
  PlatformConfig,
  UserPushPreference,
  MessageStats,
  BatchOperation,
  MessageSearchParams,
  PushTask,
  NotificationApiResponse,
  NotificationPaginatedResponse,
} from '@/types/notification';

// 消息服务
export const messageService = {
  // 获取消息列表
  getMessages: async (params?: MessageSearchParams): Promise<NotificationPaginatedResponse<Message>> => {
    return apiClient.get('/api/notification/messages', { params });
  },

  // 获取消息详情
  getMessageDetail: async (id: string): Promise<NotificationApiResponse<Message>> => {
    return apiClient.get(`/api/notification/messages/${id}`);
  },

  // 创建消息
  createMessage: async (data: MessageCreateData): Promise<NotificationApiResponse<Message>> => {
    return apiClient.post('/api/notification/messages', data);
  },

  // 标记消息为已读
  markAsRead: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.put(`/api/notification/messages/${id}/read`);
  },

  // 标记消息为已处理
  markAsProcessed: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.put(`/api/notification/messages/${id}/process`);
  },

  // 删除消息
  deleteMessage: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.delete(`/api/notification/messages/${id}`);
  },

  // 批量操作消息
  batchOperation: async (operation: BatchOperation): Promise<NotificationApiResponse> => {
    return apiClient.post('/api/notification/messages/batch', operation);
  },

  // 获取未读消息数量
  getUnreadCount: async (): Promise<NotificationApiResponse<{ count: number }>> => {
    return apiClient.get('/api/notification/messages/unread-count');
  },

  // 全部标记为已读
  markAllAsRead: async (): Promise<NotificationApiResponse> => {
    return apiClient.put('/api/notification/messages/read-all');
  },

  // 回复消息（用于私信等）
  replyMessage: async (id: string, content: string): Promise<NotificationApiResponse<Message>> => {
    return apiClient.post(`/api/notification/messages/${id}/reply`, { content });
  },
};

// 模板服务
export const templateService = {
  // 获取模板列表
  getTemplates: async (params?: {
    type?: string;
    businessType?: string;
    isActive?: boolean;
    page?: number;
    pageSize?: number;
  }): Promise<NotificationPaginatedResponse<MessageTemplate>> => {
    return apiClient.get('/api/notification/templates', { params });
  },

  // 获取模板详情
  getTemplateDetail: async (id: string): Promise<NotificationApiResponse<MessageTemplate>> => {
    return apiClient.get(`/api/notification/templates/${id}`);
  },

  // 创建模板
  createTemplate: async (data: Partial<MessageTemplate>): Promise<NotificationApiResponse<MessageTemplate>> => {
    return apiClient.post('/api/notification/templates', data);
  },

  // 更新模板
  updateTemplate: async (id: string, data: Partial<MessageTemplate>): Promise<NotificationApiResponse<MessageTemplate>> => {
    return apiClient.put(`/api/notification/templates/${id}`, data);
  },

  // 删除模板
  deleteTemplate: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.delete(`/api/notification/templates/${id}`);
  },

  // 预览模板
  previewTemplate: async (id: string, variables: Record<string, any>): Promise<NotificationApiResponse<{ title: string; content: string }>> => {
    return apiClient.post(`/api/notification/templates/${id}/preview`, { variables });
  },

  // 测试发送模板
  testTemplate: async (id: string, variables: Record<string, any>, testUsers: string[]): Promise<NotificationApiResponse> => {
    return apiClient.post(`/api/notification/templates/${id}/test`, { variables, testUsers });
  },
};

// 推送配置服务
export const pushConfigService = {
  // 获取推送配置列表
  getConfigs: async (): Promise<NotificationApiResponse<PushConfig[]>> => {
    return apiClient.get('/api/notification/push-configs');
  },

  // 获取推送配置详情
  getConfigDetail: async (id: string): Promise<NotificationApiResponse<PushConfig>> => {
    return apiClient.get(`/api/notification/push-configs/${id}`);
  },

  // 创建推送配置
  createConfig: async (data: Partial<PushConfig>): Promise<NotificationApiResponse<PushConfig>> => {
    return apiClient.post('/api/notification/push-configs', data);
  },

  // 更新推送配置
  updateConfig: async (id: string, data: Partial<PushConfig>): Promise<NotificationApiResponse<PushConfig>> => {
    return apiClient.put(`/api/notification/push-configs/${id}`, data);
  },

  // 删除推送配置
  deleteConfig: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.delete(`/api/notification/push-configs/${id}`);
  },

  // 启用/禁用推送配置
  toggleConfig: async (id: string, enabled: boolean): Promise<NotificationApiResponse> => {
    return apiClient.put(`/api/notification/push-configs/${id}/toggle`, { enabled });
  },
};

// 第三方平台服务
export const platformService = {
  // 获取平台配置列表
  getPlatforms: async (): Promise<NotificationApiResponse<PlatformConfig[]>> => {
    return apiClient.get('/api/notification/platforms');
  },

  // 获取平台配置详情
  getPlatformDetail: async (id: string): Promise<NotificationApiResponse<PlatformConfig>> => {
    return apiClient.get(`/api/notification/platforms/${id}`);
  },

  // 创建平台配置
  createPlatform: async (data: Partial<PlatformConfig>): Promise<NotificationApiResponse<PlatformConfig>> => {
    return apiClient.post('/api/notification/platforms', data);
  },

  // 更新平台配置
  updatePlatform: async (id: string, data: Partial<PlatformConfig>): Promise<NotificationApiResponse<PlatformConfig>> => {
    return apiClient.put(`/api/notification/platforms/${id}`, data);
  },

  // 删除平台配置
  deletePlatform: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.delete(`/api/notification/platforms/${id}`);
  },

  // 测试平台连接
  testConnection: async (id: string): Promise<NotificationApiResponse<{ status: string; message: string }>> => {
    return apiClient.post(`/api/notification/platforms/${id}/test`);
  },

  // 同步平台数据
  syncPlatform: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.post(`/api/notification/platforms/${id}/sync`);
  },

  // 刷新平台Token
  refreshToken: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.post(`/api/notification/platforms/${id}/refresh-token`);
  },

  // 获取平台消息
  getPlatformMessages: async (platformId: string, params?: {
    type?: string;
    startTime?: string;
    endTime?: string;
    page?: number;
    pageSize?: number;
  }): Promise<NotificationPaginatedResponse<Message>> => {
    return apiClient.get(`/api/notification/platforms/${platformId}/messages`, { params });
  },
};

// 用户偏好服务
export const preferenceService = {
  // 获取用户推送偏好
  getUserPreference: async (userId?: string): Promise<NotificationApiResponse<UserPushPreference>> => {
    return apiClient.get('/api/notification/preferences', { params: { userId } });
  },

  // 更新用户推送偏好
  updateUserPreference: async (data: Partial<UserPushPreference>): Promise<NotificationApiResponse<UserPushPreference>> => {
    return apiClient.put('/api/notification/preferences', data);
  },

  // 重置用户推送偏好
  resetUserPreference: async (userId?: string): Promise<NotificationApiResponse> => {
    return apiClient.post('/api/notification/preferences/reset', { userId });
  },
};

// 推送任务服务
export const pushTaskService = {
  // 获取推送任务列表
  getTasks: async (params?: {
    status?: string;
    type?: string;
    page?: number;
    pageSize?: number;
  }): Promise<NotificationPaginatedResponse<PushTask>> => {
    return apiClient.get('/api/notification/push-tasks', { params });
  },

  // 获取推送任务详情
  getTaskDetail: async (id: string): Promise<NotificationApiResponse<PushTask>> => {
    return apiClient.get(`/api/notification/push-tasks/${id}`);
  },

  // 创建推送任务
  createTask: async (data: Partial<PushTask>): Promise<NotificationApiResponse<PushTask>> => {
    return apiClient.post('/api/notification/push-tasks', data);
  },

  // 启动推送任务
  startTask: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.post(`/api/notification/push-tasks/${id}/start`);
  },

  // 停止推送任务
  stopTask: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.post(`/api/notification/push-tasks/${id}/stop`);
  },

  // 删除推送任务
  deleteTask: async (id: string): Promise<NotificationApiResponse> => {
    return apiClient.delete(`/api/notification/push-tasks/${id}`);
  },
};

// 统计分析服务
export const analyticsService = {
  // 获取消息统计数据
  getMessageStats: async (params?: {
    startTime?: string;
    endTime?: string;
    type?: string;
    platform?: string;
  }): Promise<NotificationApiResponse<MessageStats>> => {
    return apiClient.get('/api/notification/analytics/stats', { params });
  },

  // 获取趋势数据
  getTrendData: async (params?: {
    startTime?: string;
    endTime?: string;
    granularity?: 'hour' | 'day' | 'week' | 'month';
  }): Promise<NotificationApiResponse<Array<{
    date: string;
    sent: number;
    read: number;
    processed: number;
  }>>> => {
    return apiClient.get('/api/notification/analytics/trend', { params });
  },

  // 获取渠道效果分析
  getChannelAnalytics: async (params?: {
    startTime?: string;
    endTime?: string;
  }): Promise<NotificationApiResponse<Record<string, any>>> => {
    return apiClient.get('/api/notification/analytics/channels', { params });
  },

  // 获取平台效果分析
  getPlatformAnalytics: async (params?: {
    startTime?: string;
    endTime?: string;
  }): Promise<NotificationApiResponse<Record<string, any>>> => {
    return apiClient.get('/api/notification/analytics/platforms', { params });
  },

  // 导出统计报告
  exportReport: async (params?: {
    startTime?: string;
    endTime?: string;
    format?: 'excel' | 'pdf';
  }): Promise<Blob> => {
    return apiClient.get('/api/notification/analytics/export', {
      params,
      responseType: 'blob',
    });
  },
};

// 实时通知服务
export const realtimeService = {
  // 建立WebSocket连接
  connect: (onMessage: (data: any) => void, onError?: (error: any) => void) => {
    // WebSocket连接逻辑
    const ws = new WebSocket(`${process.env.WS_URL || 'ws://localhost:8001'}/notification/ws`);
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('WebSocket消息解析失败:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      onError?.(error);
    };

    return ws;
  },

  // 断开WebSocket连接
  disconnect: (ws: WebSocket) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.close();
    }
  },
};

// 导出所有服务
export default {
  message: messageService,
  template: templateService,
  pushConfig: pushConfigService,
  platform: platformService,
  preference: preferenceService,
  pushTask: pushTaskService,
  analytics: analyticsService,
  realtime: realtimeService,
};
