import { LoginParams, RegisterParams } from '@/services/auth/type';
import { handleResponse } from '@/utils/responseHandler';
import { post } from '../../utils/apiClient';

// 登录接口
export const login = async (params: LoginParams) => {
  // 默认账号直通
  if (
    params.username === 'frost-chain-admin' &&
    params.password === 'frost-chain-admin'
  ) {
    localStorage.setItem('token', 'default-token-for-frost-chain-admin');
    return { success: true };
  }

  return post('/api/auth/login', params).then(handleResponse);
};

// 注册接口
export const register = (params: RegisterParams) => {
  return post('/api/auth/register', params).then(handleResponse);
};

// 登出接口
export const logout = () => {
  localStorage.removeItem('token');
};
