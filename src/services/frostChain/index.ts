/* eslint-disable */
// Frost-Chain 监控数据服务

import { get } from '@/utils/apiClient';

// 定义监控数据类型
export interface MonitoringDataItem {
  id: string;
  timestamp: number;
  eventType: string;
  eventName: string;
  eventData: Record<string, any>;
  deviceInfo: {
    deviceId: string;
    deviceType: string;
    osVersion: string;
    browser?: string;
    browserVersion?: string;
  };
  userInfo?: {
    userId?: string;
    sessionId: string;
  };
  location?: {
    page: string;
    url: string;
    referrer?: string;
  };
}

export interface MonitoringDataList {
  list: MonitoringDataItem[];
  total: number;
  current: number;
  pageSize: number;
}

export interface MonitoringStatistics {
  totalEvents: number;
  eventTypeDistribution: Record<string, number>;
  topPages: Array<{ page: string; count: number }>;
  timeDistribution: Array<{ hour: number; count: number }>;
  errorCount: number;
}

import { handleResponse } from '@/utils/responseHandler';

/**
 * 获取监控数据列表
 */
export async function getMonitoringData(
  params: {
    current?: number;
    pageSize?: number;
    startTime?: number;
    endTime?: number;
    eventType?: string;
    eventName?: string;
    deviceType?: string;
    userId?: string;
    page?: string;
  },
  options?: { [key: string]: any },
): Promise<MonitoringDataList> {
  return get('/api/frost-chain/monitoring-data', {
    ...params,
    ...(options || {}),
  }).then(handleResponse<MonitoringDataList>);
}

/**
 * 获取监控数据统计信息
 */
export async function getMonitoringStatistics(
  params: {
    startTime?: number;
    endTime?: number;
    eventType?: string;
    deviceType?: string;
  },
  options?: { [key: string]: any },
): Promise<MonitoringStatistics> {
  return get('/api/frost-chain/monitoring-statistics', {
    ...params,
    ...(options || {}),
  }).then(handleResponse<MonitoringStatistics>);
}

export default {
  getMonitoringData,
  getMonitoringStatistics,
};
