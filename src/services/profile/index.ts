import { apiClient } from '@/utils/apiClient';
import type {
  UserProfile,
  CompanyInfo,
  RolePermission,
  LoginDevice,
  OperationLog,
  TodoItem,
  MyContent,
  MyCustomer,
  TeamMember,
  PersonalStats,
  PersonalSettings,
  SecuritySettings,
  Achievement,
  QuickAction,
  WorkspaceWidget,
  ProfileApiResponse,
  ProfilePaginatedResponse,
  ProfileUpdateData,
  PasswordChangeData,
  MFASetupData,
  ProfileSearchParams,
} from '@/types/profile';

// 用户基本信息服务
export const userProfileService = {
  // 获取用户基本信息
  getUserProfile: async (): Promise<ProfileApiResponse<UserProfile>> => {
    return apiClient.get('/api/profile/user');
  },

  // 更新用户基本信息
  updateUserProfile: async (data: ProfileUpdateData): Promise<ProfileApiResponse<UserProfile>> => {
    return apiClient.put('/api/profile/user', data);
  },

  // 上传头像
  uploadAvatar: async (file: File): Promise<ProfileApiResponse<{ url: string }>> => {
    const formData = new FormData();
    formData.append('avatar', file);
    return apiClient.post('/api/profile/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },

  // 获取企业信息
  getCompanyInfo: async (): Promise<ProfileApiResponse<CompanyInfo>> => {
    return apiClient.get('/api/profile/company');
  },

  // 更新企业信息
  updateCompanyInfo: async (data: Partial<CompanyInfo>): Promise<ProfileApiResponse<CompanyInfo>> => {
    return apiClient.put('/api/profile/company', data);
  },
};

// 安全中心服务
export const securityService = {
  // 修改密码
  changePassword: async (data: PasswordChangeData): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/security/password', data);
  },

  // 绑定手机号
  bindPhone: async (phone: string, code: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/security/bind-phone', { phone, code });
  },

  // 绑定邮箱
  bindEmail: async (email: string, code: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/security/bind-email', { email, code });
  },

  // 发送验证码
  sendVerificationCode: async (type: 'phone' | 'email', target: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/security/send-code', { type, target });
  },

  // 获取登录设备列表
  getLoginDevices: async (): Promise<ProfileApiResponse<LoginDevice[]>> => {
    return apiClient.get('/api/profile/security/devices');
  },

  // 移除登录设备
  removeDevice: async (deviceId: string): Promise<ProfileApiResponse> => {
    return apiClient.delete(`/api/profile/security/devices/${deviceId}`);
  },

  // 获取操作日志
  getOperationLogs: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<OperationLog>> => {
    return apiClient.get('/api/profile/security/logs', { params });
  },

  // 设置MFA
  setupMFA: async (data: MFASetupData): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/security/mfa', data);
  },

  // 禁用MFA
  disableMFA: async (code: string): Promise<ProfileApiResponse> => {
    return apiClient.delete('/api/profile/security/mfa', { data: { code } });
  },

  // 获取安全设置
  getSecuritySettings: async (): Promise<ProfileApiResponse<SecuritySettings>> => {
    return apiClient.get('/api/profile/security/settings');
  },

  // 更新安全设置
  updateSecuritySettings: async (data: Partial<SecuritySettings>): Promise<ProfileApiResponse> => {
    return apiClient.put('/api/profile/security/settings', data);
  },
};

// 权限管理服务
export const permissionService = {
  // 获取用户角色权限
  getUserPermissions: async (): Promise<ProfileApiResponse<RolePermission[]>> => {
    return apiClient.get('/api/profile/permissions');
  },

  // 申请权限
  requestPermission: async (permissionId: string, reason: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/permissions/request', { permissionId, reason });
  },

  // 获取权限申请历史
  getPermissionRequests: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<any>> => {
    return apiClient.get('/api/profile/permissions/requests', { params });
  },
};

// 工作台服务
export const workspaceService = {
  // 获取待办事项
  getTodoList: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<TodoItem>> => {
    return apiClient.get('/api/profile/workspace/todos', { params });
  },

  // 更新待办状态
  updateTodoStatus: async (todoId: string, status: TodoItem['status']): Promise<ProfileApiResponse> => {
    return apiClient.put(`/api/profile/workspace/todos/${todoId}`, { status });
  },

  // 获取个人统计数据
  getPersonalStats: async (timeRange?: string): Promise<ProfileApiResponse<PersonalStats>> => {
    return apiClient.get('/api/profile/workspace/stats', { params: { timeRange } });
  },

  // 获取快捷操作
  getQuickActions: async (): Promise<ProfileApiResponse<QuickAction[]>> => {
    return apiClient.get('/api/profile/workspace/quick-actions');
  },

  // 更新快捷操作
  updateQuickActions: async (actions: QuickAction[]): Promise<ProfileApiResponse> => {
    return apiClient.put('/api/profile/workspace/quick-actions', { actions });
  },

  // 获取工作台小部件
  getWorkspaceWidgets: async (): Promise<ProfileApiResponse<WorkspaceWidget[]>> => {
    return apiClient.get('/api/profile/workspace/widgets');
  },

  // 更新工作台布局
  updateWorkspaceLayout: async (widgets: WorkspaceWidget[]): Promise<ProfileApiResponse> => {
    return apiClient.put('/api/profile/workspace/layout', { widgets });
  },
};

// 我的内容服务
export const myContentService = {
  // 获取我的内容列表
  getMyContent: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<MyContent>> => {
    return apiClient.get('/api/profile/content', { params });
  },

  // 获取内容详情
  getContentDetail: async (contentId: string): Promise<ProfileApiResponse<MyContent>> => {
    return apiClient.get(`/api/profile/content/${contentId}`);
  },

  // 删除内容
  deleteContent: async (contentId: string): Promise<ProfileApiResponse> => {
    return apiClient.delete(`/api/profile/content/${contentId}`);
  },

  // 批量操作内容
  batchOperateContent: async (contentIds: string[], operation: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/content/batch', { contentIds, operation });
  },
};

// 我的客户服务
export const myCustomerService = {
  // 获取我的客户列表
  getMyCustomers: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<MyCustomer>> => {
    return apiClient.get('/api/profile/customers', { params });
  },

  // 获取客户详情
  getCustomerDetail: async (customerId: string): Promise<ProfileApiResponse<MyCustomer>> => {
    return apiClient.get(`/api/profile/customers/${customerId}`);
  },

  // 更新客户信息
  updateCustomer: async (customerId: string, data: Partial<MyCustomer>): Promise<ProfileApiResponse> => {
    return apiClient.put(`/api/profile/customers/${customerId}`, data);
  },

  // 添加跟进记录
  addFollowRecord: async (customerId: string, content: string): Promise<ProfileApiResponse> => {
    return apiClient.post(`/api/profile/customers/${customerId}/follow`, { content });
  },
};

// 团队协作服务
export const teamService = {
  // 获取团队成员
  getTeamMembers: async (): Promise<ProfileApiResponse<TeamMember[]>> => {
    return apiClient.get('/api/profile/team/members');
  },

  // 获取团队统计
  getTeamStats: async (): Promise<ProfileApiResponse<any>> => {
    return apiClient.get('/api/profile/team/stats');
  },

  // 发起协作
  initiateCollaboration: async (memberIds: string[], type: string, content: string): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/team/collaborate', { memberIds, type, content });
  },
};

// 个人设置服务
export const settingsService = {
  // 获取个人设置
  getPersonalSettings: async (): Promise<ProfileApiResponse<PersonalSettings>> => {
    return apiClient.get('/api/profile/settings');
  },

  // 更新个人设置
  updatePersonalSettings: async (data: Partial<PersonalSettings>): Promise<ProfileApiResponse> => {
    return apiClient.put('/api/profile/settings', data);
  },

  // 重置设置
  resetSettings: async (): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/settings/reset');
  },

  // 导出个人数据
  exportPersonalData: async (format: 'json' | 'excel'): Promise<Blob> => {
    return apiClient.get('/api/profile/export', {
      params: { format },
      responseType: 'blob',
    });
  },
};

// 成就系统服务
export const achievementService = {
  // 获取个人成就
  getAchievements: async (): Promise<ProfileApiResponse<Achievement[]>> => {
    return apiClient.get('/api/profile/achievements');
  },

  // 领取成就奖励
  claimAchievement: async (achievementId: string): Promise<ProfileApiResponse> => {
    return apiClient.post(`/api/profile/achievements/${achievementId}/claim`);
  },
};

// 反馈服务
export const feedbackService = {
  // 提交反馈
  submitFeedback: async (data: {
    type: 'bug' | 'feature' | 'improvement' | 'other';
    title: string;
    content: string;
    attachments?: string[];
  }): Promise<ProfileApiResponse> => {
    return apiClient.post('/api/profile/feedback', data);
  },

  // 获取反馈历史
  getFeedbackHistory: async (params?: ProfileSearchParams): Promise<ProfilePaginatedResponse<any>> => {
    return apiClient.get('/api/profile/feedback', { params });
  },
};

// 导出所有服务
export default {
  userProfile: userProfileService,
  security: securityService,
  permission: permissionService,
  workspace: workspaceService,
  myContent: myContentService,
  myCustomer: myCustomerService,
  team: teamService,
  settings: settingsService,
  achievement: achievementService,
  feedback: feedbackService,
};
