import { Permission, Role, User } from '@/services/permissionService/type';
import { ApiResponse } from '@/types/api';
import { del, get, post, put } from '@/utils/apiClient';

export async function queryUsers(params: any): Promise<ApiResponse<{
  data: User[];
  total: number;
}> | null> {
  const response = await get<ApiResponse<{
    data: User[];
    total: number;
  }> | null>('/api/permission/users', params);
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function addUser(params: {
  username: string;
  password: string;
}): Promise<ApiResponse<{ username: string; password: string }> | null> {
  const response = await post<ApiResponse<{
    username: string;
    password: string;
  }> | null>('/api/permission/users', params);
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function updateUser(
  user: User,
): Promise<ApiResponse<User> | null> {
  const response = await put<ApiResponse<User> | null>(
    `/api/permission/users/${user.id}`,
    user,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function deleteUser(
  id: string,
): Promise<ApiResponse<void> | null> {
  const response = await del<ApiResponse<void> | null>(
    `/api/permission/users/${id}`,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function queryRoles(params: any): Promise<ApiResponse<{
  data: Role[];
  total: number;
}> | null> {
  const response = await get<ApiResponse<{
    data: Role[];
    total: number;
  }> | null>('/api/permission/roles', params);
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function addRole(
  role: Omit<Role, 'id'>,
): Promise<ApiResponse<Role> | null> {
  const response = await post<ApiResponse<Role> | null>(
    '/api/permission/roles',
    role,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function updateRole(
  role: Role,
): Promise<ApiResponse<Role> | null> {
  const response = await put<ApiResponse<Role> | null>(
    `/api/permission/roles/${role.id}`,
    role,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function deleteRole(
  id: string,
): Promise<ApiResponse<void> | null> {
  const response = await del<ApiResponse<void> | null>(
    `/api/permission/roles/${id}`,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function queryPermissions(params: any): Promise<ApiResponse<{
  data: Permission[];
  total: number;
}> | null> {
  const response = await get<ApiResponse<{
    data: Permission[];
    total: number;
  }> | null>('/api/permission/permissions', params);
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function addPermission(
  permission: Omit<Permission, 'id'>,
): Promise<ApiResponse<Permission> | null> {
  const response = await post<ApiResponse<Permission> | null>(
    '/api/permission/permissions',
    permission,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function updatePermission(
  permission: Permission,
): Promise<ApiResponse<Permission> | null> {
  const response = await put<ApiResponse<Permission> | null>(
    `/api/permission/permissions/${permission.id}`,
    permission,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}

export async function deletePermission(
  id: string,
): Promise<ApiResponse<void> | null> {
  const response = await del<ApiResponse<void> | null>(
    `/api/permission/permissions/${id}`,
  );
  if (!response.data) {
    return null;
  }
  if (response.status === 200) {
    return response.data;
  }
  throw new Error(`API Error: ${response.status}`);
}
