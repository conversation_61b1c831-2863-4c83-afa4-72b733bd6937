export interface Content {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  // 其他内容字段...
}

export interface ContentFormData {
  title: string;
  description: string;
  content: string;
  tags: string[];
  // 其他表单字段...
}

export interface ListParams {
  page?: number;
  pageSize?: number;
  sort?: string;
  filter?: Record<string, any>;
}

export interface Statistics {
  total: number;
  published: number;
  draft: number;
  archived: number;
}

export interface ChartData {
  name: string;
  value: number;
}

export interface PlatformStatsParams {
  platform: string;
  timeRange: string;
}

export interface PublishResult {
  platform: string;
  success: boolean;
  url?: string;
}

export interface MediaFile {
  id: string;
  url: string;
  type: 'image' | 'video';
  size: number;
}

export interface ContentDetailParams {
  id: string;
  includeStats?: boolean;
  includePlatforms?: boolean;
}

export interface ContentDetailResponse extends Content {
  stats?: Record<string, number>;
  platforms?: Array<{
    name: string;
    published: boolean;
    url?: string;
  }>;
}
