import { ApiResponse } from '@/types/api';
import { del, get, post, put } from '@/utils/apiClient';
import type { Content, ContentFormData, ListParams } from './types';

const BASE_URL = '/api/content';

// 获取内容列表
export async function getContentList(
  params: ListParams,
): Promise<ApiResponse<Content[]>> {
  return get(`${BASE_URL}/list`, params);
}

// 创建内容
export async function createContent(
  data: ContentFormData,
): Promise<ApiResponse<Content>> {
  return post(`${BASE_URL}`, data);
}

// 更新内容
export async function updateContent(
  id: string,
  data: Partial<ContentFormData>,
): Promise<ApiResponse<Content>> {
  return put(`${BASE_URL}/${id}`, data);
}

// 删除内容
export async function deleteContent(id: string): Promise<ApiResponse<void>> {
  return del(`${BASE_URL}/${id}`);
}

// 批量删除内容
export async function batchDeleteContent(
  ids: string[],
): Promise<ApiResponse<void>> {
  return del(`${BASE_URL}/batch`, { ids });
}
