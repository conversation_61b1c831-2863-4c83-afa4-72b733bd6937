import logger from '@/utils/logger';
import React, { useEffect } from 'react';

interface LogOperationProps {
  module: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'READ';
  resourceType: string;
  resourceId?: string;
  description: string;
  details?: Record<string, any>;
  children: React.ReactNode;
}

/**
 * 操作日志记录组件
 * 用于手动记录用户操作，如页面访问、按钮点击等
 *
 * 使用示例:
 * <LogOperation
 *   module="用户管理"
 *   action="READ"
 *   resourceType="user_page"
 *   description="访问用户管理页面"
 * >
 *   <Button>点击记录日志</Button>
 * </LogOperation>
 */
const LogOperation: React.FC<LogOperationProps> = ({
  module,
  action,
  resourceType,
  resourceId,
  description,
  details,
  children,
}) => {
  useEffect(() => {
    // 组件挂载时记录日志
    logger.log({
      module,
      action,
      resourceType,
      resourceId,
      description,
      details,
    });
  }, []);

  return <>{children}</>;
};

/**
 * 高阶组件，用于包装需要记录操作日志的组件
 */
export const withLogOperation = (
  WrappedComponent: React.ComponentType<any>,
  logProps: Omit<LogOperationProps, 'children'>,
) => {
  return (props: any) => (
    <LogOperation {...logProps}>
      <WrappedComponent {...props} />
    </LogOperation>
  );
};

export default LogOperation;
