import config from '@/config';
import { envConfig } from '@/utils/env';
import { Card, Descriptions, Tag, Typography } from 'antd';
import React from 'react';

const { Title } = Typography;

/**
 * 环境信息组件
 * 用于展示当前环境配置信息，仅在开发和测试环境显示
 */
const EnvInfo: React.FC = () => {
  // 生产环境不显示
  if (envConfig.isProd) {
    return null;
  }

  // 获取环境标签颜色
  const getEnvColor = () => {
    if (envConfig.isProd) return 'green';
    if (envConfig.isTest) return 'orange';
    return 'blue';
  };

  return (
    <Card style={{ marginBottom: 16 }}>
      <Title level={4}>当前环境配置</Title>
      <Descriptions bordered size="small">
        <Descriptions.Item label="环境" span={3}>
          <Tag color={getEnvColor()}>{envConfig.env.toUpperCase()}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="API基础URL" span={3}>
          {config.api.baseUrl}
        </Descriptions.Item>
        <Descriptions.Item label="Mock状态" span={3}>
          <Tag color={config.api.mockEnabled ? 'green' : 'red'}>
            {config.api.mockEnabled ? '启用' : '禁用'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="调试模式" span={3}>
          <Tag color={config.debug.enabled ? 'green' : 'red'}>
            {config.debug.enabled ? '启用' : '禁用'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="日志级别" span={3}>
          {config.debug.logLevel}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default EnvInfo;
