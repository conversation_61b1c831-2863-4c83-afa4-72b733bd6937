import { getUserInfo, logout } from '@/utils/auth';
import { Avatar, Dropdown } from 'antd';
import { useNavigate } from 'umi';
import styles from './index.less';

export default function Header() {
  const navigate = useNavigate();
  const userInfo = getUserInfo();

  // 处理登出
  const handleLogout = () => {
    logout(); // 使用统一的登出函数
    console.log('用户已登出，跳转到登录页');
    navigate('/login');
  };

  return (
    <div className={styles.header}>
      <div className={styles.logo}>冰品管理系统</div>
      <div className={styles.right}>
        <Dropdown
          menu={{
            items: [
              {
                key: 'profile',
                label: '个人信息',
                onClick: () => navigate('/profile'),
              },
              {
                key: 'logout',
                label: '退出登录',
                onClick: handleLogout,
              },
            ],
          }}
          placement="bottomRight"
        >
          <div className={styles.profileBtn}>
            <Avatar />
            <span style={{ marginLeft: 8 }}>
              {userInfo?.name || userInfo?.username || '用户'}
            </span>
          </div>
        </Dropdown>
      </div>
    </div>
  );
}
