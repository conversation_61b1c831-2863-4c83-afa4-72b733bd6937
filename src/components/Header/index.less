.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 8%);
  position: relative;
  z-index: 9;

  .logo {
    font-size: 20px;
    font-weight: 600;
    color: #1890ff;
  }

  .right {
    display: flex;
    align-items: center;
  }

  .profileBtn {
    display: flex;
    align-items: center;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 2.5%);
    }
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 12px;

    .logo {
      font-size: 16px;
    }
  }
}
