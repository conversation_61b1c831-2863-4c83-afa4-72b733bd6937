// Token 存储的 key
const TOKEN_KEY = 'front_logix_token';
const USER_INFO_KEY = 'userInfo';

// 支持的 token 存储 key 列表（兼容性）
const TOKEN_KEYS = [TOKEN_KEY, 'token', 'auth_token'];

/**
 * 获取 token
 * 支持多种 token 存储方式的兼容性
 */
export function getToken(): string | null {
  for (const key of TOKEN_KEYS) {
    const token = localStorage.getItem(key);
    if (token && token.trim() !== '') {
      return token;
    }
  }
  return null;
}

/**
 * 设置 token
 */
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
}

/**
 * 移除 token
 * 清除所有可能的 token 存储
 */
export function removeToken(): void {
  TOKEN_KEYS.forEach((key) => {
    localStorage.removeItem(key);
  });
}

/**
 * 检查是否有 token
 */
export function hasToken(): boolean {
  return !!getToken();
}

/**
 * 获取用户信息
 */
export function getUserInfo(): any | null {
  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY);
    if (userInfoStr) {
      return JSON.parse(userInfoStr);
    }
  } catch (error) {
    console.error('解析用户信息失败:', error);
  }
  return null;
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo: any): void {
  try {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
}

/**
 * 移除用户信息
 */
export function removeUserInfo(): void {
  localStorage.removeItem(USER_INFO_KEY);
}

/**
 * 检查用户是否已登录
 * 同时检查 token 和用户信息的有效性
 */
export function isLoggedIn(): boolean {
  const token = getToken();
  const userInfo = getUserInfo();

  // 必须同时有 token 和有效的用户信息
  const hasValidToken = !!(token && token.trim() !== '');
  const hasValidUserInfo = !!(
    userInfo &&
    (userInfo.name || userInfo.id || userInfo.username)
  );

  return hasValidToken && hasValidUserInfo;
}

/**
 * 完整登出
 * 清除所有认证相关的本地存储
 */
export function logout(): void {
  removeToken();
  removeUserInfo();

  // 清除其他可能的认证相关存储
  const keysToRemove = ['access_token', 'refresh_token', 'user_permissions'];
  keysToRemove.forEach((key) => {
    localStorage.removeItem(key);
  });
}

/**
 * 获取用户角色
 */
export function getUserRoles(): string[] {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.roles && Array.isArray(userInfo.roles)) {
    return userInfo.roles;
  }
  return [];
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(role: string): boolean {
  const roles = getUserRoles();
  return roles.includes(role);
}

/**
 * 检查用户是否是管理员
 */
export function isAdmin(): boolean {
  const userInfo = getUserInfo();
  if (!userInfo) return false;

  return !!(
    userInfo.isAdmin ||
    userInfo.userType === 'admin' ||
    hasRole('admin')
  );
}
