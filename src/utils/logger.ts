import { post } from '@/utils/apiClient';

export interface LogParams {
  module: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'READ';
  resourceId?: string;
  resourceType: string;
  description: string;
  details?: Record<string, any>;
}

class OperationLogger {
  private static instance: OperationLogger;

  private constructor() {}

  public static getInstance(): OperationLogger {
    if (!OperationLogger.instance) {
      OperationLogger.instance = new OperationLogger();
    }
    return OperationLogger.instance;
  }

  /**
   * 记录创建操作
   */
  async logCreate(params: Omit<LogParams, 'action'>) {
    return this.log({
      ...params,
      action: 'CREATE',
    });
  }

  /**
   * 记录更新操作
   */
  async logUpdate(params: Omit<LogParams, 'action'>) {
    return this.log({
      ...params,
      action: 'UPDATE',
    });
  }

  /**
   * 记录删除操作
   */
  async logDelete(params: Omit<LogParams, 'action'>) {
    return this.log({
      ...params,
      action: 'DELETE',
    });
  }

  /**
   * 记录查询操作
   */
  async logRead(params: Omit<LogParams, 'action'>) {
    return this.log({
      ...params,
      action: 'READ',
    });
  }

  /**
   * 记录操作日志
   */
  async log(params: LogParams): Promise<void> {
    try {
      await post('/api/operation-logs', params);
    } catch (error) {
      console.error('Failed to log operation:', error);
    }
  }
}

// 创建日志记录器实例
const logger = OperationLogger.getInstance();

// 使用示例：
// import logger from '@/utils/logger';
//
// // 记录创建操作
// await logger.logCreate({
//   module: '用户管理',
//   resourceType: 'user',
//   resourceId: '123',
//   description: '创建新用户',
//   details: { name: 'John Doe', email: '<EMAIL>' }
// });
//
// // 记录更新操作
// await logger.logUpdate({
//   module: '角色管理',
//   resourceType: 'role',
//   resourceId: '456',
//   description: '更新角色权限',
//   details: { permissions: ['read', 'write'] }
// });

export default logger;
