/**
 * API 客户端
 * 基于 axios 封装
 */
import { ApiResponse } from '@/types/api';
import { notification } from 'antd';
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosRequestHeaders,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { envConfig } from './env';

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: envConfig.apiBaseUrl,
  timeout: envConfig.apiTimeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 类型安全的请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    try {
      // 获取 token
      const token = localStorage.getItem('front_logix_token');

      // 如果有 token，添加到请求头
      if (token) {
        config.headers = {
          ...(config.headers as Record<string, unknown>),
          Authorization: `Bearer ${token}`,
        } as AxiosRequestHeaders;
      }

      // 开发环境打印详细请求日志
      if (envConfig.isDev) {
        console.log('[API Request]', {
          url: config.url,
          method: config.method,
          params: config.params,
          headers: config.headers,
          data: config.data,
        });
      }

      return config;
    } catch (error) {
      console.error('[Request Interceptor Error]', error);
      return Promise.reject(new Error('请求配置处理失败'));
    }
  },
  (error) => {
    console.error('[Request Interceptor Outer Error]', error);
    return Promise.reject(error);
  },
);

// 类型安全的响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse, InternalAxiosRequestConfig>) => {
    const { data, config } = response;

    // 开发环境打印详细响应日志
    if (envConfig.isDev) {
      console.log('[API Response]', {
        url: config.url,
        status: response.status,
        data: data,
        headers: response.headers,
      });
    }

    // 记录操作日志（避免循环调用）
    if (config.url && !config.url.includes('/operation-logs')) {
      try {
        // 延迟导入 logger 以避免循环依赖
        import('./logger').then(({ default: logger }) => {
          // 解析URL以获取资源类型
          const urlParts = config.url?.split('/').filter(Boolean) || [];
          const resourceType =
            urlParts[urlParts.length - 1]?.toLowerCase() || 'unknown';

          // 确定操作类型
          let action: 'CREATE' | 'UPDATE' | 'DELETE' | 'READ';
          switch (config.method?.toUpperCase()) {
            case 'POST':
              action = 'CREATE';
              break;
            case 'PUT':
            case 'PATCH':
              action = 'UPDATE';
              break;
            case 'DELETE':
              action = 'DELETE';
              break;
            default:
              action = 'READ';
          }

          // 安全地获取资源ID
          const resourceId = (() => {
            try {
              // 尝试解析 config.data（如果是字符串的话）
              const requestData =
                typeof config.data === 'string'
                  ? JSON.parse(config.data)
                  : config.data;

              // 检查响应数据
              const responseData = data?.data;

              // 返回第一个找到的有效ID
              return requestData?.id || responseData?.id || undefined;
            } catch (e) {
              console.debug('Failed to parse resource ID:', e);
              return undefined;
            }
          })();

          // 记录日志
          logger.log({
            module: resourceType,
            action,
            resourceType,
            resourceId,
            description: `${action} ${resourceType}`,
            details: {
              request:
                typeof config.data === 'object' ? config.data : undefined,
              response: data,
              url: config.url,
              method: config.method,
            },
          });
        });
      } catch (error) {
        console.error('Failed to log operation:', error);
      }
    }

    // 处理业务逻辑错误
    if (data && data.status !== 200 && data.status !== 0) {
      const errorMsg = data.message || '业务处理失败';
      notification.error({
        message: `业务错误 (${data.status})`,
        description: errorMsg,
      });
      return Promise.reject(new Error(errorMsg));
    }

    return response;
  },
  (error) => {
    // 网络错误处理
    if (!error.response) {
      notification.error({
        message: '网络连接异常',
        description: '无法连接到服务器，请检查网络设置',
      });
      return Promise.reject(new Error('网络连接失败'));
    }

    const { response } = error;
    const { status, data, config } = response;
    const url = config?.url || '未知接口';
    const errorMsg = data?.message || error.message || '请求处理失败';

    // 根据状态码分类处理
    switch (status) {
      case 401:
        notification.warning({
          message: '登录已过期',
          description: '请重新登录',
        });
        localStorage.removeItem('front_logix_token');
        window.location.href = '/login';
        break;
      case 403:
        notification.error({
          message: '权限不足',
          description: '您没有权限访问该资源',
        });
        break;
      case 404:
        notification.error({
          message: '资源不存在',
          description: `请求的接口不存在: ${url}`,
        });
        break;
      case 500:
        notification.error({
          message: '服务器错误',
          description: '服务器内部错误，请联系管理员',
        });
        break;
      default:
        notification.error({
          message: `请求错误 (${status})`,
          description: errorMsg,
        });
    }

    // 开发环境打印错误详情
    if (envConfig.isDev) {
      console.error('[API Error]', {
        url,
        status,
        error: errorMsg,
        config,
        stack: error.stack,
      });
    }

    return Promise.reject(errorMsg);
  },
);

// 封装 GET 请求
export const get = <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> => {
  return apiClient.get(url, { params, ...config }).then((res) => res.data);
};

// 封装 POST 请求
export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> => {
  return apiClient.post(url, data, config).then((res) => res.data);
};

// 封装 PUT 请求
export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> => {
  return apiClient.put(url, data, config).then((res) => res.data);
};

// 封装 DELETE 请求
export const del = <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig,
): Promise<ApiResponse<T>> => {
  return apiClient.delete(url, { params, ...config }).then((res) => res.data);
};

// 导出 axios 实例
export default apiClient;
