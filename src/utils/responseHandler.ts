/**
 * 统一响应处理工具
 */

import { ApiResponse } from '@/types/api';

/**
 * 处理API响应
 * @template T 期望的响应数据类型
 * @param response API响应
 * @returns 完整的ApiResponse结构
 * @throws 当响应code不为200时抛出错误
 */
export function handleResponse<T>(response: any): ApiResponse<T> {
  if (response?.code === 200) {
    return response;
  }
  throw new Error(response?.message || '请求失败');
}

/**
 * 处理Blob类型响应
 * @param response API响应
 * @returns 完整的ApiResponse结构
 * @throws 当响应code不为200时抛出错误
 */
export function handleBlobResponse(response: any): ApiResponse<Blob> {
  if (response?.code === 200) {
    return response;
  }
  throw new Error(response?.message || '请求失败');
}
