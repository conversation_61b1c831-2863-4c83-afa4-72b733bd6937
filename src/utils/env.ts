/**
 * 环境配置
 */

// 判断当前环境
const isDev = process.env.NODE_ENV === 'development';

// 环境配置
export const envConfig = {
  // 是否为开发环境
  isDev,

  // API 基础路径
  apiBaseUrl: isDev
    ? 'http://localhost:8000'
    : process.env.API_BASE_URL || '/api',

  // API 超时时间（毫秒）
  apiTimeout: 10000,

  // 是否启用调试日志
  enableDebugLog: isDev,

  // 是否启用 API 请求日志
  enableApiLog: isDev,

  // 是否启用操作日志记录
  enableOperationLog: true,
};

export default envConfig;
