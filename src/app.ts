/**
 * 应用运行时配置
 * 统一使用 axios 作为 HTTP 客户端
 */
import { getToken } from '@/utils/auth';
import { RequestConfig } from '@umijs/max';
import { message } from 'antd';

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}

// 与后端约定的响应数据格式
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
  showType?: ErrorShowType;
}

/**
 * UmiJS 请求配置
 * 注意：这个配置主要用于 UmiJS 内置的请求功能
 * 项目中的业务请求建议使用 @/utils/apiClient.ts 中的 axios 实例
 */
export const request: RequestConfig = {
  // 统一的请求设置
  timeout: 10000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },

  // 请求拦截器
  requestInterceptors: [
    (config: any) => {
      // 拦截请求配置，进行个性化处理
      const token = getToken();
      const url = config?.url?.toString();
      if (token && url?.indexOf('/api') !== -1) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }
      return config;
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response as unknown as ResponseStructure;

      if (!data.success) {
        message.error(data.errorMessage);
      }
      return response;
    },
  ],
};
