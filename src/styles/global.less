@import '~antd/dist/antd.less';

// 基础变量
@primary-color: #1890ff;
@text-color: rgba(0, 0, 0, 0.85);
@border-radius-base: 4px;

// 字体
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
  'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: @font-family;
  color: @text-color;
  background-color: #f0f2f5;
}

#root {
  min-height: 100%;
}

// 常用工具类
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

// 响应式断点
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}
