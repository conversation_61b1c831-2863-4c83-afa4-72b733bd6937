import { PageContainer } from '@ant-design/pro-components';
import { Card, Tabs } from 'antd';
import React from 'react';
import PermissionManagement from './components/PermissionManagement';
import RoleManagement from './components/RoleManagement';
import UserManagement from './components/UserManagement';

const PermissionPage: React.FC = () => {
  return (
    <PageContainer title="权限管理">
      <Card>
        <Tabs
          defaultActiveKey="1"
          items={[
            {
              key: '1',
              label: '用户管理',
              children: <UserManagement />,
            },
            {
              key: '2',
              label: '角色管理',
              children: <RoleManagement />,
            },
            {
              key: '3',
              label: '权限管理',
              children: <PermissionManagement />,
            },
          ]}
        />
      </Card>
    </PageContainer>
  );
};

export default PermissionPage;
