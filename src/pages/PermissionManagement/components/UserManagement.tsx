import {
  addUser,
  deleteUser,
  queryUsers,
  updateUser,
} from '@/services/permissionService';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import UserForm from './UserForm';

interface UserItem {
  id: string;
  username: string;
  email: string;
  roles: string[];
  status: 'active' | 'disabled';
}

const UserManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserItem | undefined>(
    undefined,
  );

  const handleAdd = () => {
    setCurrentUser(undefined);
    setVisible(true);
  };

  const handleEdit = (record: UserItem) => {
    setCurrentUser(record);
    setVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除用户',
      content: '确定要删除这个用户吗？',
      onOk: async () => {
        await deleteUser(id);
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<UserItem>[] = [
    {
      title: '用户名',
      dataIndex: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
    {
      title: '角色',
      dataIndex: 'roles',
      render: (_, record) => record.roles.join(', '),
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        active: { text: '活跃', status: 'Success' },
        disabled: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <a key="delete" onClick={() => handleDelete(record.id)}>
          删除
        </a>,
      ],
    },
  ];

  return (
    <>
      <ProTable<UserItem>
        columns={columns}
        actionRef={actionRef}
        request={async (params) => {
          const response = await queryUsers(params);
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={handleAdd}
          >
            新建
          </Button>,
        ]}
      />
      <UserForm
        visible={visible}
        onCancel={() => setVisible(false)}
        onFinish={async (values) => {
          if (currentUser) {
            await updateUser({ ...values, id: currentUser.id });
            message.success('更新成功');
          } else {
            await addUser(values);
            message.success('添加成功');
          }
          setVisible(false);
          actionRef.current?.reload();
        }}
        initialValues={currentUser}
      />
    </>
  );
};

export default UserManagement;
