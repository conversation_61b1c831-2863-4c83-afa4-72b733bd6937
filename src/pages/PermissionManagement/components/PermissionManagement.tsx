import {
  addPermission,
  deletePermission,
  queryPermissions,
  updatePermission,
} from '@/services/permissionService';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import PermissionForm from './PermissionForm';

interface PermissionItem {
  id: string;
  name: string;
  code: string;
  description: string;
}

const PermissionManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState(false);
  const [currentPermission, setCurrentPermission] = useState<
    PermissionItem | undefined
  >(undefined);

  const handleAdd = () => {
    setCurrentPermission(undefined);
    setVisible(true);
  };

  const handleEdit = (record: PermissionItem) => {
    setCurrentPermission(record);
    setVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除权限',
      content: '确定要删除这个权限吗？',
      onOk: async () => {
        await deletePermission(id);
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<PermissionItem>[] = [
    {
      title: '权限名称',
      dataIndex: 'name',
    },
    {
      title: '权限编码',
      dataIndex: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <a key="delete" onClick={() => handleDelete(record.id)}>
          删除
        </a>,
      ],
    },
  ];

  return (
    <>
      <ProTable<PermissionItem>
        columns={columns}
        actionRef={actionRef}
        request={async (params) => {
          const response = await queryPermissions(params);
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={handleAdd}
          >
            新建
          </Button>,
        ]}
      />
      <PermissionForm
        visible={visible}
        onCancel={() => setVisible(false)}
        onFinish={async (values) => {
          if (currentPermission) {
            await updatePermission({ ...values, id: currentPermission.id });
            message.success('更新成功');
          } else {
            await addPermission(values);
            message.success('添加成功');
          }
          setVisible(false);
          actionRef.current?.reload();
        }}
        initialValues={currentPermission}
      />
    </>
  );
};

export default PermissionManagement;
