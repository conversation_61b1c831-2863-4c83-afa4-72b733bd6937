import { Form, Input, Modal, Select } from 'antd';
import React from 'react';

interface RoleFormProps {
  visible: boolean;
  onCancel: () => void;
  onFinish: (values: any) => Promise<void>;
  initialValues?: any;
}

const RoleForm: React.FC<RoleFormProps> = ({
  visible,
  onCancel,
  onFinish,
  initialValues,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues]);

  return (
    <Modal
      title={initialValues ? '编辑角色' : '新建角色'}
      visible={visible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      destroyOnClose
    >
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          name="name"
          label="角色名称"
          rules={[{ required: true, message: '请输入角色名称' }]}
        >
          <Input placeholder="请输入角色名称" />
        </Form.Item>
        <Form.Item
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入角色描述' }]}
        >
          <Input.TextArea placeholder="请输入角色描述" rows={4} />
        </Form.Item>
        <Form.Item
          name="permissions"
          label="权限"
          rules={[{ required: true, message: '请选择权限' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择权限"
            options={[
              { label: '用户管理', value: 'user:manage' },
              { label: '角色管理', value: 'role:manage' },
              { label: '权限管理', value: 'permission:manage' },
              { label: '内容管理', value: 'content:manage' },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RoleForm;
