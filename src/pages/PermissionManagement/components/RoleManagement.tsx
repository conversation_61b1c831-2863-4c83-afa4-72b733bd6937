import {
  addRole,
  deleteRole,
  queryRoles,
  updateRole,
} from '@/services/permissionService';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import RoleForm from './RoleForm';

interface RoleItem {
  id: string;
  name: string;
  description: string;
  permissions: string[];
}

const RoleManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [visible, setVisible] = useState(false);
  const [currentRole, setCurrentRole] = useState<RoleItem | undefined>(
    undefined,
  );

  const handleAdd = () => {
    setCurrentRole(undefined);
    setVisible(true);
  };

  const handleEdit = (record: RoleItem) => {
    setCurrentRole(record);
    setVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除角色',
      content: '确定要删除这个角色吗？',
      onOk: async () => {
        await deleteRole(id);
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const columns: ProColumns<RoleItem>[] = [
    {
      title: '角色名称',
      dataIndex: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      render: (_, record) => record.permissions.join(', '),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        <a key="delete" onClick={() => handleDelete(record.id)}>
          删除
        </a>,
      ],
    },
  ];

  return (
    <>
      <ProTable<RoleItem>
        columns={columns}
        actionRef={actionRef}
        request={async (params) => {
          const response = await queryRoles(params);
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            type="primary"
            onClick={handleAdd}
          >
            新建
          </Button>,
        ]}
      />
      <RoleForm
        visible={visible}
        onCancel={() => setVisible(false)}
        onFinish={async (values) => {
          if (currentRole) {
            await updateRole({ ...values, id: currentRole.id });
            message.success('更新成功');
          } else {
            await addRole(values);
            message.success('添加成功');
          }
          setVisible(false);
          actionRef.current?.reload();
        }}
        initialValues={currentRole}
      />
    </>
  );
};

export default RoleManagement;
