import { Form, Input, Modal } from 'antd';
import React from 'react';

interface PermissionFormProps {
  visible: boolean;
  onCancel: () => void;
  onFinish: (values: any) => Promise<void>;
  initialValues?: any;
}

const PermissionForm: React.FC<PermissionFormProps> = ({
  visible,
  onCancel,
  onFinish,
  initialValues,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues]);

  return (
    <Modal
      title={initialValues ? '编辑权限' : '新建权限'}
      visible={visible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      destroyOnClose
    >
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          name="name"
          label="权限名称"
          rules={[{ required: true, message: '请输入权限名称' }]}
        >
          <Input placeholder="请输入权限名称" />
        </Form.Item>
        <Form.Item
          name="code"
          label="权限编码"
          rules={[{ required: true, message: '请输入权限编码' }]}
        >
          <Input placeholder="请输入权限编码" />
        </Form.Item>
        <Form.Item
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入权限描述' }]}
        >
          <Input.TextArea placeholder="请输入权限描述" rows={4} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PermissionForm;
