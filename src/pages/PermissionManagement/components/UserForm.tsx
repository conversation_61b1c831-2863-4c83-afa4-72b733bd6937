import { Form, Input, Modal, Select } from 'antd';
import React from 'react';

interface UserFormProps {
  visible: boolean;
  onCancel: () => void;
  onFinish: (values: any) => Promise<void>;
  initialValues?: any;
}

const UserForm: React.FC<UserFormProps> = ({
  visible,
  onCancel,
  onFinish,
  initialValues,
}) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues]);

  return (
    <Modal
      title={initialValues ? '编辑用户' : '新建用户'}
      visible={visible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      destroyOnClose
    >
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item
          name="roles"
          label="角色"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择角色"
            options={[
              { label: '管理员', value: 'admin' },
              { label: '编辑', value: 'editor' },
              { label: '查看', value: 'viewer' },
            ]}
          />
        </Form.Item>
        <Form.Item name="status" label="状态" initialValue="active">
          <Select
            options={[
              { label: '活跃', value: 'active' },
              { label: '禁用', value: 'disabled' },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserForm;
