.container {
  .filterCard {
    margin-bottom: 16px;
  }

  .statsRow {
    margin-bottom: 16px;

    .growth {
      font-size: 12px;
      margin-left: 8px;
    }
  }

  .chartsRow {
    margin-bottom: 16px;
  }

  .channelCard {
    .rateCell {
      display: flex;
      align-items: center;
      gap: 8px;

      .ant-progress {
        flex: 1;
        margin: 0;
      }

      span {
        font-size: 12px;
        color: #666;
        min-width: 40px;
      }
    }
  }

  .typeStats {
    .typeItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px;
      background: #fafafa;
      border-radius: 6px;

      .typeInfo {
        display: flex;
        align-items: center;
        gap: 12px;

        .count {
          font-weight: 500;
          color: #262626;
        }
      }

      .readRate {
        width: 120px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .filterCard {
      .ant-space {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;

        .ant-select,
        .ant-picker {
          width: 100%;
        }
      }
    }

    .channelCard {
      .rateCell {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;

        .ant-progress {
          width: 100%;
        }
      }
    }

    .typeStats {
      .typeItem {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .readRate {
          width: 100%;
        }
      }
    }
  }
}
