import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Space,
  Table,
  Tag,
  Progress,
  Button,
  Tooltip,
} from 'antd';
import {
  SendOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  DownloadOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Line, Column, Pie, Area } from '@ant-design/plots';
import type { MessageStats } from '@/types/notification';
import styles from './index.less';

const { RangePicker } = DatePicker;
const { Option } = Select;

const NotificationAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('7d');
  const [loading, setLoading] = useState<boolean>(false);

  // 模拟统计数据
  const stats: MessageStats = {
    totalSent: 125680,
    totalRead: 98540,
    totalProcessed: 87320,
    readRate: 78.4,
    processRate: 69.5,
    channelStats: {
      in_app: { sent: 85000, delivered: 84500, opened: 67200, clicked: 45800, failed: 500 },
      push: { sent: 25000, delivered: 23500, opened: 18800, clicked: 12600, failed: 1500 },
      sms: { sent: 10000, delivered: 9800, opened: 8900, clicked: 6200, failed: 200 },
      email: { sent: 5680, delivered: 5400, opened: 4040, clicked: 2720, failed: 280 },
    },
    platformStats: {
      local: { messages: 95000, interactions: 78500, lastSync: '2024-01-15 14:30:00' },
      douyin: { messages: 18500, interactions: 12800, lastSync: '2024-01-15 14:25:00' },
      xiaohongshu: { messages: 12180, interactions: 7240, lastSync: '2024-01-15 14:20:00' },
    },
    typeStats: {
      system: { count: 45000, readRate: 85.2 },
      comment: { count: 38500, readRate: 72.8 },
      like: { count: 25600, readRate: 65.4 },
      activity: { count: 16580, readRate: 88.9 },
    },
    trendData: [
      { date: '2024-01-08', sent: 15200, read: 11800, processed: 10200 },
      { date: '2024-01-09', sent: 16800, read: 13200, processed: 11500 },
      { date: '2024-01-10', sent: 14500, read: 11400, processed: 9800 },
      { date: '2024-01-11', sent: 18200, read: 14300, processed: 12600 },
      { date: '2024-01-12', sent: 19500, read: 15200, processed: 13400 },
      { date: '2024-01-13', sent: 21000, read: 16500, processed: 14200 },
      { date: '2024-01-14', sent: 20480, read: 16140, processed: 14220 },
    ],
  };

  // 渠道效果数据
  const channelData = Object.entries(stats.channelStats).map(([channel, data]) => ({
    channel,
    sent: data.sent,
    delivered: data.delivered,
    opened: data.opened,
    clicked: data.clicked,
    failed: data.failed,
    deliveryRate: ((data.delivered / data.sent) * 100).toFixed(1),
    openRate: ((data.opened / data.delivered) * 100).toFixed(1),
    clickRate: ((data.clicked / data.opened) * 100).toFixed(1),
  }));

  // 平台分布数据
  const platformData = Object.entries(stats.platformStats).map(([platform, data]) => ({
    platform,
    messages: data.messages,
    interactions: data.interactions,
    interactionRate: ((data.interactions / data.messages) * 100).toFixed(1),
  }));

  // 消息类型分布数据
  const typeDistributionData = Object.entries(stats.typeStats).map(([type, data]) => ({
    type,
    count: data.count,
    readRate: data.readRate,
  }));

  // 渠道配置
  const channelConfig = {
    in_app: { name: '站内信', color: '#1890ff' },
    push: { name: 'App推送', color: '#52c41a' },
    sms: { name: '短信', color: '#faad14' },
    email: { name: '邮件', color: '#722ed1' },
  };

  // 平台配置
  const platformConfig = {
    local: { name: '本地平台', color: '#1890ff' },
    douyin: { name: '抖音', color: '#ff4d4f' },
    xiaohongshu: { name: '小红书', color: '#eb2f96' },
  };

  // 消息类型配置
  const typeConfig = {
    system: { name: '系统消息', color: '#1890ff' },
    comment: { name: '评论消息', color: '#52c41a' },
    like: { name: '点赞消息', color: '#ff4d4f' },
    activity: { name: '活动消息', color: '#722ed1' },
  };

  // 趋势图配置
  const trendConfig = {
    data: stats.trendData,
    xField: 'date',
    yField: 'sent',
    seriesField: 'type',
    smooth: true,
    color: ['#1890ff', '#52c41a', '#faad14'],
    point: { size: 4, shape: 'circle' },
    legend: { position: 'top' as const },
  };

  // 渠道对比图配置
  const channelCompareConfig = {
    data: channelData,
    xField: 'channel',
    yField: 'sent',
    color: '#1890ff',
    columnWidthRatio: 0.6,
    meta: {
      channel: { alias: '渠道' },
      sent: { alias: '发送量' },
    },
  };

  // 平台分布饼图配置
  const platformPieConfig = {
    data: platformData,
    angleField: 'messages',
    colorField: 'platform',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 渠道效果表格列
  const channelColumns = [
    {
      title: '渠道',
      dataIndex: 'channel',
      key: 'channel',
      render: (channel: string) => (
        <Tag color={channelConfig[channel as keyof typeof channelConfig]?.color}>
          {channelConfig[channel as keyof typeof channelConfig]?.name}
        </Tag>
      ),
    },
    {
      title: '发送量',
      dataIndex: 'sent',
      key: 'sent',
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: '送达率',
      dataIndex: 'deliveryRate',
      key: 'deliveryRate',
      render: (rate: string) => (
        <div className={styles.rateCell}>
          <Progress percent={parseFloat(rate)} size="small" />
          <span>{rate}%</span>
        </div>
      ),
    },
    {
      title: '打开率',
      dataIndex: 'openRate',
      key: 'openRate',
      render: (rate: string) => (
        <div className={styles.rateCell}>
          <Progress percent={parseFloat(rate)} size="small" strokeColor="#52c41a" />
          <span>{rate}%</span>
        </div>
      ),
    },
    {
      title: '点击率',
      dataIndex: 'clickRate',
      key: 'clickRate',
      render: (rate: string) => (
        <div className={styles.rateCell}>
          <Progress percent={parseFloat(rate)} size="small" strokeColor="#faad14" />
          <span>{rate}%</span>
        </div>
      ),
    },
    {
      title: '失败数',
      dataIndex: 'failed',
      key: 'failed',
      render: (value: number) => (
        <span style={{ color: value > 1000 ? '#ff4d4f' : '#666' }}>
          {value.toLocaleString()}
        </span>
      ),
    },
  ];

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('加载统计数据，时间范围:', timeRange);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [timeRange]);

  return (
    <PageContainer
      header={{
        title: '统计分析',
        breadcrumb: {
          items: [
            { title: '消息通知', path: '/notification' },
            { title: '统计分析' },
          ],
        },
      }}
    >
      <div className={styles.container}>
        {/* 筛选器 */}
        <Card className={styles.filterCard}>
          <Space size="large">
            <span>时间范围：</span>
            <Select value={timeRange} onChange={setTimeRange} style={{ width: 120 }}>
              <Option value="1d">今天</Option>
              <Option value="7d">近7天</Option>
              <Option value="30d">近30天</Option>
              <Option value="90d">近90天</Option>
            </Select>
            <RangePicker />
            <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
              刷新
            </Button>
            <Button icon={<DownloadOutlined />}>
              导出报告
            </Button>
          </Space>
        </Card>

        {/* 核心指标 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总发送量"
                value={stats.totalSent}
                prefix={<SendOutlined />}
                suffix={
                  <span className={styles.growth}>
                    <ArrowUpOutlined style={{ color: '#3f8600' }} />
                    12.5%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总阅读量"
                value={stats.totalRead}
                prefix={<EyeOutlined />}
                suffix={
                  <span className={styles.growth}>
                    <ArrowUpOutlined style={{ color: '#3f8600' }} />
                    8.3%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="阅读率"
                value={stats.readRate}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="处理率"
                value={stats.processRate}
                suffix="%"
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 趋势图表 */}
        <Row gutter={[16, 16]} className={styles.chartsRow}>
          <Col xs={24} lg={16}>
            <Card title="消息发送趋势" loading={loading}>
              <Line {...trendConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="平台消息分布" loading={loading}>
              <Pie {...platformPieConfig} height={300} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="渠道发送量对比" loading={loading}>
              <Column {...channelCompareConfig} height={250} />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="消息类型统计" loading={loading}>
              <div className={styles.typeStats}>
                {typeDistributionData.map(item => (
                  <div key={item.type} className={styles.typeItem}>
                    <div className={styles.typeInfo}>
                      <Tag color={typeConfig[item.type as keyof typeof typeConfig]?.color}>
                        {typeConfig[item.type as keyof typeof typeConfig]?.name}
                      </Tag>
                      <span className={styles.count}>{item.count.toLocaleString()}</span>
                    </div>
                    <div className={styles.readRate}>
                      <Progress
                        percent={item.readRate}
                        size="small"
                        format={(percent) => `${percent}%`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>

        {/* 渠道效果分析 */}
        <Card title="渠道效果分析" className={styles.channelCard}>
          <Table
            dataSource={channelData}
            columns={channelColumns}
            pagination={false}
            size="small"
            rowKey="channel"
          />
        </Card>
      </div>
    </PageContainer>
  );
};

export default NotificationAnalytics;
