import React, { useState, useEffect } from 'react';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import {
  Button,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Card,
  Row,
  Col,
  Tooltip,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  SendOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useRef } from 'react';
import type { MessageTemplate, MessageType, BusinessType, PushChannel } from '@/types/notification';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;

// 模拟模板数据
const mockTemplates: MessageTemplate[] = [
  {
    id: '1',
    name: '内容审核通过通知',
    type: 'audit' as MessageType,
    businessType: 'audit_result' as BusinessType,
    title: '您的{{contentType}}已通过审核',
    content: '恭喜！您提交的{{contentType}}"{{contentTitle}}"已通过审核，现已发布。感谢您的优质内容创作！',
    variables: ['contentType', 'contentTitle'],
    channels: ['in_app', 'push'] as PushChannel[],
    isActive: true,
    createTime: '2024-01-10 10:00:00',
    updateTime: '2024-01-15 14:30:00',
    creator: 'admin',
    description: '用于通知用户内容审核通过',
  },
  {
    id: '2',
    name: '新评论通知',
    type: 'comment' as MessageType,
    businessType: 'content_comment' as BusinessType,
    title: '您的{{contentType}}收到新评论',
    content: '用户"{{commenterName}}"评论了您的{{contentType}}"{{contentTitle}}"：{{commentContent}}',
    variables: ['contentType', 'commenterName', 'contentTitle', 'commentContent'],
    channels: ['in_app', 'push', 'email'] as PushChannel[],
    isActive: true,
    createTime: '2024-01-10 11:00:00',
    updateTime: '2024-01-10 11:00:00',
    creator: 'admin',
    description: '用于通知用户收到新评论',
  },
  {
    id: '3',
    name: '活动邀请通知',
    type: 'activity' as MessageType,
    businessType: 'activity_invite' as BusinessType,
    title: '{{activityName}} 活动邀请',
    content: '亲爱的{{userName}}，{{activityName}}活动即将开始！活动时间：{{startTime}}，快来参与吧！',
    variables: ['userName', 'activityName', 'startTime'],
    channels: ['in_app', 'push', 'sms'] as PushChannel[],
    isActive: false,
    createTime: '2024-01-10 12:00:00',
    updateTime: '2024-01-12 16:20:00',
    creator: 'admin',
    description: '用于邀请用户参与活动',
  },
];

const TemplateManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [previewForm] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [previewModalVisible, setPreviewModalVisible] = useState<boolean>(false);
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null);
  const [previewContent, setPreviewContent] = useState<{ title: string; content: string }>({ title: '', content: '' });

  // 消息类型配置
  const messageTypeConfig = {
    system: { color: 'blue', text: '系统消息' },
    comment: { color: 'green', text: '评论消息' },
    like: { color: 'red', text: '点赞消息' },
    follow: { color: 'purple', text: '关注消息' },
    private: { color: 'orange', text: '私信消息' },
    security: { color: 'red', text: '安全消息' },
    activity: { color: 'cyan', text: '活动消息' },
    audit: { color: 'gold', text: '审核消息' },
  };

  // 渠道配置
  const channelConfig = {
    in_app: { color: 'blue', text: '站内信' },
    push: { color: 'green', text: 'App推送' },
    sms: { color: 'orange', text: '短信' },
    email: { color: 'purple', text: '邮件' },
    web_popup: { color: 'cyan', text: 'Web弹窗' },
  };

  // 处理模板操作
  const handleTemplateAction = async (action: string, template: MessageTemplate) => {
    try {
      switch (action) {
        case 'edit':
          setSelectedTemplate(template);
          form.setFieldsValue({
            name: template.name,
            type: template.type,
            businessType: template.businessType,
            title: template.title,
            content: template.content,
            channels: template.channels,
            isActive: template.isActive,
            description: template.description,
          });
          setCreateModalVisible(true);
          break;
        case 'copy':
          setSelectedTemplate(null);
          form.setFieldsValue({
            name: `${template.name} - 副本`,
            type: template.type,
            businessType: template.businessType,
            title: template.title,
            content: template.content,
            channels: template.channels,
            isActive: false,
            description: template.description,
          });
          setCreateModalVisible(true);
          break;
        case 'preview':
          setSelectedTemplate(template);
          previewForm.resetFields();
          setPreviewModalVisible(true);
          break;
        case 'test':
          message.info(`测试发送模板：${template.name}`);
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除模板"${template.name}"吗？`,
            onOk: () => {
              message.success('删除成功');
              actionRef.current?.reload();
            },
          });
          break;
        case 'toggle':
          message.success(template.isActive ? '模板已禁用' : '模板已启用');
          actionRef.current?.reload();
          break;
        default:
          break;
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 创建/编辑模板
  const handleCreateTemplate = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建/编辑模板:', values);
      message.success(selectedTemplate ? '更新成功' : '创建成功');
      setCreateModalVisible(false);
      setSelectedTemplate(null);
      form.resetFields();
      actionRef.current?.reload();
    } catch (error) {
      message.error('请完善必填信息');
    }
  };

  // 预览模板
  const handlePreviewTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const variables = await previewForm.validateFields();
      
      // 替换模板变量
      let title = selectedTemplate.title;
      let content = selectedTemplate.content;
      
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        title = title.replace(regex, value as string);
        content = content.replace(regex, value as string);
      });

      setPreviewContent({ title, content });
    } catch (error) {
      message.error('请填写所有变量');
    }
  };

  // 提取模板变量
  const extractVariables = (text: string): string[] => {
    const regex = /{{(\w+)}}/g;
    const variables: string[] = [];
    let match;
    while ((match = regex.exec(text)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }
    return variables;
  };

  // 表格列配置
  const columns: ProColumns<MessageTemplate>[] = [
    {
      title: '模板信息',
      key: 'info',
      width: 250,
      render: (_, record) => (
        <div className={styles.templateInfo}>
          <div className={styles.name}>{record.name}</div>
          <div className={styles.description}>{record.description}</div>
          <div className={styles.meta}>
            <Tag color={messageTypeConfig[record.type]?.color}>
              {messageTypeConfig[record.type]?.text}
            </Tag>
            <Tag color={record.isActive ? 'success' : 'default'}>
              {record.isActive ? '启用' : '禁用'}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: '模板内容',
      key: 'content',
      width: 300,
      render: (_, record) => (
        <div className={styles.templateContent}>
          <div className={styles.title}>
            <strong>标题：</strong>{record.title}
          </div>
          <div className={styles.content}>
            <strong>内容：</strong>
            <Tooltip title={record.content}>
              <span className={styles.contentText}>{record.content}</span>
            </Tooltip>
          </div>
          {record.variables.length > 0 && (
            <div className={styles.variables}>
              <strong>变量：</strong>
              {record.variables.map(variable => (
                <Tag key={variable} size="small" color="blue">
                  {variable}
                </Tag>
              ))}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '推送渠道',
      dataIndex: 'channels',
      key: 'channels',
      width: 150,
      render: (channels: PushChannel[]) => (
        <div className={styles.channels}>
          {channels.map(channel => (
            <Tag key={channel} color={channelConfig[channel]?.color} size="small">
              {channelConfig[channel]?.text}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '创建信息',
      key: 'createInfo',
      width: 150,
      render: (_, record) => (
        <div className={styles.createInfo}>
          <div>创建者：{record.creator}</div>
          <div>创建时间：{record.createTime}</div>
          <div>更新时间：{record.updateTime}</div>
        </div>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Button
          key="preview"
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleTemplateAction('preview', record)}
        >
          预览
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleTemplateAction('edit', record)}
        >
          编辑
        </Button>,
        <Button
          key="copy"
          type="link"
          size="small"
          icon={<CopyOutlined />}
          onClick={() => handleTemplateAction('copy', record)}
        >
          复制
        </Button>,
        <Button
          key="test"
          type="link"
          size="small"
          icon={<SendOutlined />}
          onClick={() => handleTemplateAction('test', record)}
        >
          测试
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleTemplateAction('delete', record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer
      header={{
        title: '模板管理',
        breadcrumb: {
          items: [
            { title: '消息通知', path: '/notification' },
            { title: '模板管理' },
          ],
        },
      }}
    >
      <div className={styles.container}>
        {/* 统计概览 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <FileTextOutlined className={styles.statIcon} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>24</div>
                  <div className={styles.statLabel}>总模板数</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <FileTextOutlined className={styles.statIcon} style={{ color: '#52c41a' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>18</div>
                  <div className={styles.statLabel}>启用模板</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <SendOutlined className={styles.statIcon} style={{ color: '#1890ff' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>1,256</div>
                  <div className={styles.statLabel}>今日使用</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <SendOutlined className={styles.statIcon} style={{ color: '#faad14' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>95.6%</div>
                  <div className={styles.statLabel}>发送成功率</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 模板表格 */}
        <ProTable<MessageTemplate>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="create"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建模板
            </Button>,
          ]}
          request={async (params, sort, filter) => {
            console.log('请求参数:', params, sort, filter);
            return {
              data: mockTemplates,
              success: true,
              total: mockTemplates.length,
            };
          }}
          columns={columns}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />

        {/* 创建/编辑模板模态框 */}
        <Modal
          title={selectedTemplate ? '编辑模板' : '创建模板'}
          open={createModalVisible}
          onOk={handleCreateTemplate}
          onCancel={() => {
            setCreateModalVisible(false);
            setSelectedTemplate(null);
            form.resetFields();
          }}
          width={800}
          destroyOnClose
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              isActive: true,
              channels: ['in_app'],
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="模板名称"
                  rules={[{ required: true, message: '请输入模板名称' }]}
                >
                  <Input placeholder="输入模板名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="type"
                  label="消息类型"
                  rules={[{ required: true, message: '请选择消息类型' }]}
                >
                  <Select placeholder="选择消息类型">
                    {Object.entries(messageTypeConfig).map(([key, config]) => (
                      <Option key={key} value={key}>
                        {config.text}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="title"
              label="消息标题"
              rules={[{ required: true, message: '请输入消息标题' }]}
              extra="支持变量，格式：{{变量名}}"
            >
              <Input placeholder="输入消息标题，支持变量" />
            </Form.Item>

            <Form.Item
              name="content"
              label="消息内容"
              rules={[{ required: true, message: '请输入消息内容' }]}
              extra="支持变量，格式：{{变量名}}"
            >
              <TextArea
                rows={4}
                placeholder="输入消息内容，支持变量"
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item
              name="channels"
              label="推送渠道"
              rules={[{ required: true, message: '请选择推送渠道' }]}
            >
              <Select mode="multiple" placeholder="选择推送渠道">
                {Object.entries(channelConfig).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="isActive" label="启用状态" valuePropName="checked">
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="description" label="模板描述">
              <TextArea
                rows={2}
                placeholder="可选：添加模板描述"
                maxLength={200}
                showCount
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 预览模板模态框 */}
        <Modal
          title="预览模板"
          open={previewModalVisible}
          onCancel={() => {
            setPreviewModalVisible(false);
            setSelectedTemplate(null);
            setPreviewContent({ title: '', content: '' });
          }}
          footer={[
            <Button key="cancel" onClick={() => setPreviewModalVisible(false)}>
              关闭
            </Button>,
            <Button key="preview" type="primary" onClick={handlePreviewTemplate}>
              生成预览
            </Button>,
          ]}
          width={700}
        >
          {selectedTemplate && (
            <div className={styles.previewModal}>
              <div className={styles.variableForm}>
                <h4>填写模板变量：</h4>
                <Form form={previewForm} layout="vertical">
                  <Row gutter={16}>
                    {selectedTemplate.variables.map(variable => (
                      <Col span={12} key={variable}>
                        <Form.Item
                          name={variable}
                          label={variable}
                          rules={[{ required: true, message: `请输入${variable}` }]}
                        >
                          <Input placeholder={`输入${variable}`} />
                        </Form.Item>
                      </Col>
                    ))}
                  </Row>
                </Form>
              </div>

              <Divider />

              <div className={styles.previewContent}>
                <h4>预览效果：</h4>
                <div className={styles.previewCard}>
                  <div className={styles.previewTitle}>
                    <strong>标题：</strong>
                    {previewContent.title || selectedTemplate.title}
                  </div>
                  <div className={styles.previewText}>
                    <strong>内容：</strong>
                    {previewContent.content || selectedTemplate.content}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </PageContainer>
  );
};

export default TemplateManagement;
