.container {
  .statsRow {
    margin-bottom: 16px;

    .statCard {
      display: flex;
      align-items: center;
      gap: 12px;

      .statIcon {
        font-size: 24px;
        color: #1890ff;
      }

      .statContent {
        .statValue {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          line-height: 1;
        }

        .statLabel {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }

  .templateInfo {
    .name {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .description {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .meta {
      .ant-tag {
        margin-bottom: 4px;
      }
    }
  }

  .templateContent {
    .title {
      margin-bottom: 8px;
      font-size: 13px;
      line-height: 1.4;
    }

    .content {
      margin-bottom: 8px;
      font-size: 13px;
      line-height: 1.4;

      .contentText {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: #666;
      }
    }

    .variables {
      font-size: 12px;

      .ant-tag {
        margin-bottom: 2px;
      }
    }
  }

  .channels {
    .ant-tag {
      margin-bottom: 4px;
    }
  }

  .createInfo {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;
    }
  }

  .previewModal {
    .variableForm {
      h4 {
        margin-bottom: 16px;
        font-size: 14px;
        color: #262626;
      }
    }

    .previewContent {
      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        color: #262626;
      }

      .previewCard {
        padding: 16px;
        background: #f5f5f5;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        .previewTitle {
          margin-bottom: 12px;
          font-size: 14px;
          line-height: 1.4;

          strong {
            color: #262626;
          }
        }

        .previewText {
          font-size: 13px;
          line-height: 1.5;
          color: #666;

          strong {
            color: #262626;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .statsRow {
      .statCard {
        gap: 8px;

        .statIcon {
          font-size: 20px;
        }

        .statContent {
          .statValue {
            font-size: 16px;
          }

          .statLabel {
            font-size: 11px;
          }
        }
      }
    }

    .templateInfo {
      .name {
        font-size: 13px;
      }

      .description {
        font-size: 11px;
      }
    }

    .templateContent {
      .title,
      .content {
        font-size: 12px;
      }

      .variables {
        font-size: 11px;
      }
    }

    .createInfo {
      font-size: 11px;
    }

    .previewModal {
      .previewCard {
        padding: 12px;

        .previewTitle {
          font-size: 13px;
        }

        .previewText {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .container {
    .statsRow {
      .statCard {
        flex-direction: column;
        text-align: center;
        gap: 4px;
      }
    }

    .templateContent {
      .contentText {
        -webkit-line-clamp: 3;
      }
    }
  }
}
