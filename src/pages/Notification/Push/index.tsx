import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import {
  Button,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Progress,
  Statistic,
  Card,
  Row,
  Col,
  Tooltip,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  SendOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useRef } from 'react';
import type { PushTask, MessageTemplate, PushChannel } from '@/types/notification';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;

// 模拟推送任务数据
const mockPushTasks: PushTask[] = [
  {
    id: '1',
    name: '新年活动推送',
    type: 'scheduled',
    status: 'completed',
    templateId: 'template_001',
    targetUsers: ['user1', 'user2', 'user3'],
    channels: ['in_app', 'push', 'sms'],
    scheduledTime: '2024-01-15 10:00:00',
    progress: {
      total: 1000,
      sent: 1000,
      delivered: 950,
      failed: 50,
    },
    createTime: '2024-01-14 16:30:00',
    startTime: '2024-01-15 10:00:00',
    endTime: '2024-01-15 10:15:00',
    creator: 'admin',
  },
  {
    id: '2',
    name: '系统维护通知',
    type: 'immediate',
    status: 'running',
    templateId: 'template_002',
    targetUsers: ['all'],
    channels: ['in_app', 'push'],
    progress: {
      total: 5000,
      sent: 3200,
      delivered: 3000,
      failed: 200,
    },
    createTime: '2024-01-15 14:00:00',
    startTime: '2024-01-15 14:05:00',
    creator: 'admin',
  },
  {
    id: '3',
    name: '内容审核结果通知',
    type: 'recurring',
    status: 'pending',
    templateId: 'template_003',
    targetUsers: ['content_creators'],
    channels: ['in_app', 'email'],
    recurringConfig: {
      frequency: 'daily',
      interval: 1,
      endDate: '2024-02-15',
    },
    progress: {
      total: 0,
      sent: 0,
      delivered: 0,
      failed: 0,
    },
    createTime: '2024-01-15 09:00:00',
    creator: 'admin',
  },
];

// 模拟模板数据
const mockTemplates: MessageTemplate[] = [
  {
    id: 'template_001',
    name: '活动推送模板',
    type: 'activity',
    businessType: 'activity_invite',
    title: '{{activityName}} 活动邀请',
    content: '亲爱的用户，{{activityName}} 活动即将开始，快来参与吧！活动时间：{{startTime}}',
    variables: ['activityName', 'startTime'],
    channels: ['in_app', 'push', 'sms'],
    isActive: true,
    createTime: '2024-01-10 10:00:00',
    updateTime: '2024-01-10 10:00:00',
    creator: 'admin',
  },
  {
    id: 'template_002',
    name: '系统通知模板',
    type: 'system',
    businessType: 'system_notice',
    title: '系统通知',
    content: '{{content}}',
    variables: ['content'],
    channels: ['in_app', 'push'],
    isActive: true,
    createTime: '2024-01-10 11:00:00',
    updateTime: '2024-01-10 11:00:00',
    creator: 'admin',
  },
];

const PushManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<PushTask | null>(null);
  const [templates, setTemplates] = useState<MessageTemplate[]>(mockTemplates);

  // 任务状态配置
  const statusConfig = {
    pending: { color: 'default', text: '待执行' },
    running: { color: 'processing', text: '执行中' },
    completed: { color: 'success', text: '已完成' },
    failed: { color: 'error', text: '执行失败' },
    cancelled: { color: 'warning', text: '已取消' },
  };

  // 任务类型配置
  const typeConfig = {
    immediate: { color: 'blue', text: '立即执行' },
    scheduled: { color: 'green', text: '定时执行' },
    recurring: { color: 'purple', text: '循环执行' },
  };

  // 渠道配置
  const channelConfig = {
    in_app: { color: 'blue', text: '站内信' },
    push: { color: 'green', text: 'App推送' },
    sms: { color: 'orange', text: '短信' },
    email: { color: 'purple', text: '邮件' },
    web_popup: { color: 'cyan', text: 'Web弹窗' },
  };

  // 处理任务操作
  const handleTaskAction = async (action: string, task: PushTask) => {
    try {
      switch (action) {
        case 'start':
          message.success(`启动任务：${task.name}`);
          actionRef.current?.reload();
          break;
        case 'stop':
          Modal.confirm({
            title: '确认停止',
            content: `确定要停止任务"${task.name}"吗？`,
            onOk: () => {
              message.success('任务已停止');
              actionRef.current?.reload();
            },
          });
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除任务"${task.name}"吗？`,
            onOk: () => {
              message.success('删除成功');
              actionRef.current?.reload();
            },
          });
          break;
        case 'view':
          message.info(`查看任务详情：${task.name}`);
          break;
        case 'edit':
          setSelectedTask(task);
          form.setFieldsValue({
            name: task.name,
            templateId: task.templateId,
            channels: task.channels,
            type: task.type,
            scheduledTime: task.scheduledTime,
          });
          setCreateModalVisible(true);
          break;
        default:
          break;
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 创建/编辑推送任务
  const handleCreateTask = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建推送任务:', values);
      message.success(selectedTask ? '更新成功' : '创建成功');
      setCreateModalVisible(false);
      setSelectedTask(null);
      form.resetFields();
      actionRef.current?.reload();
    } catch (error) {
      message.error('请完善必填信息');
    }
  };

  // 表格列配置
  const columns: ProColumns<PushTask>[] = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string, record) => (
        <div className={styles.taskName}>
          <div className={styles.name}>{name}</div>
          <div className={styles.creator}>创建者：{record.creator}</div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: keyof typeof typeConfig) => (
        <Tag color={typeConfig[type].color}>{typeConfig[type].text}</Tag>
      ),
      filters: [
        { text: '立即执行', value: 'immediate' },
        { text: '定时执行', value: 'scheduled' },
        { text: '循环执行', value: 'recurring' },
      ],
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusConfig) => (
        <Tag color={statusConfig[status].color}>{statusConfig[status].text}</Tag>
      ),
      filters: [
        { text: '待执行', value: 'pending' },
        { text: '执行中', value: 'running' },
        { text: '已完成', value: 'completed' },
        { text: '执行失败', value: 'failed' },
        { text: '已取消', value: 'cancelled' },
      ],
    },
    {
      title: '推送渠道',
      dataIndex: 'channels',
      key: 'channels',
      width: 150,
      render: (channels: PushChannel[]) => (
        <div className={styles.channels}>
          {channels.map(channel => (
            <Tag key={channel} color={channelConfig[channel].color} size="small">
              {channelConfig[channel].text}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '执行进度',
      key: 'progress',
      width: 200,
      render: (_, record) => {
        const { progress } = record;
        const percent = progress.total > 0 ? Math.round((progress.sent / progress.total) * 100) : 0;
        
        return (
          <div className={styles.progressCell}>
            <Progress
              percent={percent}
              size="small"
              status={record.status === 'failed' ? 'exception' : 'normal'}
            />
            <div className={styles.progressText}>
              {progress.sent}/{progress.total} 
              {progress.failed > 0 && (
                <span className={styles.failed}> (失败: {progress.failed})</span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: '计划时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
      width: 150,
      render: (time: string) => time || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      sorter: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        record.status === 'pending' && (
          <Button
            key="start"
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handleTaskAction('start', record)}
          >
            启动
          </Button>
        ),
        record.status === 'running' && (
          <Button
            key="stop"
            type="link"
            size="small"
            icon={<PauseCircleOutlined />}
            onClick={() => handleTaskAction('stop', record)}
          >
            停止
          </Button>
        ),
        <Button
          key="view"
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleTaskAction('view', record)}
        >
          查看
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => handleTaskAction('edit', record)}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleTaskAction('delete', record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer
      header={{
        title: '推送管理',
        breadcrumb: {
          items: [
            { title: '消息通知', path: '/notification' },
            { title: '推送管理' },
          ],
        },
      }}
    >
      <div className={styles.container}>
        {/* 统计概览 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总任务数"
                value={156}
                prefix={<SendOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="执行中"
                value={5}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="今日发送"
                value={12580}
                prefix={<SendOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="成功率"
                value={95.6}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 推送任务表格 */}
        <ProTable<PushTask>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="create"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建推送任务
            </Button>,
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={() => actionRef.current?.reload()}
            >
              刷新
            </Button>,
          ]}
          request={async (params, sort, filter) => {
            console.log('请求参数:', params, sort, filter);
            return {
              data: mockPushTasks,
              success: true,
              total: mockPushTasks.length,
            };
          }}
          columns={columns}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />

        {/* 创建/编辑推送任务模态框 */}
        <Modal
          title={selectedTask ? '编辑推送任务' : '创建推送任务'}
          open={createModalVisible}
          onOk={handleCreateTask}
          onCancel={() => {
            setCreateModalVisible(false);
            setSelectedTask(null);
            form.resetFields();
          }}
          width={600}
          destroyOnClose
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              type: 'immediate',
              channels: ['in_app'],
            }}
          >
            <Form.Item
              name="name"
              label="任务名称"
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input placeholder="输入推送任务名称" />
            </Form.Item>

            <Form.Item
              name="templateId"
              label="消息模板"
              rules={[{ required: true, message: '请选择消息模板' }]}
            >
              <Select placeholder="选择消息模板">
                {templates.map(template => (
                  <Option key={template.id} value={template.id}>
                    {template.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="channels"
              label="推送渠道"
              rules={[{ required: true, message: '请选择推送渠道' }]}
            >
              <Select mode="multiple" placeholder="选择推送渠道">
                {Object.entries(channelConfig).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="type"
              label="执行类型"
              rules={[{ required: true, message: '请选择执行类型' }]}
            >
              <Select>
                <Option value="immediate">立即执行</Option>
                <Option value="scheduled">定时执行</Option>
                <Option value="recurring">循环执行</Option>
              </Select>
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.type !== currentValues.type
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('type') === 'scheduled' ? (
                  <Form.Item
                    name="scheduledTime"
                    label="执行时间"
                    rules={[{ required: true, message: '请选择执行时间' }]}
                  >
                    <DatePicker
                      showTime
                      placeholder="选择执行时间"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                ) : null
              }
            </Form.Item>

            <Form.Item name="description" label="任务描述">
              <TextArea
                rows={3}
                placeholder="可选：添加任务描述"
                maxLength={200}
                showCount
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </PageContainer>
  );
};

export default PushManagement;
