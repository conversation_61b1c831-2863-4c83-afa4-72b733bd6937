.container {
  .statsRow {
    margin-bottom: 16px;
  }

  .taskName {
    .name {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }

    .creator {
      font-size: 12px;
      color: #999;
    }
  }

  .channels {
    .ant-tag {
      margin-bottom: 4px;
    }
  }

  .progressCell {
    .progressText {
      font-size: 12px;
      color: #666;
      margin-top: 4px;

      .failed {
        color: #ff4d4f;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .statsRow {
      .ant-col {
        margin-bottom: 8px;
      }
    }

    .channels {
      .ant-tag {
        font-size: 11px;
        margin-bottom: 2px;
      }
    }

    .progressCell {
      .progressText {
        font-size: 11px;
      }
    }
  }
}
