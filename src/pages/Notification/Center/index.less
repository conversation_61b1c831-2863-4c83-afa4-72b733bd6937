.container {
  .filterCard {
    margin-bottom: 16px;
  }

  .messageCard {
    .ant-tabs-tab {
      .ant-badge {
        .ant-badge-count {
          font-size: 10px;
          height: 16px;
          min-width: 16px;
          line-height: 16px;
          padding: 0 4px;
        }
      }
    }

    .ant-tabs-extra-content {
      .ant-space {
        align-items: center;
      }
    }
  }

  .messageItem {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      background-color: #fafafa;
    }

    &.unread {
      background-color: #f6ffed;
      border-left: 3px solid #52c41a;
      padding-left: 13px;

      .messageContent .messageInfo .title {
        font-weight: 600;
        color: #262626;
      }
    }

    .ant-list-item-action {
      margin-left: 16px;
    }
  }

  .messageContent {
    width: 100%;

    .messageHeader {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      width: 100%;
    }

    .messageInfo {
      flex: 1;
      min-width: 0;

      .messageMeta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .title {
          font-size: 14px;
          color: #262626;
          font-weight: 500;
          flex: 1;
          margin-right: 12px;
        }
      }

      .messageText {
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .messageFooter {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;

        .time {
          color: #999;
        }

        .ant-btn-link {
          padding: 0;
          height: auto;
          font-size: 12px;
        }
      }
    }
  }

  .replyModal {
    .originalMessage {
      margin-bottom: 16px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 6px;

      h4 {
        margin-bottom: 8px;
        font-size: 14px;
        color: #262626;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
      }
    }

    .replyInput {
      h4 {
        margin-bottom: 8px;
        font-size: 14px;
        color: #262626;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .filterCard {
      .ant-space {
        width: 100%;
        
        .ant-input-affix-wrapper,
        .ant-select {
          width: 100% !important;
          margin-bottom: 8px;
        }
      }
    }

    .messageCard {
      .ant-tabs-extra-content {
        display: none;
      }
    }

    .messageItem {
      .messageContent {
        .messageInfo {
          .messageMeta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .title {
              margin-right: 0;
            }
          }

          .messageFooter {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .container {
    .messageItem {
      padding: 12px 0;

      .messageContent {
        .messageHeader {
          gap: 8px;
        }

        .messageInfo {
          .messageText {
            font-size: 12px;
            -webkit-line-clamp: 3;
          }

          .messageFooter {
            font-size: 11px;
          }
        }
      }
    }
  }
}
