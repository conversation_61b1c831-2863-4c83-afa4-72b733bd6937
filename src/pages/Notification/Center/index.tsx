import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Tabs,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Badge,
  Input,
  Select,
  DatePicker,
  Modal,
  message,
  Checkbox,
  Dropdown,
  Empty,
  Spin,
  Tooltip,
} from 'antd';
import {
  BellOutlined,
  MessageOutlined,
  HeartOutlined,
  UserAddOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckOutlined,
  MoreOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { Message, MessageType, MessageStatus, ThirdPartyPlatform } from '@/types/notification';
import styles from './index.less';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 模拟数据
const mockMessages: Message[] = [
  {
    id: '1',
    type: 'comment' as MessageType,
    businessType: 'content_comment' as any,
    platform: 'douyin' as ThirdPartyPlatform,
    title: '您的视频收到新评论',
    content: '用户"小明"评论了您的视频"产品运营策略分享"：这个分享很有用，学到了很多！',
    summary: '用户"小明"评论了您的视频',
    status: 'unread' as MessageStatus,
    priority: 'normal' as any,
    senderId: 'user_123',
    senderName: '小明',
    senderAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=xiaoming',
    receiverId: 'current_user',
    relatedId: 'video_001',
    relatedTitle: '产品运营策略分享',
    externalUrl: 'https://douyin.com/video/123',
    channels: ['in_app' as any],
    createTime: '2024-01-15 14:30:00',
  },
  {
    id: '2',
    type: 'like' as MessageType,
    businessType: 'content_like' as any,
    platform: 'xiaohongshu' as ThirdPartyPlatform,
    title: '您的笔记获得点赞',
    content: '您在小红书发布的笔记"2024年内容营销趋势"获得了5个新点赞',
    summary: '笔记获得5个新点赞',
    status: 'unread' as MessageStatus,
    priority: 'normal' as any,
    receiverId: 'current_user',
    relatedId: 'note_002',
    relatedTitle: '2024年内容营销趋势',
    externalUrl: 'https://xiaohongshu.com/note/456',
    channels: ['in_app' as any],
    createTime: '2024-01-15 13:15:00',
  },
  {
    id: '3',
    type: 'system' as MessageType,
    businessType: 'audit_result' as any,
    platform: 'local' as ThirdPartyPlatform,
    title: '内容审核通过',
    content: '您提交的内容"短视频制作技巧分享"已通过审核，现已发布。',
    summary: '内容审核通过',
    status: 'read' as MessageStatus,
    priority: 'normal' as any,
    receiverId: 'current_user',
    relatedId: 'content_003',
    relatedTitle: '短视频制作技巧分享',
    channels: ['in_app' as any, 'push' as any],
    createTime: '2024-01-15 10:20:00',
    readTime: '2024-01-15 11:00:00',
  },
  {
    id: '4',
    type: 'private' as MessageType,
    businessType: 'private_message' as any,
    platform: 'local' as ThirdPartyPlatform,
    title: '新私信',
    content: '用户"运营达人"向您发送了私信：您好，想请教一下关于用户增长的问题...',
    summary: '用户"运营达人"发送了私信',
    status: 'unread' as MessageStatus,
    priority: 'high' as any,
    senderId: 'user_456',
    senderName: '运营达人',
    senderAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=yunying',
    receiverId: 'current_user',
    channels: ['in_app' as any],
    createTime: '2024-01-15 09:45:00',
  },
];

const MessageCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('all');
  const [messages, setMessages] = useState<Message[]>(mockMessages);
  const [filteredMessages, setFilteredMessages] = useState<Message[]>(mockMessages);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [filterPlatform, setFilterPlatform] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [replyModalVisible, setReplyModalVisible] = useState<boolean>(false);
  const [currentMessage, setCurrentMessage] = useState<Message | null>(null);
  const [replyContent, setReplyContent] = useState<string>('');

  // 消息类型配置
  const messageTypeConfig = {
    all: { icon: <BellOutlined />, label: '全部', color: 'default' },
    system: { icon: <ExclamationCircleOutlined />, label: '系统', color: 'blue' },
    comment: { icon: <MessageOutlined />, label: '评论', color: 'green' },
    like: { icon: <HeartOutlined />, label: '点赞', color: 'red' },
    follow: { icon: <UserAddOutlined />, label: '关注', color: 'purple' },
    private: { icon: <MessageOutlined />, label: '私信', color: 'orange' },
    security: { icon: <ExclamationCircleOutlined />, label: '安全', color: 'red' },
    third_party: { icon: <BellOutlined />, label: '第三方平台', color: 'cyan' },
  };

  // 平台配置
  const platformConfig = {
    local: { name: '本地平台', color: 'blue' },
    douyin: { name: '抖音', color: 'red' },
    xiaohongshu: { name: '小红书', color: 'pink' },
    weibo: { name: '微博', color: 'orange' },
    wechat: { name: '微信', color: 'green' },
    bilibili: { name: 'B站', color: 'cyan' },
  };

  // 优先级配置
  const priorityConfig = {
    low: { color: 'default', text: '低' },
    normal: { color: 'blue', text: '普通' },
    high: { color: 'orange', text: '高' },
    urgent: { color: 'red', text: '紧急' },
  };

  // 加载消息数据
  useEffect(() => {
    loadMessages();
  }, []);

  // 筛选消息
  useEffect(() => {
    filterMessages();
  }, [activeTab, searchKeyword, filterPlatform, filterStatus, messages]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      // 这里应该调用真实的API
      console.log('加载消息数据');
    } catch (error) {
      message.error('加载消息失败');
    } finally {
      setLoading(false);
    }
  };

  const filterMessages = () => {
    let filtered = messages;

    // 按类型筛选
    if (activeTab !== 'all') {
      if (activeTab === 'third_party') {
        filtered = filtered.filter(msg => msg.platform !== 'local');
      } else {
        filtered = filtered.filter(msg => msg.type === activeTab);
      }
    }

    // 按关键词搜索
    if (searchKeyword) {
      filtered = filtered.filter(msg =>
        msg.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        msg.content.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        msg.senderName?.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }

    // 按平台筛选
    if (filterPlatform !== 'all') {
      filtered = filtered.filter(msg => msg.platform === filterPlatform);
    }

    // 按状态筛选
    if (filterStatus !== 'all') {
      filtered = filtered.filter(msg => msg.status === filterStatus);
    }

    setFilteredMessages(filtered);
  };

  // 获取未读消息数量
  const getUnreadCount = (type?: string) => {
    let msgs = messages;
    if (type && type !== 'all') {
      if (type === 'third_party') {
        msgs = msgs.filter(msg => msg.platform !== 'local');
      } else {
        msgs = msgs.filter(msg => msg.type === type);
      }
    }
    return msgs.filter(msg => msg.status === 'unread').length;
  };

  // 处理消息操作
  const handleMessageAction = async (action: string, messageId?: string) => {
    const targetMessages = messageId ? [messageId] : selectedMessages;
    
    if (targetMessages.length === 0) {
      message.warning('请选择要操作的消息');
      return;
    }

    try {
      switch (action) {
        case 'read':
          setMessages(prev => prev.map(msg => 
            targetMessages.includes(msg.id) 
              ? { ...msg, status: 'read' as MessageStatus, readTime: new Date().toISOString() }
              : msg
          ));
          message.success('标记为已读成功');
          break;
        case 'unread':
          setMessages(prev => prev.map(msg => 
            targetMessages.includes(msg.id) 
              ? { ...msg, status: 'unread' as MessageStatus, readTime: undefined }
              : msg
          ));
          message.success('标记为未读成功');
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除选中的 ${targetMessages.length} 条消息吗？`,
            onOk: () => {
              setMessages(prev => prev.filter(msg => !targetMessages.includes(msg.id)));
              setSelectedMessages([]);
              message.success('删除成功');
            },
          });
          break;
        case 'process':
          setMessages(prev => prev.map(msg => 
            targetMessages.includes(msg.id) 
              ? { ...msg, status: 'processed' as MessageStatus, processTime: new Date().toISOString() }
              : msg
          ));
          message.success('标记为已处理成功');
          break;
        default:
          break;
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 回复消息
  const handleReply = async () => {
    if (!currentMessage || !replyContent.trim()) {
      message.warning('请输入回复内容');
      return;
    }

    try {
      // 模拟API调用
      console.log('回复消息:', currentMessage.id, replyContent);
      message.success('回复成功');
      setReplyModalVisible(false);
      setReplyContent('');
      setCurrentMessage(null);
    } catch (error) {
      message.error('回复失败');
    }
  };

  // 跳转到外部链接
  const handleExternalLink = (url?: string) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  // 渲染消息项
  const renderMessageItem = (item: Message) => {
    const isUnread = item.status === 'unread';
    const platformInfo = platformConfig[item.platform];
    const priorityInfo = priorityConfig[item.priority];

    return (
      <List.Item
        key={item.id}
        className={`${styles.messageItem} ${isUnread ? styles.unread : ''}`}
        actions={[
          <Dropdown
            key="more"
            menu={{
              items: [
                {
                  key: 'read',
                  label: isUnread ? '标记已读' : '标记未读',
                  icon: <EyeOutlined />,
                  onClick: () => handleMessageAction(isUnread ? 'read' : 'unread', item.id),
                },
                ...(item.businessType === 'private_message' ? [{
                  key: 'reply',
                  label: '回复',
                  icon: <MessageOutlined />,
                  onClick: () => {
                    setCurrentMessage(item);
                    setReplyModalVisible(true);
                  },
                }] : []),
                {
                  key: 'process',
                  label: '标记已处理',
                  icon: <CheckOutlined />,
                  onClick: () => handleMessageAction('process', item.id),
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleMessageAction('delete', item.id),
                },
              ],
            }}
            placement="bottomRight"
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>,
        ]}
      >
        <div className={styles.messageContent}>
          <div className={styles.messageHeader}>
            <Checkbox
              checked={selectedMessages.includes(item.id)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedMessages(prev => [...prev, item.id]);
                } else {
                  setSelectedMessages(prev => prev.filter(id => id !== item.id));
                }
              }}
            />
            <Avatar src={item.senderAvatar} icon={<UserAddOutlined />} size="small" />
            <div className={styles.messageInfo}>
              <div className={styles.messageMeta}>
                <span className={styles.title}>{item.title}</span>
                <Space size="small">
                  <Tag color={platformInfo.color} size="small">
                    {platformInfo.name}
                  </Tag>
                  <Tag color={priorityInfo.color} size="small">
                    {priorityInfo.text}
                  </Tag>
                  {isUnread && <Badge status="processing" text="未读" />}
                </Space>
              </div>
              <div className={styles.messageText}>
                {item.summary || item.content}
              </div>
              <div className={styles.messageFooter}>
                <span className={styles.time}>{item.createTime}</span>
                {item.externalUrl && (
                  <Button
                    type="link"
                    size="small"
                    onClick={() => handleExternalLink(item.externalUrl)}
                  >
                    查看原文
                  </Button>
                )}
                {item.relatedTitle && (
                  <Tooltip title={item.relatedTitle}>
                    <Tag size="small">关联内容</Tag>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>
      </List.Item>
    );
  };

  // Tab项配置
  const tabItems = Object.entries(messageTypeConfig).map(([key, config]) => ({
    key,
    label: (
      <Badge count={getUnreadCount(key)} size="small" offset={[10, 0]}>
        <Space>
          {config.icon}
          {config.label}
        </Space>
      </Badge>
    ),
  }));

  return (
    <PageContainer
      header={{
        title: '消息中心',
        breadcrumb: {
          items: [
            { title: '消息通知', path: '/notification' },
            { title: '消息中心' },
          ],
        },
      }}
    >
      <div className={styles.container}>
        {/* 筛选器 */}
        <Card className={styles.filterCard}>
          <Space size="middle" wrap>
            <Search
              placeholder="搜索消息内容、发送者"
              allowClear
              style={{ width: 250 }}
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="选择平台"
              style={{ width: 120 }}
              value={filterPlatform}
              onChange={setFilterPlatform}
            >
              <Option value="all">全部平台</Option>
              {Object.entries(platformConfig).map(([key, config]) => (
                <Option key={key} value={key}>{config.name}</Option>
              ))}
            </Select>
            <Select
              placeholder="消息状态"
              style={{ width: 120 }}
              value={filterStatus}
              onChange={setFilterStatus}
            >
              <Option value="all">全部状态</Option>
              <Option value="unread">未读</Option>
              <Option value="read">已读</Option>
              <Option value="processed">已处理</Option>
            </Select>
            <Button icon={<ReloadOutlined />} onClick={loadMessages}>
              刷新
            </Button>
            <Button icon={<SettingOutlined />} onClick={() => message.info('推送设置功能开发中')}>
              推送设置
            </Button>
          </Space>
        </Card>

        {/* 消息列表 */}
        <Card className={styles.messageCard}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            tabBarExtraContent={
              selectedMessages.length > 0 && (
                <Space>
                  <span>已选择 {selectedMessages.length} 条</span>
                  <Button size="small" onClick={() => handleMessageAction('read')}>
                    标记已读
                  </Button>
                  <Button size="small" onClick={() => handleMessageAction('process')}>
                    标记已处理
                  </Button>
                  <Button size="small" danger onClick={() => handleMessageAction('delete')}>
                    删除
                  </Button>
                  <Button size="small" onClick={() => setSelectedMessages([])}>
                    取消选择
                  </Button>
                </Space>
              )
            }
          />
          
          <Spin spinning={loading}>
            {filteredMessages.length > 0 ? (
              <List
                dataSource={filteredMessages}
                renderItem={renderMessageItem}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                }}
              />
            ) : (
              <Empty description="暂无消息" />
            )}
          </Spin>
        </Card>

        {/* 回复模态框 */}
        <Modal
          title="回复消息"
          open={replyModalVisible}
          onOk={handleReply}
          onCancel={() => {
            setReplyModalVisible(false);
            setReplyContent('');
            setCurrentMessage(null);
          }}
          width={600}
        >
          {currentMessage && (
            <div className={styles.replyModal}>
              <div className={styles.originalMessage}>
                <h4>原消息：</h4>
                <p>{currentMessage.content}</p>
              </div>
              <div className={styles.replyInput}>
                <h4>回复内容：</h4>
                <Input.TextArea
                  rows={4}
                  placeholder="输入回复内容..."
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  maxLength={500}
                  showCount
                />
              </div>
            </div>
          )}
        </Modal>
      </div>
    </PageContainer>
  );
};

export default MessageCenter;
