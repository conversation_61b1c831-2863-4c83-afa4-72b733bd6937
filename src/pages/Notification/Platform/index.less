.container {
  .statsRow {
    margin-bottom: 16px;

    .statCard {
      display: flex;
      align-items: center;
      gap: 12px;

      .statIcon {
        font-size: 24px;
        color: #1890ff;
      }

      .statContent {
        .statValue {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          line-height: 1;
        }

        .statLabel {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }

  .alertCard {
    margin-bottom: 16px;
  }

  .platformInfo {
    .platformName {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 8px;

      .icon {
        font-size: 16px;
      }
    }

    .platformType {
      .ant-tag {
        margin-bottom: 4px;
      }
    }
  }

  .statusCell {
    display: flex;
    align-items: center;
    gap: 8px;

    .errorIcon {
      color: #ff4d4f;
      cursor: pointer;
    }
  }

  .configInfo {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 4px;
      word-break: break-all;
    }
  }

  .syncInfo {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .statsRow {
      .statCard {
        gap: 8px;

        .statIcon {
          font-size: 20px;
        }

        .statContent {
          .statValue {
            font-size: 16px;
          }

          .statLabel {
            font-size: 11px;
          }
        }
      }
    }

    .platformInfo {
      .platformName {
        font-size: 13px;
        gap: 6px;

        .icon {
          font-size: 14px;
        }
      }
    }

    .configInfo,
    .syncInfo {
      font-size: 11px;
    }
  }
}

@media (max-width: 576px) {
  .container {
    .statsRow {
      .statCard {
        flex-direction: column;
        text-align: center;
        gap: 4px;
      }
    }

    .platformInfo {
      .platformName {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  }
}
