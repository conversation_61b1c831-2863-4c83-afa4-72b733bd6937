import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ontainer, ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import {
  Button,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Card,
  Row,
  Col,
  Tooltip,
  Badge,
  Progress,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DisconnectOutlined,
  ApiOutlined,
  SettingOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useRef } from 'react';
import type { PlatformConfig, ThirdPartyPlatform } from '@/types/notification';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;
const { Password } = Input;

// 模拟平台配置数据
const mockPlatforms: PlatformConfig[] = [
  {
    id: '1',
    platform: 'douyin' as ThirdPartyPlatform,
    name: '抖音开放平台',
    isEnabled: true,
    config: {
      appId: 'dy_app_123456',
      appSecret: '***hidden***',
      accessToken: 'dy_token_***',
      apiEndpoint: 'https://open.douyin.com/api',
      webhookUrl: 'https://your-domain.com/webhook/douyin',
    },
    status: 'connected',
    lastSyncTime: '2024-01-15 14:30:00',
    createTime: '2024-01-10 10:00:00',
    updateTime: '2024-01-15 14:30:00',
  },
  {
    id: '2',
    platform: 'xiaohongshu' as ThirdPartyPlatform,
    name: '小红书开放平台',
    isEnabled: true,
    config: {
      appId: 'xhs_app_789012',
      appSecret: '***hidden***',
      accessToken: 'xhs_token_***',
      apiEndpoint: 'https://api.xiaohongshu.com',
      webhookUrl: 'https://your-domain.com/webhook/xiaohongshu',
    },
    status: 'connected',
    lastSyncTime: '2024-01-15 13:45:00',
    createTime: '2024-01-10 11:00:00',
    updateTime: '2024-01-15 13:45:00',
  },
  {
    id: '3',
    platform: 'weibo' as ThirdPartyPlatform,
    name: '微博开放平台',
    isEnabled: false,
    config: {
      appId: 'wb_app_345678',
      appSecret: '***hidden***',
      accessToken: '',
      apiEndpoint: 'https://api.weibo.com',
    },
    status: 'disconnected',
    createTime: '2024-01-10 12:00:00',
    updateTime: '2024-01-10 12:00:00',
    errorMessage: 'Token已过期，需要重新授权',
  },
];

const PlatformManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformConfig | null>(null);
  const [testLoading, setTestLoading] = useState<string>('');
  const [syncLoading, setSyncLoading] = useState<string>('');

  // 平台类型配置
  const platformTypeConfig = {
    douyin: { name: '抖音', color: 'red', icon: '🎵' },
    xiaohongshu: { name: '小红书', color: 'pink', icon: '📖' },
    weibo: { name: '微博', color: 'orange', icon: '📱' },
    wechat: { name: '微信', color: 'green', icon: '💬' },
    bilibili: { name: 'B站', color: 'cyan', icon: '📺' },
  };

  // 状态配置
  const statusConfig = {
    connected: { color: 'success', text: '已连接', icon: <CheckCircleOutlined /> },
    disconnected: { color: 'default', text: '未连接', icon: <DisconnectOutlined /> },
    error: { color: 'error', text: '连接异常', icon: <ExclamationCircleOutlined /> },
  };

  // 处理平台操作
  const handlePlatformAction = async (action: string, platform: PlatformConfig) => {
    try {
      switch (action) {
        case 'edit':
          setSelectedPlatform(platform);
          form.setFieldsValue({
            platform: platform.platform,
            name: platform.name,
            isEnabled: platform.isEnabled,
            appId: platform.config.appId,
            appSecret: platform.config.appSecret,
            apiEndpoint: platform.config.apiEndpoint,
            webhookUrl: platform.config.webhookUrl,
          });
          setCreateModalVisible(true);
          break;
        case 'test':
          setTestLoading(platform.id);
          // 模拟测试连接
          setTimeout(() => {
            setTestLoading('');
            if (platform.status === 'connected') {
              message.success('连接测试成功');
            } else {
              message.error('连接测试失败：' + (platform.errorMessage || '未知错误'));
            }
          }, 2000);
          break;
        case 'sync':
          setSyncLoading(platform.id);
          // 模拟同步数据
          setTimeout(() => {
            setSyncLoading('');
            message.success('数据同步成功');
            actionRef.current?.reload();
          }, 3000);
          break;
        case 'refresh':
          message.success('Token刷新成功');
          actionRef.current?.reload();
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除平台配置"${platform.name}"吗？`,
            onOk: () => {
              message.success('删除成功');
              actionRef.current?.reload();
            },
          });
          break;
        case 'toggle':
          message.success(platform.isEnabled ? '平台已禁用' : '平台已启用');
          actionRef.current?.reload();
          break;
        default:
          break;
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 创建/编辑平台配置
  const handleCreatePlatform = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建/编辑平台配置:', values);
      message.success(selectedPlatform ? '更新成功' : '创建成功');
      setCreateModalVisible(false);
      setSelectedPlatform(null);
      form.resetFields();
      actionRef.current?.reload();
    } catch (error) {
      message.error('请完善必填信息');
    }
  };

  // 表格列配置
  const columns: ProColumns<PlatformConfig>[] = [
    {
      title: '平台信息',
      key: 'info',
      width: 200,
      render: (_, record) => {
        const platformInfo = platformTypeConfig[record.platform];
        return (
          <div className={styles.platformInfo}>
            <div className={styles.platformName}>
              <span className={styles.icon}>{platformInfo.icon}</span>
              {record.name}
            </div>
            <div className={styles.platformType}>
              <Tag color={platformInfo.color}>{platformInfo.name}</Tag>
              <Tag color={record.isEnabled ? 'success' : 'default'}>
                {record.isEnabled ? '启用' : '禁用'}
              </Tag>
            </div>
          </div>
        );
      },
    },
    {
      title: '连接状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <div className={styles.statusCell}>
          <Badge
            status={record.status === 'connected' ? 'success' : record.status === 'error' ? 'error' : 'default'}
            text={statusConfig[record.status].text}
          />
          {record.errorMessage && (
            <Tooltip title={record.errorMessage}>
              <ExclamationCircleOutlined className={styles.errorIcon} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '配置信息',
      key: 'config',
      width: 250,
      render: (_, record) => (
        <div className={styles.configInfo}>
          <div>App ID: {record.config.appId}</div>
          <div>API地址: {record.config.apiEndpoint}</div>
          {record.config.webhookUrl && (
            <div>Webhook: {record.config.webhookUrl}</div>
          )}
        </div>
      ),
    },
    {
      title: '同步信息',
      key: 'sync',
      width: 150,
      render: (_, record) => (
        <div className={styles.syncInfo}>
          {record.lastSyncTime ? (
            <>
              <div>最后同步:</div>
              <div>{record.lastSyncTime}</div>
            </>
          ) : (
            <div>未同步</div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 250,
      render: (_, record) => [
        <Button
          key="test"
          type="link"
          size="small"
          icon={<ApiOutlined />}
          loading={testLoading === record.id}
          onClick={() => handlePlatformAction('test', record)}
        >
          测试连接
        </Button>,
        <Button
          key="sync"
          type="link"
          size="small"
          icon={<SyncOutlined />}
          loading={syncLoading === record.id}
          onClick={() => handlePlatformAction('sync', record)}
          disabled={record.status !== 'connected'}
        >
          同步数据
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handlePlatformAction('edit', record)}
        >
          编辑
        </Button>,
        record.status === 'error' && (
          <Button
            key="refresh"
            type="link"
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => handlePlatformAction('refresh', record)}
          >
            刷新Token
          </Button>
        ),
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handlePlatformAction('delete', record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer
      header={{
        title: '平台配置',
        breadcrumb: {
          items: [
            { title: '消息通知', path: '/notification' },
            { title: '平台配置' },
          ],
        },
      }}
    >
      <div className={styles.container}>
        {/* 统计概览 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <ApiOutlined className={styles.statIcon} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>5</div>
                  <div className={styles.statLabel}>总平台数</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <CheckCircleOutlined className={styles.statIcon} style={{ color: '#52c41a' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>3</div>
                  <div className={styles.statLabel}>已连接</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <SyncOutlined className={styles.statIcon} style={{ color: '#1890ff' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>1,256</div>
                  <div className={styles.statLabel}>今日同步</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <div className={styles.statCard}>
                <ExclamationCircleOutlined className={styles.statIcon} style={{ color: '#faad14' }} />
                <div className={styles.statContent}>
                  <div className={styles.statValue}>1</div>
                  <div className={styles.statLabel}>异常平台</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 提示信息 */}
        <Alert
          message="平台配置说明"
          description="配置第三方平台API密钥后，系统将自动同步平台消息数据。请确保API密钥有效且具有相应权限。"
          type="info"
          showIcon
          closable
          className={styles.alertCard}
        />

        {/* 平台配置表格 */}
        <ProTable<PlatformConfig>
          actionRef={actionRef}
          rowKey="id"
          search={false}
          toolBarRender={() => [
            <Button
              key="create"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加平台
            </Button>,
          ]}
          request={async () => {
            return {
              data: mockPlatforms,
              success: true,
              total: mockPlatforms.length,
            };
          }}
          columns={columns}
          pagination={false}
        />

        {/* 创建/编辑平台配置模态框 */}
        <Modal
          title={selectedPlatform ? '编辑平台配置' : '添加平台配置'}
          open={createModalVisible}
          onOk={handleCreatePlatform}
          onCancel={() => {
            setCreateModalVisible(false);
            setSelectedPlatform(null);
            form.resetFields();
          }}
          width={600}
          destroyOnClose
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              isEnabled: true,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="platform"
                  label="平台类型"
                  rules={[{ required: true, message: '请选择平台类型' }]}
                >
                  <Select placeholder="选择平台类型" disabled={!!selectedPlatform}>
                    {Object.entries(platformTypeConfig).map(([key, config]) => (
                      <Option key={key} value={key}>
                        <Space>
                          <span>{config.icon}</span>
                          {config.name}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="配置名称"
                  rules={[{ required: true, message: '请输入配置名称' }]}
                >
                  <Input placeholder="输入配置名称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="appId"
                  label="App ID"
                  rules={[{ required: true, message: '请输入App ID' }]}
                >
                  <Input placeholder="输入App ID" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="appSecret"
                  label="App Secret"
                  rules={[{ required: true, message: '请输入App Secret' }]}
                >
                  <Password placeholder="输入App Secret" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="apiEndpoint"
              label="API地址"
              rules={[{ required: true, message: '请输入API地址' }]}
            >
              <Input placeholder="输入API地址" />
            </Form.Item>

            <Form.Item name="webhookUrl" label="Webhook地址">
              <Input placeholder="可选：输入Webhook地址" />
            </Form.Item>

            <Form.Item name="isEnabled" label="启用状态" valuePropName="checked">
              <Switch checkedChildren="启用" unCheckedChildren="禁用" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </PageContainer>
  );
};

export default PlatformManagement;
