import { Product } from '@/services/product';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Divider,
  Form,
  Input,
  Modal,
  Typography,
  Upload,
  message,
} from 'antd';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import React, { useEffect, useState } from 'react';

const { Text } = Typography;
const { TextArea } = Input;

interface ProductFormProps {
  visible: boolean;
  mode: 'create' | 'edit';
  initialValues?: Product;
  onCancel: () => void;
  onSubmit: (values: any) => void;
}

const ProductForm: React.FC<ProductFormProps> = ({
  visible,
  mode,
  initialValues,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [specKeys, setSpecKeys] = useState<string[]>([]);

  // 当初始值变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();

      // 处理规格信息
      if (initialValues?.specifications) {
        const keys = Object.keys(initialValues.specifications);
        setSpecKeys(keys);

        // 设置规格表单值
        const specValues: Record<string, string> = {};
        keys.forEach((key) => {
          specValues[`spec_${key}`] = initialValues.specifications[key];
        });

        form.setFieldsValue({
          ...initialValues,
          ...specValues,
        });
      } else {
        setSpecKeys([]);
      }

      // 处理图片信息
      if (initialValues?.images) {
        const uploadFiles = initialValues.images.map((img) => ({
          uid: `img-${img.id}`,
          name: img.alt || `图片${img.id}`,
          status: 'done' as const,
          url: img.src,
          thumbUrl: img.src,
          response: { id: img.id, src: img.src, alt: img.alt },
        }));
        setFileList(uploadFiles);
      } else {
        setFileList([]);
      }
    }
  }, [visible, initialValues, form]);

  // 处理表单提交
  const handleSubmit = () => {
    form.validateFields().then((values) => {
      // 处理规格信息
      const specifications: Record<string, string> = {};
      specKeys.forEach((key) => {
        if (values[`spec_${key}`]) {
          specifications[key] = values[`spec_${key}`];
          delete values[`spec_${key}`];
        }
      });

      // 处理图片信息
      const images = fileList.map((file, index) => {
        if (file.response) {
          return file.response;
        } else {
          return {
            id: index + 1,
            src: file.url || `/img/placeholder-${index + 1}.jpg`,
            alt: file.name || `图片${index + 1}`,
          };
        }
      });

      // 提交处理后的数据
      onSubmit({
        ...values,
        specifications,
        images,
      });
    });
  };

  // 添加规格字段
  const addSpecField = () => {
    const newKey = `规格${specKeys.length + 1}`;
    setSpecKeys([...specKeys, newKey]);
  };

  // 删除规格字段
  const removeSpecField = (key: string) => {
    setSpecKeys(specKeys.filter((k) => k !== key));
    form.setFieldsValue({ [`spec_${key}`]: undefined });
  };

  // 处理图片上传
  const handleUpload: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    // 在实际应用中，这里应该处理真实的文件上传
    // 这里我们只是模拟上传成功
    const processedFileList = newFileList.map((file, index) => {
      if (file.status === 'uploading' && !file.url) {
        // 模拟上传成功
        return {
          ...file,
          status: 'done',
          url: file.originFileObj
            ? URL.createObjectURL(file.originFileObj as Blob)
            : undefined,
          response: {
            id: Date.now() + index,
            src: file.url || `/img/placeholder-${Date.now() + index}.jpg`,
            alt: file.name || `图片${Date.now() + index}`,
          },
        };
      }
      return file;
    });

    setFileList(processedFileList);
  };

  // 上传前检查文件
  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于2MB!');
    }

    return isImage && isLt2M;
  };

  return (
    <Modal
      title={mode === 'create' ? '新增产品' : '编辑产品'}
      open={visible}
      width={800}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          保存
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" initialValues={initialValues}>
        <Form.Item
          name="title"
          label="产品名称"
          rules={[{ required: true, message: '请输入产品名称' }]}
        >
          <Input placeholder="请输入产品名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="产品描述"
          rules={[{ required: true, message: '请输入产品描述' }]}
        >
          <TextArea rows={4} placeholder="请输入产品描述" />
        </Form.Item>

        <Divider orientation="left">产品特性</Divider>
        <Form.List
          name="features"
          rules={[
            {
              validator: async (_, features) => {
                if (!features || features.length < 1) {
                  return Promise.reject(new Error('至少添加一个产品特性'));
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field) => (
                <Form.Item required={false} key={field.key}>
                  <Form.Item
                    {...field}
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: '请输入特性内容或删除此项',
                      },
                    ]}
                    noStyle
                  >
                    <Input
                      placeholder="请输入产品特性"
                      style={{ width: 'calc(100% - 32px)' }}
                    />
                  </Form.Item>
                  <MinusCircleOutlined
                    className="dynamic-delete-button"
                    onClick={() => remove(field.name)}
                    style={{ marginLeft: 8 }}
                  />
                </Form.Item>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  style={{ width: '100%' }}
                >
                  添加产品特性
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>

        <Divider orientation="left">应用场景</Divider>
        <Form.List
          name="applications"
          rules={[
            {
              validator: async (_, applications) => {
                if (!applications || applications.length < 1) {
                  return Promise.reject(new Error('至少添加一个应用场景'));
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field) => (
                <Form.Item required={false} key={field.key}>
                  <Form.Item
                    {...field}
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: '请输入应用场景或删除此项',
                      },
                    ]}
                    noStyle
                  >
                    <Input
                      placeholder="请输入应用场景"
                      style={{ width: 'calc(100% - 32px)' }}
                    />
                  </Form.Item>
                  <MinusCircleOutlined
                    className="dynamic-delete-button"
                    onClick={() => remove(field.name)}
                    style={{ marginLeft: 8 }}
                  />
                </Form.Item>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  style={{ width: '100%' }}
                >
                  添加应用场景
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>

        <Divider orientation="left">产品规格</Divider>
        {specKeys.map((key) => (
          <div key={key} style={{ display: 'flex', marginBottom: 16 }}>
            <Input
              style={{ width: 150, marginRight: 8 }}
              value={key}
              disabled
              placeholder="规格名称"
            />
            <Form.Item
              name={`spec_${key}`}
              style={{ marginBottom: 0, flex: 1 }}
              rules={[{ required: true, message: '请输入规格值' }]}
            >
              <Input placeholder="规格值" />
            </Form.Item>
            <Button
              type="text"
              icon={<MinusCircleOutlined />}
              onClick={() => removeSpecField(key)}
              style={{ marginLeft: 8 }}
            />
          </div>
        ))}
        <Form.Item>
          <Button
            type="dashed"
            onClick={addSpecField}
            icon={<PlusOutlined />}
            style={{ width: '100%' }}
          >
            添加规格
          </Button>
        </Form.Item>

        <Divider orientation="left">产品图片</Divider>
        <Form.Item>
          <Upload
            listType="picture-card"
            fileList={fileList}
            onChange={handleUpload}
            beforeUpload={beforeUpload}
            customRequest={({ onSuccess }) => {
              // 模拟上传成功
              setTimeout(() => {
                onSuccess?.('ok');
              }, 0);
            }}
          >
            {fileList.length >= 8 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            )}
          </Upload>
          <Text type="secondary">
            支持 .jpg, .jpeg, .png 格式，单张图片不超过2MB，最多上传8张图片
          </Text>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ProductForm;
