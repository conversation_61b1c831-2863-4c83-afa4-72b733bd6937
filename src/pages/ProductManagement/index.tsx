import productService, { Product } from '@/services/product';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Grid,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tag,
  Typography,
} from 'antd';
import type { ColumnType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import ProductDetail from './components/ProductDetail';
import ProductForm from './components/ProductForm';

const { Title } = Typography;

const ProductManagement: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [currentProduct, setCurrentProduct] = useState<Product | undefined>(
    undefined,
  );
  const [formVisible, setFormVisible] = useState<boolean>(false);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // 加载产品列表
  const loadProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList();
      if (response.status === 200) {
        setProducts(response.data);
      }
    } catch (error) {
      console.error('加载产品列表失败:', error);
      message.error('加载产品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadProducts();
  }, []);

  // 查看产品详情
  const handleViewProduct = async (id: number) => {
    try {
      const response = await productService.getProductDetail(id);
      if (response.status === 200) {
        setCurrentProduct(response.data);
        setDetailVisible(true);
      }
    } catch (error) {
      console.error('获取产品详情失败:', error);
      message.error('获取产品详情失败');
    }
  };

  // 创建产品
  const handleCreateProduct = () => {
    setCurrentProduct(undefined);
    setFormMode('create');
    setFormVisible(true);
  };

  // 编辑产品
  const handleEditProduct = async (id: number) => {
    try {
      const response = await productService.getProductDetail(id);
      if (response.status === 200) {
        setCurrentProduct(response.data);
        setFormMode('edit');
        setFormVisible(true);
      }
    } catch (error) {
      console.error('获取产品详情失败:', error);
      message.error('获取产品详情失败');
    }
  };

  // 删除产品
  const handleDeleteProduct = async (id: number) => {
    try {
      const response = await productService.deleteProduct(id);
      if (response.status === 200) {
        message.success('删除产品成功');
        loadProducts();
      }
    } catch (error) {
      console.error('删除产品失败:', error);
      message.error('删除产品失败');
    }
  };

  // 保存产品（创建或更新）
  const handleSaveProduct = async (values: any) => {
    try {
      if (formMode === 'create') {
        await productService.createProduct(values);
        message.success('创建产品成功');
      } else {
        if (currentProduct) {
          await productService.updateProduct(currentProduct.id, values);
          message.success('更新产品成功');
        }
      }
      setFormVisible(false);
      loadProducts();
    } catch (error) {
      console.error('保存产品失败:', error);
      message.error('保存产品失败');
    }
  };

  // 响应式断点
  const { md } = Grid.useBreakpoint();

  // 表格列定义 - 响应式调整
  const columns: ColumnType<Product>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '产品名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 300,
      responsive: ['md'],
    },
    {
      title: '特性',
      dataIndex: 'features',
      key: 'features',
      render: (features: string[]) => (
        <span>
          {features.slice(0, 2).map((feature, index) => (
            <Tag color="blue" key={index}>
              {feature.length > 20 ? `${feature.slice(0, 20)}...` : feature}
            </Tag>
          ))}
          {features.length > 2 && (
            <Tag color="blue">+{features.length - 2}</Tag>
          )}
        </span>
      ),
      responsive: ['md'],
    },
    {
      title: '应用场景',
      dataIndex: 'applications',
      key: 'applications',
      render: (applications: string[]) => (
        <span>
          {applications.slice(0, 2).map((app, index) => (
            <Tag color="green" key={index}>
              {app.length > 20 ? `${app.slice(0, 20)}...` : app}
            </Tag>
          ))}
          {applications.length > 2 && (
            <Tag color="green">+{applications.length - 2}</Tag>
          )}
        </span>
      ),
      responsive: ['md'],
    },
    {
      title: '图片数量',
      dataIndex: 'images',
      key: 'images',
      render: (images: any[]) => images.length,
      responsive: ['md'],
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: md ? 200 : 150,
      render: (_: any, record: Product) => (
        <div style={{ maxWidth: '100%', overflow: 'hidden' }}>
          <Space
            size="middle"
            direction={md ? 'horizontal' : 'vertical'}
            style={{
              flexWrap: 'wrap',
              rowGap: 8,
              maxWidth: '100%',
            }}
          >
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewProduct(record.id)}
              block={!md}
              style={{ minWidth: 60 }}
            >
              查看
            </Button>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditProduct(record.id)}
              block={!md}
              style={{ minWidth: 60 }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个产品吗？"
              onConfirm={() => handleDeleteProduct(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                block={!md}
                style={{ minWidth: 60 }}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '产品管理',
        subTitle: '管理前台展示的产品信息',
        style: { paddingBottom: md ? 24 : 12 },
      }}
      extra={[
        <Button
          key="add"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateProduct}
          block={!md}
        >
          新增产品
        </Button>,
      ]}
    >
      <Card>
        <Title level={4}>产品列表</Title>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={products}
            rowKey="id"
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        </Spin>
      </Card>

      {/* 产品表单（新增/编辑） */}
      <ProductForm
        visible={formVisible}
        mode={formMode}
        initialValues={currentProduct}
        onCancel={() => setFormVisible(false)}
        onSubmit={handleSaveProduct}
      />

      {/* 产品详情 */}
      <ProductDetail
        visible={detailVisible}
        product={currentProduct}
        onClose={() => setDetailVisible(false)}
      />
    </PageContainer>
  );
};

export default ProductManagement;
