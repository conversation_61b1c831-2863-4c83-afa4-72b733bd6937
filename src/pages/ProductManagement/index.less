.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.formItem {
  margin-bottom: 24px;
}

.dynamicDeleteButton {
  position: relative;
  top: 4px;
  margin: 0 8px;
  color: #999;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.dynamicDeleteButton:hover {
  color: #777;
}

.dynamicDeleteButton[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.uploadList {
  display: flex;
  flex-wrap: wrap;
}

.uploadItem {
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
  position: relative;
}

.uploadItemImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.uploadItemActions {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 50%);
  padding: 4px;
  border-radius: 0 0 0 4px;
}

.specificationItem {
  display: flex;
  margin-bottom: 16px;
}

.specificationLabel {
  width: 150px;
  margin-right: 8px;
}

.specificationValue {
  flex: 1;
}

.specificationAction {
  margin-left: 8px;
}
