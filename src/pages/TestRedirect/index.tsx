/**
 * 测试重定向页面
 * 用于验证路由和重定向逻辑
 */
import { getUserInfo, isLoggedIn, logout } from '@/utils/auth';
import { history } from '@umijs/max';
import { Button, Card, Divider, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text, Paragraph } = Typography;

const TestRedirect: React.FC = () => {
  const [loginStatus, setLoginStatus] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<any>(null);

  const checkStatus = () => {
    const loggedIn = isLoggedIn();
    const info = getUserInfo();
    setLoginStatus(loggedIn);
    setUserInfo(info);

    console.group('[Test Redirect Debug]');
    console.log('登录状态:', loggedIn);
    console.log('用户信息:', info);
    console.log('当前路径:', window.location.pathname);
    console.groupEnd();
  };

  useEffect(() => {
    checkStatus();
  }, []);

  const handleLogin = () => {
    // 模拟登录
    localStorage.setItem('front_logix_token', 'test-token');
    localStorage.setItem(
      'userInfo',
      JSON.stringify({
        name: 'Test User',
        username: 'testuser',
        isAdmin: false,
        roles: ['user'],
        userType: 'user',
      }),
    );
    checkStatus();
  };

  const handleLogout = () => {
    logout();
    checkStatus();
  };

  const handleGoToRoot = () => {
    history.push('/');
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Title level={2}>🧪 路由重定向测试页面</Title>

      <Card title="当前状态" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>
            <strong>登录状态:</strong> {loginStatus ? '✅ 已登录' : '❌ 未登录'}
          </Text>
          <Text>
            <strong>当前路径:</strong> {window.location.pathname}
          </Text>
          {userInfo && (
            <div>
              <Text>
                <strong>用户信息:</strong>
              </Text>
              <pre
                style={{ background: '#f5f5f5', padding: 8, borderRadius: 4 }}
              >
                {JSON.stringify(userInfo, null, 2)}
              </pre>
            </div>
          )}
        </Space>
      </Card>

      <Card title="测试操作" style={{ marginBottom: 16 }}>
        <Space wrap>
          <Button onClick={checkStatus}>刷新状态</Button>
          <Button type="primary" onClick={handleLogin} disabled={loginStatus}>
            模拟登录
          </Button>
          <Button onClick={handleLogout} disabled={!loginStatus}>
            登出
          </Button>
          <Button type="dashed" onClick={handleGoToRoot}>
            跳转到根路径 (/)
          </Button>
        </Space>
      </Card>

      <Card title="测试说明">
        <Paragraph>这个页面用于测试路由重定向逻辑：</Paragraph>
        <ul>
          <li>点击&ldquo;模拟登录&rdquo;设置登录状态</li>
          <li>点击&ldquo;跳转到根路径&rdquo;测试重定向逻辑</li>
          <li>预期行为：未登录时跳转到 /login，已登录时跳转到 /home</li>
        </ul>

        <Divider />

        <Paragraph>
          <Text strong>访问地址:</Text>
        </Paragraph>
        <ul>
          <li>
            <a href="http://localhost:8000/" target="_blank" rel="noreferrer">
              根路径 (/)
            </a>
          </li>
          <li>
            <a
              href="http://localhost:8000/login"
              target="_blank"
              rel="noreferrer"
            >
              登录页 (/login)
            </a>
          </li>
          <li>
            <a
              href="http://localhost:8000/home"
              target="_blank"
              rel="noreferrer"
            >
              首页 (/home)
            </a>
          </li>
          <li>
            <a
              href="http://localhost:8000/test-redirect"
              target="_blank"
              rel="noreferrer"
            >
              测试页 (/test-redirect)
            </a>
          </li>
        </ul>
      </Card>
    </div>
  );
};

export default TestRedirect;
