import Guide from '@/components/Guide';
import Header from '@/components/Header';
import { trim } from '@/utils/format';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card } from 'antd';
import { Area, Bar, Pie } from '@ant-design/charts';
import styles from './index.less';

const Dashboard = () => {
  // 用户趋势数据 (7天)
  const userTrendData = [
    { date: '2023-01-01', value: 12345, type: 'DAU' },
    { date: '2023-01-02', value: 12567, type: 'DAU' },
    { date: '2023-01-03', value: 11890, type: 'DAU' },
    { date: '2023-01-04', value: 13210, type: 'DAU' },
    { date: '2023-01-05', value: 14230, type: 'DAU' },
    { date: '2023-01-06', value: 13890, type: 'DAU' },
    { date: '2023-01-07', value: 14560, type: 'DAU' },
    { date: '2023-01-01', value: 256789, type: 'MAU' },
  ];

  // 访问数据对比
  const trafficData = [
    { type: 'PV', value: 45678 },
    { type: 'UV', value: 23456 },
  ];

  // 地区分布数据
  const regionData = [
    { region: '北京', value: 5432 },
    { region: '上海', value: 4321 },
    { region: '广州', value: 3456 },
    { region: '深圳', value: 3210 },
  ];

  // 订单状态数据
  const orderData = [
    { status: '新增订单', value: 1234 },
    { status: '待处理订单', value: 567 },
    { status: '发货中订单', value: 890 },
    { status: '已完成订单', value: 4321 },
  ];

  // 用户趋势图配置
  const userTrendConfig = {
    data: userTrendData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    color: ['#1890ff', '#13c2c2'],
    areaStyle: { fillOpacity: 0.3 },
    point: { size: 4, shape: 'circle' },
    smooth: true,
    legend: { position: 'top' },
  };

  // 访问数据配置
  const trafficConfig = {
    data: trafficData,
    xField: 'value',
    yField: 'type',
    color: ({ type}) => {
      return type === 'PV' ? '#1890ff' : '#13c2c2';
    },
    legend: false,
    label: {
      position: 'middle',
      style: { fill: '#fff' },
    },
  };

  // 地区分布配置
  const regionConfig = {
    data: regionData,
    angleField: 'value',
    colorField: 'region',
    radius: 0.7,
    label: {
      type: 'spider',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 订单状态配置
  const orderConfig = {
    data: orderData,
    xField: 'value',
    yField: 'status',
    color: '#1890ff',
    label: {
      position: 'middle',
      style: { fill: '#fff' },
    },
  };

  return (
    <div className={styles.dashboard}>
      <Card title="用户趋势" className={styles.card}>
        <Area {...userTrendConfig} />
      </Card>

      <Card title="访问数据对比" className={styles.card}>
        <Bar {...trafficConfig} />
      </Card>

      <Card title="地区分布" className={styles.card}>
        <Pie {...regionConfig} />
      </Card>

      <Card title="订单状态" className={styles.card}>
        <Bar {...orderConfig} />
      </Card>
    </div>
  );
};

const HomePage: React.FC = () => {
  const { name } = useModel<{ name: string }>('global');
  return (
    <>
      <Header />
      <PageContainer ghost>
        <div className={styles.container}>
          <Dashboard />
          <Guide name={trim(name as string)} />
        </div>
      </PageContainer>
    </>
  );
};

export default HomePage;
