.container {
  padding-top: 80px;
}

.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;

  .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    height: 400px;

    :global {
      .ant-card-head {
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
      }

      .ant-card-body {
        padding: 16px;
        height: calc(100% - 57px);

        .g2-html-annotation {
          display: none;
        }
 }

      .g2-chart {
        height: 100% !important;
      }
    }
  }
}
