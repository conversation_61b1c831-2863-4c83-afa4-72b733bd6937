import {
  CarOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  SafetyCertificateOutlined,
  StarOutlined,
} from '@ant-design/icons';
import {
  Checkbox,
  Col,
  Divider,
  InputNumber,
  Radio,
  Rate,
  Row,
  Slider,
  Space,
  Typography,
} from 'antd';
import React from 'react';

const { Text } = Typography;
const { Group } = Checkbox;

interface FilterConditions {
  urgency: 'all' | 'immediate' | 'scheduled';
  transportMethod: 'all' | 'self-pickup' | 'delivery';
  certifications: string[];
  priceRange: [number, number];
  rating: number;
}

interface FilterPanelProps {
  filterConditions: FilterConditions;
  onChange: (newFilters: Partial<FilterConditions>) => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filterConditions,
  onChange,
}) => {
  // 处理紧急程度变化
  const handleUrgencyChange = (e: any) => {
    onChange({ urgency: e.target.value });
  };

  // 处理运输方式变化
  const handleTransportMethodChange = (e: any) => {
    onChange({ transportMethod: e.target.value });
  };

  // 处理认证变化
  const handleCertificationsChange = (checkedValues: string[]) => {
    onChange({ certifications: checkedValues });
  };

  // 处理价格范围变化
  const handlePriceRangeChange = (value: [number, number]) => {
    onChange({ priceRange: value });
  };

  // 处理评分变化
  const handleRatingChange = (value: number) => {
    onChange({ rating: value });
  };

  return (
    <div>
      <div>
        <Text strong style={{ fontSize: 16 }}>
          <ClockCircleOutlined /> 紧急程度
        </Text>
        <Radio.Group
          value={filterConditions.urgency}
          onChange={handleUrgencyChange}
          style={{ marginTop: 8, display: 'block' }}
        >
          <Space direction="vertical">
            <Radio value="all">全部</Radio>
            <Radio value="immediate">可立即提货</Radio>
            <Radio value="scheduled">需预定</Radio>
          </Space>
        </Radio.Group>
      </div>

      <Divider />

      <div>
        <Text strong style={{ fontSize: 16 }}>
          <CarOutlined /> 运输方式
        </Text>
        <Radio.Group
          value={filterConditions.transportMethod}
          onChange={handleTransportMethodChange}
          style={{ marginTop: 8, display: 'block' }}
        >
          <Space direction="vertical">
            <Radio value="all">全部</Radio>
            <Radio value="self-pickup">自提</Radio>
            <Radio value="delivery">冷链配送</Radio>
          </Space>
        </Radio.Group>
      </div>

      <Divider />

      <div>
        <Text strong style={{ fontSize: 16 }}>
          <SafetyCertificateOutlined /> 认证标识
        </Text>
        <Group
          options={[
            { label: '食品级认证', value: 'food' },
            { label: '工业标准认证', value: 'industrial' },
            { label: '医用级认证', value: 'medical' },
            { label: 'ISO9001认证', value: 'iso9001' },
            { label: 'HACCP认证', value: 'haccp' },
          ]}
          value={filterConditions.certifications}
          onChange={handleCertificationsChange}
          style={{ marginTop: 8, display: 'block' }}
        />
      </div>

      <Divider />

      <div>
        <Text strong style={{ fontSize: 16 }}>
          <DollarOutlined /> 价格范围 (元/吨)
        </Text>
        <div style={{ marginTop: 16 }}>
          <Row gutter={8}>
            <Col span={10}>
              <InputNumber
                min={0}
                max={filterConditions.priceRange[1]}
                value={filterConditions.priceRange[0]}
                onChange={(value) => {
                  if (value !== null) {
                    onChange({
                      priceRange: [value, filterConditions.priceRange[1]],
                    });
                  }
                }}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={4} style={{ textAlign: 'center' }}>
              <Text>至</Text>
            </Col>
            <Col span={10}>
              <InputNumber
                min={filterConditions.priceRange[0]}
                max={10000}
                value={filterConditions.priceRange[1]}
                onChange={(value) => {
                  if (value !== null) {
                    onChange({
                      priceRange: [filterConditions.priceRange[0], value],
                    });
                  }
                }}
                style={{ width: '100%' }}
              />
            </Col>
          </Row>
          <Slider
            range
            min={0}
            max={1000}
            value={filterConditions.priceRange}
            onChange={handlePriceRangeChange}
            style={{ marginTop: 16 }}
          />
        </div>
      </div>

      <Divider />

      <div>
        <Text strong style={{ fontSize: 16 }}>
          <StarOutlined /> 最低评分
        </Text>
        <div style={{ marginTop: 8 }}>
          <Rate
            allowHalf
            value={filterConditions.rating}
            onChange={handleRatingChange}
          />
          <Text style={{ marginLeft: 8 }}>
            {filterConditions.rating
              ? `${filterConditions.rating} 星及以上`
              : '不限'}
          </Text>
        </div>
      </div>

      <Divider />

      <div>
        <Text strong style={{ fontSize: 16 }}>
          更多筛选
        </Text>
        <Group
          options={[
            { label: '只显示有库存', value: 'inStock' },
            { label: '只显示促销商品', value: 'promotion' },
            { label: '只显示新品', value: 'new' },
            { label: '只显示认证供应商', value: 'certified' },
            { label: '排除黑名单供应商', value: 'excludeBlacklist' },
          ]}
          defaultValue={['excludeBlacklist']}
          style={{ marginTop: 8, display: 'block' }}
        />
      </div>
    </div>
  );
};

export default FilterPanel;
