import { EnhancedIceType } from '@/services/iceOrder/types/enhanced';
import {
  ClockCircleOutlined,
  EyeOutlined,
  SafetyCertificateOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { Button, Card, Tag, Typography } from 'antd';
import React from 'react';

const { Meta } = Card;
const { Text, Paragraph } = Typography;

interface ProductCardProps {
  product: EnhancedIceType;
  onViewDetail: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onViewDetail }) => {
  // 获取产品类别标签颜色
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'food':
        return 'green';
      case 'industrial':
        return 'blue';
      case 'cooling':
        return 'orange';
      default:
        return 'default';
    }
  };

  // 获取产品类别名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'food':
        return '食用冰';
      case 'industrial':
        return '工业冰';
      case 'cooling':
        return '降温冰';
      default:
        return '未知类型';
    }
  };

  // 随机生成库存状态
  const getStockStatus = () => {
    const random = Math.random();
    if (random < 0.7) {
      return { status: 'in-stock', text: '有库存', color: 'green' };
    } else if (random < 0.9) {
      return { status: 'low-stock', text: '库存不足', color: 'orange' };
    } else {
      return { status: 'out-of-stock', text: '无库存', color: 'red' };
    }
  };

  // 随机生成价格
  const getPrice = () => {
    const basePrice =
      product.category === 'food'
        ? 100
        : product.category === 'industrial'
        ? 200
        : 150;
    return Math.round(basePrice * (0.9 + Math.random() * 0.2));
  };

  const stockStatus = getStockStatus();
  const price = getPrice();

  return (
    <Card
      hoverable
      cover={
        <div style={{ height: 160, overflow: 'hidden', position: 'relative' }}>
          <img
            alt={product.name}
            src={
              product.images && product.images.length > 0
                ? product.images[0]
                : 'https://via.placeholder.com/300x200?text=No+Image'
            }
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
          <Tag
            color={getCategoryColor(product.category)}
            style={{ position: 'absolute', top: 8, right: 8 }}
          >
            {getCategoryName(product.category)}
          </Tag>
        </div>
      }
      actions={[
        <span key="detail">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={onViewDetail}
            title="查看详情"
          >
            详情
          </Button>
        </span>,
        <span key="buy">
          <Button
            type="text"
            icon={<ShoppingCartOutlined />}
            disabled={stockStatus.status === 'out-of-stock'}
            title="加入购物车"
          >
            购买
          </Button>
        </span>,
      ]}
    >
      <Meta
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text ellipsis style={{ maxWidth: '70%' }} title={product.name}>
              {product.name}
            </Text>
            <Text type="danger" strong>
              ¥{price}/吨
            </Text>
          </div>
        }
        description={
          <>
            <Paragraph
              type="secondary"
              ellipsis={{
                rows: 2,
                expandable: false,
                symbol: '更多',
              }}
              style={{
                margin: 0,
                fontSize: 14,
                display: 'block', // 新增样式修复
              }}
              title={product.description}
            >
              {product.description}
            </Paragraph>
            <div style={{ marginTop: 8 }}>
              <Tag color={stockStatus.color}>{stockStatus.text}</Tag>
              {stockStatus.status === 'in-stock' && (
                <Tag icon={<ClockCircleOutlined />} color="blue">
                  可立即提货
                </Tag>
              )}
              {product.certifications &&
                product.certifications.map((cert, index) => (
                  <Tag key={index} icon={<SafetyCertificateOutlined />}>
                    {cert}
                  </Tag>
                ))}
            </div>
          </>
        }
      />
    </Card>
  );
};

export default ProductCard;
