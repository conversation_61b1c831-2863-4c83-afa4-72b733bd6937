import { Supplier } from '@/services/iceOrder/types/enhanced';
import {
  ClockCircleOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SafetyCertificateOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Progress,
  Rate,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import React from 'react';

const { Meta } = Card;
const { Text, Paragraph } = Typography;

interface SupplierCardProps {
  supplier: Supplier;
  onViewDetail: () => void;
}

const SupplierCard: React.FC<SupplierCardProps> = ({
  supplier,
  onViewDetail,
}) => {
  // 获取信用评分颜色
  const getCreditScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a'; // 绿色
    if (score >= 60) return '#faad14'; // 黄色
    return '#f5222d'; // 红色
  };

  // 获取准时交付率颜色
  const getDeliveryRateColor = (rate: number) => {
    if (rate >= 90) return '#52c41a'; // 绿色
    if (rate >= 80) return '#faad14'; // 黄色
    return '#f5222d'; // 红色
  };

  // 随机生成可提供的产品类型（实际项目中应该从API获取）
  const getProductTypes = () => {
    const types = ['食用冰', '工业冰', '降温冰'];
    const count = Math.floor(Math.random() * 3) + 1;
    return types.slice(0, count);
  };

  const productTypes = getProductTypes();

  return (
    <Card
      hoverable
      actions={[
        <Tooltip title="查看详情" key="view-detail">
          <Button type="text" icon={<EyeOutlined />} onClick={onViewDetail}>
            详情
          </Button>
        </Tooltip>,
        <Tooltip title="查看产品" key="view-product">
          <Button type="text" icon={<ShoppingCartOutlined />}>
            产品
          </Button>
        </Tooltip>,
      ]}
    >
      <Meta
        title={
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text ellipsis style={{ maxWidth: '70%' }} title={supplier.name}>
              {supplier.name}
            </Text>
            {supplier.blacklisted ? (
              <Tag color="red">黑名单</Tag>
            ) : (
              <Rate
                disabled
                defaultValue={supplier.starRating}
                allowHalf
                style={{ fontSize: 14 }}
              />
            )}
          </div>
        }
        description={
          <>
            <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: 8 }}>
              {supplier.description}
            </Paragraph>

            <Space direction="vertical" size={8} style={{ width: '100%' }}>
              <div>
                <Text type="secondary">
                  <EnvironmentOutlined style={{ marginRight: 4 }} />
                  {supplier.address
                    ? `${supplier.address.province} ${supplier.address.city}`
                    : '地址未提供'}
                </Text>
              </div>

              <div>
                <Text type="secondary">
                  <PhoneOutlined style={{ marginRight: 4 }} />
                  {supplier.contactPerson
                    ? `${supplier.contactPerson}: ${
                        supplier.contactPhone || '未提供'
                      }`
                    : '联系人未提供'}
                </Text>
              </div>

              <div>
                <Text type="secondary">
                  <MailOutlined style={{ marginRight: 4 }} />
                  {supplier.email || '邮箱未提供'}
                </Text>
              </div>

              <div>
                <Text strong>信用评分:</Text>
                <Tooltip title="信用评分越高，供应商越可靠">
                  <InfoCircleOutlined
                    style={{ marginLeft: 4, marginRight: 8 }}
                  />
                </Tooltip>
                <Progress
                  percent={supplier.creditScore || 0}
                  size="small"
                  status={
                    supplier.creditScore && supplier.creditScore < 60
                      ? 'exception'
                      : 'normal'
                  }
                  strokeColor={getCreditScoreColor(supplier.creditScore || 0)}
                  format={(percent) => `${percent || 0}分`}
                />
              </div>

              <div>
                <Text strong>准时交付率:</Text>
                <Tooltip title="过去30天内准时交付的订单比例">
                  <InfoCircleOutlined
                    style={{ marginLeft: 4, marginRight: 8 }}
                  />
                </Tooltip>
                <Progress
                  percent={supplier.onTimeDeliveryRate || 0}
                  size="small"
                  status={
                    supplier.onTimeDeliveryRate &&
                    supplier.onTimeDeliveryRate < 80
                      ? 'exception'
                      : 'normal'
                  }
                  strokeColor={getDeliveryRateColor(
                    supplier.onTimeDeliveryRate || 0,
                  )}
                />
              </div>

              <div>
                <Space wrap>
                  {productTypes.map((type, index) => (
                    <Tag
                      key={index}
                      color={
                        type === '食用冰'
                          ? 'green'
                          : type === '工业冰'
                          ? 'blue'
                          : 'orange'
                      }
                    >
                      {type}
                    </Tag>
                  ))}
                  <Tag icon={<ClockCircleOutlined />} color="blue">
                    可立即发货
                  </Tag>
                  <Tag icon={<SafetyCertificateOutlined />}>已认证</Tag>
                </Space>
              </div>
            </Space>
          </>
        }
      />
    </Card>
  );
};

export default SupplierCard;
