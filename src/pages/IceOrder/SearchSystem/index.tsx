/**
 * 智能检索系统页面
 * 多维度筛选冰块产品和供应商，包括紧急程度、运输方式和认证标识等
 */
import {
  enhancedIceTypeService,
  supplierService,
} from '@/services/iceOrder/enhanced';
import { EnhancedIceType, Supplier } from '@/services/iceOrder/types/enhanced';
import {
  CarOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  FilterOutlined,
  InfoCircleOutlined,
  SafetyCertificateOutlined,
  SearchOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Drawer,
  Empty,
  Input,
  List,
  Rate,
  Row,
  Space,
  Spin,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import FilterPanel from './components/FilterPanel';
import ProductCard from './components/ProductCard';
import SupplierCard from './components/SupplierCard';

const { Search } = Input;
const { Text } = Typography;
const { TabPane } = Tabs;

/**
 * 搜索结果类型定义
 * 包含产品列表、供应商列表和加载状态
 */
interface SearchResult {
  products: EnhancedIceType[]; // 产品列表
  suppliers: Supplier[]; // 供应商列表
  loading: boolean; // 加载状态
}

/**
 * 过滤条件类型定义
 * 定义了各种过滤条件的类型
 */
interface FilterConditions {
  urgency: 'all' | 'immediate' | 'scheduled'; // 紧急程度
  transportMethod: 'all' | 'self-pickup' | 'delivery'; // 运输方式
  certifications: string[]; // 认证标识
  priceRange: [number, number]; // 价格范围
  rating: number; // 评分
}

const SearchSystem: React.FC = () => {
  // 状态管理
  const [searchText, setSearchText] = useState<string>(''); // 搜索文本
  const [searchResults, setSearchResults] = useState<SearchResult>({
    // 搜索结果
    products: [],
    suppliers: [],
    loading: false,
  });
  const [filterVisible, setFilterVisible] = useState<boolean>(false); // 过滤面板是否可见
  const [filterConditions, setFilterConditions] = useState<FilterConditions>({
    // 过滤条件
    urgency: 'all',
    transportMethod: 'all',
    certifications: [],
    priceRange: [0, 1000],
    rating: 0,
  });
  const [activeTab, setActiveTab] = useState<string>('products'); // 当前激活的标签页
  const [selectedProduct, setSelectedProduct] =
    useState<EnhancedIceType | null>(null); // 选中的产品
  const [productDetailVisible, setProductDetailVisible] =
    useState<boolean>(false); // 产品详情是否可见
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(
    null,
  ); // 选中的供应商
  const [supplierDetailVisible, setSupplierDetailVisible] =
    useState<boolean>(false); // 供应商详情是否可见

  /**
   * 加载初始数据
   * 从服务器获取产品和供应商数据，如果失败则使用模拟数据
   */
  const loadInitialData = async () => {
    setSearchResults((prev) => ({ ...prev, loading: true }));
    try {
      // 加载产品数据
      const productsResponse = await enhancedIceTypeService
        .getEnhancedIceTypeList()
        .catch(() => null);

      // 加载供应商数据
      const suppliersResponse = await supplierService
        .getSupplierList()
        .catch(() => null);

      // 检查响应是否有效
      const productsData =
        productsResponse?.data?.data || productsResponse?.data || [];
      const suppliersData =
        suppliersResponse?.data?.data || suppliersResponse?.data || [];

      if (productsResponse && suppliersResponse) {
        setSearchResults({
          products: productsData,
          suppliers: suppliersData,
          loading: false,
        });
      }
    } catch (error) {
      console.error('加载初始数据失败:', error);

      // 使用模拟数据
      const mockProducts = [
        {
          id: 1,
          name: '方块冰',
          description: '标准方形冰块，适合各种饮品',
          category: 'food',
          specifications: {
            shape: '方形',
            size: '2x2x2cm',
            weight: '20g/块',
            temperatureRange: {
              min: -10,
              max: 0,
            },
            duration: 2,
          },
          certifications: ['食品级认证'],
          images: ['https://example.com/ice1.jpg'],
        },
        {
          id: 2,
          name: '碎冰',
          description: '小颗粒碎冰，适合刨冰和冰沙',
          category: 'food',
          specifications: {
            shape: '不规则',
            size: '0.5-1cm',
            weight: '5g/块',
            temperatureRange: {
              min: -8,
              max: 0,
            },
            duration: 1,
          },
          certifications: ['食品级认证'],
          images: ['https://example.com/ice2.jpg'],
        },
        {
          id: 3,
          name: '工业冰块',
          description: '大型工业冰块，适合工业降温',
          category: 'industrial',
          specifications: {
            shape: '方形',
            size: '10x10x10cm',
            weight: '1kg/块',
            temperatureRange: {
              min: -15,
              max: -5,
            },
            duration: 8,
          },
          certifications: ['工业标准认证'],
          images: ['https://example.com/ice3.jpg'],
        },
        {
          id: 4,
          name: '降温冰袋',
          description: '专业降温冰袋，适合运输和保存',
          category: 'cooling',
          specifications: {
            shape: '袋状',
            size: '20x30cm',
            weight: '500g/袋',
            temperatureRange: {
              min: -20,
              max: -10,
            },
            duration: 12,
          },
          certifications: ['工业标准认证', '医用级认证'],
          images: ['https://example.com/ice4.jpg'],
        },
      ];

      const mockSuppliers = [
        {
          id: 1,
          factoryId: 1,
          name: '北冰洋冰业',
          description: '专业生产各类食用冰和工业冰',
          contactPerson: '张经理',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '建国路88号',
          },
          businessLicense: '**********',
          creditScore: 85,
          transactionVolume: 5000,
          starRating: 4.5,
          onTimeDeliveryRate: 95,
          products: [1, 2, 3],
          blacklisted: false,
          createdAt: '2023-01-15T08:30:00',
        },
        {
          id: 2,
          factoryId: 2,
          name: '上海冷冻厂',
          description: '上海地区最大的工业冰生产商',
          contactPerson: '李经理',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '上海市',
            city: '上海市',
            district: '浦东新区',
            detail: '陆家嘴1号',
          },
          businessLicense: '**********',
          creditScore: 92,
          transactionVolume: 8000,
          starRating: 4.8,
          onTimeDeliveryRate: 98,
          products: [1, 4, 5],
          blacklisted: false,
          createdAt: '2023-02-20T10:15:00',
        },
      ];

      setSearchResults({
        products: mockProducts as EnhancedIceType[],
        suppliers: mockSuppliers as Supplier[],
        loading: false,
      });
    }
  };

  /**
   * 页面初始化时加载初始数据
   */
  useEffect(() => {
    loadInitialData();
  }, []);

  /**
   * 处理搜索
   * 根据搜索文本过滤产品和供应商列表
   * @param value 搜索文本
   */
  const handleSearch = (value: string) => {
    setSearchText(value);
    // 实际项目中，这里应该调用API进行搜索
    // 这里简单模拟搜索结果
    setSearchResults((prev) => ({
      ...prev,
      products: prev.products.filter(
        (product) =>
          product.name.toLowerCase().includes(value.toLowerCase()) ||
          product.description.toLowerCase().includes(value.toLowerCase()),
      ),
      suppliers: prev.suppliers.filter(
        (supplier) =>
          supplier.name.toLowerCase().includes(value.toLowerCase()) ||
          supplier.description.toLowerCase().includes(value.toLowerCase()),
      ),
    }));
  };

  /**
   * 处理过滤条件变化
   * 更新过滤条件状态
   * @param newFilters 新的过滤条件
   */
  const handleFilterChange = (newFilters: Partial<FilterConditions>) => {
    setFilterConditions((prev) => ({ ...prev, ...newFilters }));
  };

  /**
   * 应用过滤条件
   * 关闭过滤面板并重新加载数据
   */
  const applyFilters = () => {
    setFilterVisible(false);
    // 实际项目中，这里应该调用API进行过滤
    // 这里简单模拟过滤结果
    loadInitialData();
  };

  /**
   * 重置过滤条件
   * 将过滤条件重置为默认值
   */
  const resetFilters = () => {
    setFilterConditions({
      urgency: 'all',
      transportMethod: 'all',
      certifications: [],
      priceRange: [0, 1000],
      rating: 0,
    });
  };

  /**
   * 查看产品详情
   * 设置选中产品并显示详情弹窗
   * @param product 要查看详情的产品对象
   */
  const viewProductDetail = (product: EnhancedIceType) => {
    setSelectedProduct(product);
    setProductDetailVisible(true);
  };

  /**
   * 查看供应商详情
   * 设置选中供应商并显示详情弹窗
   * @param supplier 要查看详情的供应商对象
   */
  const viewSupplierDetail = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setSupplierDetailVisible(true);
  };

  /**
   * 渲染产品列表
   * 根据搜索结果渲染产品列表，包括加载状态和空状态处理
   * @returns 产品列表组件
   */
  const renderProductList = () => {
    const { products, loading } = searchResults;

    if (loading) {
      return <Spin tip="加载中..." />;
    }

    if (products.length === 0) {
      return <Empty description="暂无符合条件的产品" />;
    }

    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}
        dataSource={products}
        renderItem={(product) => (
          <List.Item>
            <ProductCard
              product={product}
              onViewDetail={() => viewProductDetail(product)}
            />
          </List.Item>
        )}
      />
    );
  };

  /**
   * 渲染供应商列表
   * 根据搜索结果渲染供应商列表，包括加载状态和空状态处理
   * @returns 供应商列表组件
   */
  const renderSupplierList = () => {
    const { suppliers, loading } = searchResults;

    if (loading) {
      return <Spin tip="加载中..." />;
    }

    if (suppliers.length === 0) {
      return <Empty description="暂无符合条件的供应商" />;
    }

    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }}
        dataSource={suppliers}
        renderItem={(supplier) => (
          <List.Item>
            <SupplierCard
              supplier={supplier}
              onViewDetail={() => viewSupplierDetail(supplier)}
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <PageContainer
      header={{
        title: '智能检索系统',
        subTitle: '多维度筛选冰块产品和供应商',
      }}
    >
      {/* 搜索栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={18}>
            <Search
              placeholder="搜索产品名称、描述或供应商"
              allowClear
              enterButton={
                <>
                  <SearchOutlined /> 搜索
                </>
              }
              size="large"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
            />
          </Col>
          <Col span={6}>
            <Button
              type="primary"
              icon={<FilterOutlined />}
              size="large"
              onClick={() => setFilterVisible(true)}
              style={{ width: '100%' }}
            >
              高级筛选
            </Button>
          </Col>
        </Row>

        {/* 快速筛选标签 */}
        <div style={{ marginTop: 16 }}>
          <Space wrap>
            <Text strong>紧急程度:</Text>
            <Tag.CheckableTag
              checked={filterConditions.urgency === 'all'}
              onChange={() => handleFilterChange({ urgency: 'all' })}
            >
              全部
            </Tag.CheckableTag>
            <Tag.CheckableTag
              checked={filterConditions.urgency === 'immediate'}
              onChange={() => handleFilterChange({ urgency: 'immediate' })}
            >
              <Badge status="processing" text="可立即提货" />
            </Tag.CheckableTag>
            <Tag.CheckableTag
              checked={filterConditions.urgency === 'scheduled'}
              onChange={() => handleFilterChange({ urgency: 'scheduled' })}
            >
              <ClockCircleOutlined /> 需预定
            </Tag.CheckableTag>
          </Space>

          <Divider type="vertical" />

          <Space wrap>
            <Text strong>运输方式:</Text>
            <Tag.CheckableTag
              checked={filterConditions.transportMethod === 'all'}
              onChange={() => handleFilterChange({ transportMethod: 'all' })}
            >
              全部
            </Tag.CheckableTag>
            <Tag.CheckableTag
              checked={filterConditions.transportMethod === 'self-pickup'}
              onChange={() =>
                handleFilterChange({ transportMethod: 'self-pickup' })
              }
            >
              自提
            </Tag.CheckableTag>
            <Tag.CheckableTag
              checked={filterConditions.transportMethod === 'delivery'}
              onChange={() =>
                handleFilterChange({ transportMethod: 'delivery' })
              }
            >
              <CarOutlined /> 冷链配送
            </Tag.CheckableTag>
          </Space>

          <Divider type="vertical" />

          <Space wrap>
            <Text strong>认证:</Text>
            <Tag.CheckableTag
              checked={filterConditions.certifications.includes('food')}
              onChange={() => {
                const newCerts = filterConditions.certifications.includes(
                  'food',
                )
                  ? filterConditions.certifications.filter((c) => c !== 'food')
                  : [...filterConditions.certifications, 'food'];
                handleFilterChange({ certifications: newCerts });
              }}
            >
              <SafetyCertificateOutlined /> 食品级认证
            </Tag.CheckableTag>
            <Tag.CheckableTag
              checked={filterConditions.certifications.includes('industrial')}
              onChange={() => {
                const newCerts = filterConditions.certifications.includes(
                  'industrial',
                )
                  ? filterConditions.certifications.filter(
                      (c) => c !== 'industrial',
                    )
                  : [...filterConditions.certifications, 'industrial'];
                handleFilterChange({ certifications: newCerts });
              }}
            >
              <SafetyCertificateOutlined /> 工业标准认证
            </Tag.CheckableTag>
          </Space>
        </div>
      </Card>

      {/* 搜索结果 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <ShoppingCartOutlined />
                产品 ({searchResults.products?.length || 0})
              </span>
            }
            key="products"
          >
            {renderProductList()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <EnvironmentOutlined />
                供应商 ({searchResults.suppliers?.length || 0})
              </span>
            }
            key="suppliers"
          >
            {renderSupplierList()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 高级筛选抽屉 */}
      <Drawer
        title="高级筛选"
        placement="right"
        width={400}
        onClose={() => setFilterVisible(false)}
        open={filterVisible}
        extra={
          <Space>
            <Button onClick={resetFilters}>重置</Button>
            <Button type="primary" onClick={applyFilters}>
              应用筛选
            </Button>
          </Space>
        }
      >
        <FilterPanel
          filterConditions={filterConditions}
          onChange={handleFilterChange}
        />
      </Drawer>

      {/* 产品详情抽屉 */}
      {selectedProduct && (
        <Drawer
          title={selectedProduct.name}
          placement="right"
          width={600}
          onClose={() => setProductDetailVisible(false)}
          open={productDetailVisible}
        >
          <div style={{ textAlign: 'center', marginBottom: 24 }}>
            <img
              src={
                selectedProduct.images && selectedProduct.images.length > 0
                  ? selectedProduct.images[0]
                  : 'https://via.placeholder.com/300x200?text=No+Image'
              }
              alt={selectedProduct.name}
              style={{ maxWidth: '100%', maxHeight: 300 }}
            />
          </div>

          <Divider orientation="left">产品信息</Divider>
          <p>
            <strong>类别:</strong>{' '}
            {selectedProduct.category === 'food'
              ? '食用冰'
              : selectedProduct.category === 'industrial'
              ? '工业冰'
              : '降温冰'}
          </p>
          <p>
            <strong>描述:</strong> {selectedProduct.description}
          </p>

          <Divider orientation="left">规格参数</Divider>
          <p>
            <strong>形状:</strong>{' '}
            {selectedProduct.specifications?.shape || '未提供'}
          </p>
          <p>
            <strong>尺寸:</strong>{' '}
            {selectedProduct.specifications?.size || '未提供'}
          </p>
          <p>
            <strong>重量:</strong>{' '}
            {selectedProduct.specifications?.weight || '未提供'}
          </p>
          <p>
            <strong>温度范围:</strong>{' '}
            {selectedProduct.specifications?.temperatureRange?.min || 0}°C 至{' '}
            {selectedProduct.specifications?.temperatureRange?.max || 0}°C
          </p>
          <p>
            <strong>持续时间:</strong> 约{' '}
            {selectedProduct.specifications?.duration || 0} 小时
          </p>

          <Divider orientation="left">认证信息</Divider>
          <div>
            {selectedProduct.certifications &&
            selectedProduct.certifications.length > 0 ? (
              selectedProduct.certifications.map((cert, index) => (
                <Tag color="blue" key={index}>
                  <SafetyCertificateOutlined /> {cert}
                </Tag>
              ))
            ) : (
              <Empty description="暂无认证信息" />
            )}
          </div>

          <Divider orientation="left">供应商</Divider>
          <List
            dataSource={
              searchResults.suppliers?.filter(
                (s) =>
                  s.products &&
                  Array.isArray(s.products) &&
                  s.products.includes(selectedProduct.id),
              ) || []
            }
            renderItem={(supplier) => (
              <List.Item
                actions={[
                  <Button
                    key="view-detail"
                    type="link"
                    onClick={() => viewSupplierDetail(supplier)}
                  >
                    查看详情
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  title={supplier.name}
                  description={
                    <>
                      <Rate
                        disabled
                        defaultValue={supplier.starRating}
                        allowHalf
                      />
                      <div>准时交付率: {supplier.onTimeDeliveryRate}%</div>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        </Drawer>
      )}

      {/* 供应商详情抽屉 */}
      {selectedSupplier && (
        <Drawer
          title={selectedSupplier.name}
          placement="right"
          width={600}
          onClose={() => setSupplierDetailVisible(false)}
          open={supplierDetailVisible}
        >
          <Divider orientation="left">基本信息</Divider>
          <p>
            <strong>描述:</strong> {selectedSupplier.description || '未提供'}
          </p>
          <p>
            <strong>联系人:</strong>{' '}
            {selectedSupplier.contactPerson || '未提供'}
          </p>
          <p>
            <strong>联系电话:</strong>{' '}
            {selectedSupplier.contactPhone || '未提供'}
          </p>
          <p>
            <strong>电子邮箱:</strong> {selectedSupplier.email || '未提供'}
          </p>
          <p>
            <strong>地址:</strong>{' '}
            {selectedSupplier.address
              ? `${selectedSupplier.address.province || ''} ${
                  selectedSupplier.address.city || ''
                } ${selectedSupplier.address.district || ''} ${
                  selectedSupplier.address.detail || ''
                }`
              : '未提供'}
          </p>

          <Divider orientation="left">信用信息</Divider>
          <p>
            <strong>信用评分:</strong>
            <span style={{ marginLeft: 8 }}>
              {selectedSupplier.creditScore || 0}
              <Tooltip title="信用评分越高，供应商越可靠">
                <InfoCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </span>
          </p>
          <p>
            <strong>星级评价:</strong>
            <span style={{ marginLeft: 8 }}>
              <Rate
                disabled
                defaultValue={selectedSupplier.starRating || 0}
                allowHalf
              />
            </span>
          </p>
          <p>
            <strong>准时交付率:</strong>
            <span style={{ marginLeft: 8 }}>
              {selectedSupplier.onTimeDeliveryRate || 0}%
              <Tooltip title="过去30天内准时交付的订单比例">
                <InfoCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </span>
          </p>
          <p>
            <strong>交易量:</strong> {selectedSupplier.transactionVolume || 0}{' '}
            吨
          </p>

          <Divider orientation="left">提供的产品</Divider>
          <List
            dataSource={
              searchResults.products?.filter(
                (p) =>
                  selectedSupplier.products &&
                  Array.isArray(selectedSupplier.products) &&
                  selectedSupplier.products.includes(p.id),
              ) || []
            }
            renderItem={(product) => (
              <List.Item
                actions={[
                  <Button
                    key="view-detail"
                    type="link"
                    onClick={() => viewProductDetail(product)}
                  >
                    查看详情
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  title={product.name}
                  description={
                    <>
                      <Tag
                        color={
                          product.category === 'food'
                            ? 'green'
                            : product.category === 'industrial'
                            ? 'blue'
                            : 'orange'
                        }
                      >
                        {product.category === 'food'
                          ? '食用冰'
                          : product.category === 'industrial'
                          ? '工业冰'
                          : '降温冰'}
                      </Tag>
                      <div>{product.description}</div>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        </Drawer>
      )}
    </PageContainer>
  );
};

export default SearchSystem;
