import { factoryService } from '@/services/iceOrder/enhanced';
import { Factory } from '@/services/iceOrder/types/enhanced';
import { <PERSON><PERSON>, Button, Card, InputNumber, message, Select, Spin } from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface FactoryMapProps {
  factories: Factory[];
  selectedFactory: Factory | null;
  onSave: () => void;
}

const FactoryMap: React.FC<FactoryMapProps> = ({
  factories,
  selectedFactory,
  onSave,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [currentFactory, setCurrentFactory] = useState<Factory | null>(
    selectedFactory,
  );
  const [radius, setRadius] = useState<number>(
    selectedFactory?.serviceArea.radius || 50,
  );
  const [mapLoaded, setMapLoaded] = useState<boolean>(false);
  const [mapError, setMapError] = useState<string>('');

  // 当选中的工厂变化时，更新当前工厂
  useEffect(() => {
    if (selectedFactory) {
      setCurrentFactory(selectedFactory);
      setRadius(selectedFactory.serviceArea.radius);
    } else if (factories.length > 0) {
      setCurrentFactory(factories[0]);
      setRadius(factories[0].serviceArea.radius);
    }
  }, [selectedFactory, factories]);

  // 模拟地图加载
  useEffect(() => {
    // 清除之前的错误
    setMapError('');

    // 模拟地图加载过程
    const timer = setTimeout(() => {
      // 模拟成功率95%
      const isSuccess = Math.random() > 0.05;

      if (isSuccess) {
        setMapLoaded(true);
      } else {
        // 模拟地图加载失败
        setMapError(
          '地图服务暂时不可用，请稍后再试。可能是网络问题或API密钥无效。',
        );
      }
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // 处理工厂选择变化
  const handleFactoryChange = (factoryId: number) => {
    const factory = factories.find((f) => f.id === factoryId);
    if (factory) {
      setCurrentFactory(factory);
      setRadius(factory.serviceArea.radius);
    }
  };

  // 处理半径变化
  const handleRadiusChange = (value: number | null) => {
    if (value !== null) {
      setRadius(value);
    }
  };

  // 保存服务区域
  const handleSaveServiceArea = async () => {
    if (!currentFactory) return;

    setLoading(true);
    try {
      const serviceArea = {
        ...currentFactory.serviceArea,
        radius,
      };

      await factoryService.updateFactoryServiceArea(
        currentFactory.id,
        serviceArea,
      );
      message.success('服务区域更新成功');
      onSave();
    } catch (error) {
      console.error('更新服务区域失败:', error);
      message.error('更新服务区域失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        onSave();
      }
    } finally {
      setLoading(false);
    }
  };

  // 绘制自定义区域
  const handleDrawCustomArea = () => {
    message.info('绘制功能需要集成地图API，当前为模拟展示');
  };

  return (
    <Spin spinning={loading}>
      <Card title="工厂服务区域配置">
        <div style={{ marginBottom: 16 }}>
          <Select
            style={{ width: 200, marginRight: 16 }}
            placeholder="选择工厂"
            value={currentFactory?.id}
            onChange={handleFactoryChange}
          >
            {factories.map((factory) => (
              <Option key={factory.id} value={factory.id}>
                {factory.name}
              </Option>
            ))}
          </Select>

          <InputNumber
            style={{ width: 120, marginRight: 16 }}
            min={1}
            max={500}
            value={radius}
            onChange={handleRadiusChange}
            addonAfter="公里"
            placeholder="服务半径"
          />

          <Button
            type="primary"
            onClick={handleSaveServiceArea}
            style={{ marginRight: 16 }}
          >
            保存半径
          </Button>

          <Button onClick={handleDrawCustomArea}>绘制自定义区域</Button>
        </div>

        {!mapLoaded ? (
          <div
            style={{
              height: 500,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Spin tip="地图加载中..." />
          </div>
        ) : mapError ? (
          <Alert
            message="地图加载错误"
            description={mapError}
            type="error"
            showIcon
          />
        ) : (
          <div style={{ position: 'relative' }}>
            {/* 这里是地图的模拟展示，实际项目中需要集成地图API */}
            <div
              style={{
                height: 500,
                backgroundColor: '#f0f2f5',
                backgroundImage:
                  'url(https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/121.4737,31.2304,9,0/800x500?access_token=pk.placeholder)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative',
              }}
            >
              {currentFactory && (
                <div
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: `${radius * 2}px`,
                    height: `${radius * 2}px`,
                    borderRadius: '50%',
                    border: '2px solid #1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.2)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <div
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      boxShadow: '0 0 0 5px rgba(24, 144, 255, 0.5)',
                    }}
                  />
                </div>
              )}
              <div
                style={{
                  position: 'absolute',
                  bottom: 16,
                  left: 16,
                  backgroundColor: 'white',
                  padding: '8px 16px',
                  borderRadius: 4,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                }}
              >
                <div>当前工厂: {currentFactory?.name || '未选择'}</div>
                <div>服务半径: {radius} 公里</div>
                <div>
                  覆盖面积: 约 {Math.round(Math.PI * radius * radius)} 平方公里
                </div>
              </div>
            </div>
            <div
              style={{
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'white',
                padding: 8,
                borderRadius: 4,
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              }}
            >
              <div>注意: 这是模拟地图展示</div>
              <div>实际项目中需要集成地图API</div>
            </div>
          </div>
        )}
      </Card>
    </Spin>
  );
};

export default FactoryMap;
