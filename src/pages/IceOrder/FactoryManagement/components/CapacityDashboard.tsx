import { factoryService } from '@/services/iceOrder/enhanced';
import { Factory } from '@/services/iceOrder/types/enhanced';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Divider,
  InputNumber,
  message,
  Progress,
  Row,
  Select,
  Spin,
  Statistic,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface CapacityDashboardProps {
  factories: Factory[];
  selectedFactory: Factory | null;
  onSave: () => void;
}

const CapacityDashboard: React.FC<CapacityDashboardProps> = ({
  factories,
  selectedFactory,
  onSave,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [currentFactory, setCurrentFactory] = useState<Factory | null>(
    selectedFactory,
  );
  const [currentInventory, setCurrentInventory] = useState<number>(
    selectedFactory?.capacity.currentInventory || 0,
  );
  const [dailyProduction, setDailyProduction] = useState<number>(
    selectedFactory?.capacity.dailyProduction || 0,
  );
  const [maxInventory, setMaxInventory] = useState<number>(
    selectedFactory?.capacity.maxInventory || 0,
  );

  // 当选中的工厂变化时，更新当前工厂
  useEffect(() => {
    if (selectedFactory) {
      setCurrentFactory(selectedFactory);
      setCurrentInventory(selectedFactory.capacity.currentInventory);
      setDailyProduction(selectedFactory.capacity.dailyProduction);
      setMaxInventory(selectedFactory.capacity.maxInventory);
    } else if (factories.length > 0) {
      setCurrentFactory(factories[0]);
      setCurrentInventory(factories[0].capacity.currentInventory);
      setDailyProduction(factories[0].capacity.dailyProduction);
      setMaxInventory(factories[0].capacity.maxInventory);
    }
  }, [selectedFactory, factories]);

  // 处理工厂选择变化
  const handleFactoryChange = (factoryId: number) => {
    const factory = factories.find((f) => f.id === factoryId);
    if (factory) {
      setCurrentFactory(factory);
      setCurrentInventory(factory.capacity.currentInventory);
      setDailyProduction(factory.capacity.dailyProduction);
      setMaxInventory(factory.capacity.maxInventory);
    }
  };

  // 保存产能信息
  const handleSaveCapacity = async () => {
    if (!currentFactory) return;

    setLoading(true);
    try {
      const capacity = {
        dailyProduction,
        currentInventory,
        maxInventory,
      };

      await factoryService.updateFactoryCapacity(currentFactory.id, capacity);
      message.success('产能信息更新成功');
      onSave();
    } catch (error) {
      console.error('更新产能信息失败:', error);
      message.error('更新产能信息失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        onSave();
      }
    } finally {
      setLoading(false);
    }
  };

  // 计算库存百分比
  const inventoryPercentage =
    maxInventory > 0 ? Math.round((currentInventory / maxInventory) * 100) : 0;

  // 确定库存状态颜色
  const getInventoryStatusColor = (percentage: number) => {
    if (percentage < 30) return '#f5222d'; // 红色
    if (percentage < 60) return '#faad14'; // 黄色
    return '#52c41a'; // 绿色
  };

  // 计算产能利用率
  const calculateCapacityUtilization = () => {
    if (!currentFactory) return 0;

    const totalCapacity = currentFactory.productionLines.reduce((sum, line) => {
      return sum + (line.status === 'active' ? line.capacity : 0);
    }, 0);

    return totalCapacity > 0
      ? Math.round((dailyProduction / totalCapacity) * 100)
      : 0;
  };

  // 计算订单饱和度（模拟数据）
  const calculateOrderSaturation = () => {
    return Math.min(Math.round(Math.random() * 100), 100);
  };

  return (
    <Spin spinning={loading}>
      <Card title="产能监控仪表盘">
        <div style={{ marginBottom: 16 }}>
          <Select
            style={{ width: 200, marginRight: 16 }}
            placeholder="选择工厂"
            value={currentFactory?.id}
            onChange={handleFactoryChange}
          >
            {factories.map((factory) => (
              <Option key={factory.id} value={factory.id}>
                {factory.name}
              </Option>
            ))}
          </Select>

          <Button type="primary" onClick={handleSaveCapacity}>
            保存产能信息
          </Button>
        </div>

        <Row gutter={16}>
          <Col span={8}>
            <Card title="日产能(吨)" bordered={false}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                value={dailyProduction}
                onChange={(value) => setDailyProduction(value || 0)}
              />
              <Divider />
              <Statistic
                title="产能利用率"
                value={calculateCapacityUtilization()}
                suffix="%"
                valueStyle={{
                  color:
                    calculateCapacityUtilization() > 80 ? '#cf1322' : '#3f8600',
                }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="当前库存(吨)" bordered={false}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={maxInventory}
                value={currentInventory}
                onChange={(value) => setCurrentInventory(value || 0)}
              />
              <Divider />
              <Progress
                percent={inventoryPercentage}
                status={inventoryPercentage < 30 ? 'exception' : 'normal'}
                strokeColor={getInventoryStatusColor(inventoryPercentage)}
              />
              {inventoryPercentage < 30 && (
                <div style={{ color: '#f5222d', marginTop: 8 }}>
                  <ExclamationCircleOutlined /> 库存不足，建议补充
                </div>
              )}
            </Card>
          </Col>
          <Col span={8}>
            <Card title="最大库存容量(吨)" bordered={false}>
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                value={maxInventory}
                onChange={(value) => setMaxInventory(value || 0)}
              />
              <Divider />
              <Statistic
                title="库存周转天数"
                value={
                  dailyProduction > 0
                    ? Math.round(currentInventory / dailyProduction)
                    : 0
                }
                suffix="天"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={16}>
          <Col span={12}>
            <Card title="订单饱和度" bordered={false}>
              <Progress
                type="dashboard"
                percent={calculateOrderSaturation()}
                strokeColor={
                  calculateOrderSaturation() > 80
                    ? '#f5222d'
                    : calculateOrderSaturation() > 60
                    ? '#faad14'
                    : '#52c41a'
                }
              />
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                {calculateOrderSaturation() > 80 ? (
                  <div style={{ color: '#f5222d' }}>
                    <ArrowUpOutlined /> 订单饱和，建议增加产能
                  </div>
                ) : calculateOrderSaturation() < 30 ? (
                  <div style={{ color: '#faad14' }}>
                    <ArrowDownOutlined /> 订单不足，可考虑促销
                  </div>
                ) : (
                  <div style={{ color: '#52c41a' }}>订单量适中</div>
                )}
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="生产线状态" bordered={false}>
              {currentFactory?.productionLines.map((line, index) => (
                <div key={index} style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 8 }}>
                    {line.name} ({line.capacity}吨/天)
                  </div>
                  <Progress
                    percent={
                      line.status === 'active'
                        ? 100
                        : line.status === 'maintenance'
                        ? 50
                        : 0
                    }
                    status={
                      line.status === 'active'
                        ? 'normal'
                        : line.status === 'maintenance'
                        ? 'active'
                        : 'exception'
                    }
                    strokeColor={
                      line.status === 'active'
                        ? '#52c41a'
                        : line.status === 'maintenance'
                        ? '#faad14'
                        : '#f5222d'
                    }
                    format={() =>
                      line.status === 'active'
                        ? '运行中'
                        : line.status === 'maintenance'
                        ? '维护中'
                        : '已停用'
                    }
                  />
                </div>
              ))}
            </Card>
          </Col>
        </Row>
      </Card>
    </Spin>
  );
};

export default CapacityDashboard;
