import { factoryService } from '@/services/iceOrder/enhanced';
import { Factory } from '@/services/iceOrder/types/enhanced';
import { EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Spin,
  Table,
  Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface ProductionLineTableProps {
  factories: Factory[];
  selectedFactory: Factory | null;
  onSave: () => void;
}

interface ProductionLine {
  id: number;
  name: string;
  status: 'active' | 'maintenance' | 'offline';
  capacity: number;
}

const ProductionLineTable: React.FC<ProductionLineTableProps> = ({
  factories,
  selectedFactory,
  onSave,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [currentFactory, setCurrentFactory] = useState<Factory | null>(
    selectedFactory,
  );
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editingLine, setEditingLine] = useState<ProductionLine | null>(null);
  const [form] = Form.useForm();

  // 当选中的工厂变化时，更新当前工厂
  useEffect(() => {
    if (selectedFactory) {
      setCurrentFactory(selectedFactory);
    } else if (factories.length > 0) {
      setCurrentFactory(factories[0]);
    }
  }, [selectedFactory, factories]);

  // 处理工厂选择变化
  const handleFactoryChange = (factoryId: number) => {
    const factory = factories.find((f) => f.id === factoryId);
    if (factory) {
      setCurrentFactory(factory);
    }
  };

  // 更新生产线状态
  const handleUpdateStatus = async (
    lineId: number,
    status: 'active' | 'maintenance' | 'offline',
  ) => {
    if (!currentFactory) return;

    setLoading(true);
    try {
      await factoryService.updateProductionLineStatus(
        currentFactory.id,
        lineId,
        status,
      );
      message.success('生产线状态更新成功');
      onSave();
    } catch (error) {
      console.error('更新生产线状态失败:', error);
      message.error('更新生产线状态失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        onSave();
      }
    } finally {
      setLoading(false);
    }
  };

  // 打开编辑模态框
  const handleEdit = (line: ProductionLine) => {
    setEditingLine(line);
    form.setFieldsValue(line);
    setModalVisible(true);
  };

  // 打开添加模态框
  const handleAdd = () => {
    setEditingLine(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 保存生产线
  const handleSaveLine = async () => {
    try {
      // 验证表单字段
      await form.validateFields();

      if (!currentFactory) return;

      setLoading(true);

      // 这里应该调用API保存生产线信息
      // 由于没有具体的API，这里模拟成功
      setTimeout(() => {
        message.success(editingLine ? '生产线更新成功' : '生产线添加成功');
        setModalVisible(false);
        onSave();
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('保存生产线失败:', error);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '生产线名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        let text = '';

        switch (status) {
          case 'active':
            color = 'green';
            text = '运行中';
            break;
          case 'maintenance':
            color = 'orange';
            text = '维护中';
            break;
          case 'offline':
            color = 'red';
            text = '已停用';
            break;
          default:
            color = 'default';
            text = '未知';
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '产能(吨/天)',
      dataIndex: 'capacity',
      key: 'capacity',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ProductionLine) => (
        <div>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            style={{ marginRight: 8 }}
          >
            编辑
          </Button>

          {record.status !== 'active' && (
            <Popconfirm
              title="确定要将状态更改为运行中吗？"
              onConfirm={() => handleUpdateStatus(record.id, 'active')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" style={{ color: 'green' }}>
                设为运行中
              </Button>
            </Popconfirm>
          )}

          {record.status !== 'maintenance' && (
            <Popconfirm
              title="确定要将状态更改为维护中吗？"
              onConfirm={() => handleUpdateStatus(record.id, 'maintenance')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" style={{ color: 'orange' }}>
                设为维护中
              </Button>
            </Popconfirm>
          )}

          {record.status !== 'offline' && (
            <Popconfirm
              title="确定要将状态更改为已停用吗？"
              onConfirm={() => handleUpdateStatus(record.id, 'offline')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                设为已停用
              </Button>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  return (
    <Spin spinning={loading}>
      <Card title="生产线管理">
        <div
          style={{
            marginBottom: 16,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Select
            style={{ width: 200 }}
            placeholder="选择工厂"
            value={currentFactory?.id}
            onChange={handleFactoryChange}
          >
            {factories.map((factory) => (
              <Option key={factory.id} value={factory.id}>
                {factory.name}
              </Option>
            ))}
          </Select>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加生产线
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={currentFactory?.productionLines || []}
          rowKey="id"
          pagination={false}
        />
      </Card>

      <Modal
        title={editingLine ? '编辑生产线' : '添加生产线'}
        open={modalVisible}
        onOk={handleSaveLine}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ status: 'active' }}
        >
          <Form.Item
            name="name"
            label="生产线名称"
            rules={[{ required: true, message: '请输入生产线名称' }]}
          >
            <Input placeholder="请输入生产线名称" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="active">运行中</Option>
              <Option value="maintenance">维护中</Option>
              <Option value="offline">已停用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="capacity"
            label="产能(吨/天)"
            rules={[{ required: true, message: '请输入产能' }]}
          >
            <InputNumber
              min={0}
              placeholder="请输入产能"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Spin>
  );
};

export default ProductionLineTable;
