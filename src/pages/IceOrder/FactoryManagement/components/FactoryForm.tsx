/**
 * 工厂信息表单组件
 * 用于添加和编辑工厂信息
 */
import { factoryService } from '@/services/iceOrder/enhanced';
import { Factory } from '@/services/iceOrder/types/enhanced';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  message,
} from 'antd';
import React, { useEffect } from 'react';

const { Option } = Select;

/**
 * 工厂表单组件属性定义
 */
interface FactoryFormProps {
  visible: boolean; // 表单是否可见
  initialValues: Factory | null; // 初始值，编辑时使用
  onCancel: () => void; // 取消回调
  onSuccess: () => void; // 成功提交回调
}

const FactoryForm: React.FC<FactoryFormProps> = ({
  visible,
  initialValues,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const isEdit = !!initialValues;

  /**
   * 当表单显示或初始值变化时，重置表单并设置初始值
   */
  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues, form]);

  /**
   * 提交表单
   * 验证表单并调用API创建或更新工厂信息
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEdit) {
        // 更新工厂
        await factoryService.updateFactory(initialValues.id, values);
        message.success('工厂信息更新成功');
      } else {
        // 创建工厂
        await factoryService.createFactory(values);
        message.success('工厂创建成功');
      }

      onSuccess();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交失败，请检查表单');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟提交成功');
        onSuccess();
      }
    }
  };

  return (
    <Modal
      title={isEdit ? '编辑工厂信息' : '添加工厂'}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          保存
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          capacity: {
            dailyProduction: 0,
            currentInventory: 0,
            maxInventory: 0,
          },
          serviceArea: {
            radius: 50,
          },
          productionLines: [{ name: '', status: 'active', capacity: 0 }],
          certifications: [],
          rating: 5,
        }}
      >
        {/* 基本信息 */}
        <Form.Item
          name="name"
          label="工厂名称"
          rules={[{ required: true, message: '请输入工厂名称' }]}
        >
          <Input placeholder="请输入工厂名称" />
        </Form.Item>

        {/* 地址信息 */}
        <Form.Item label="地址信息">
          <Input.Group compact>
            <Form.Item
              name={['address', 'province']}
              noStyle
              rules={[{ required: true, message: '请选择省份' }]}
            >
              <Select placeholder="省份" style={{ width: '25%' }}>
                <Option value="北京市">北京市</Option>
                <Option value="上海市">上海市</Option>
                <Option value="广东省">广东省</Option>
                <Option value="江苏省">江苏省</Option>
                <Option value="浙江省">浙江省</Option>
                {/* 更多省份... */}
              </Select>
            </Form.Item>
            <Form.Item
              name={['address', 'city']}
              noStyle
              rules={[{ required: true, message: '请选择城市' }]}
            >
              <Select placeholder="城市" style={{ width: '25%' }}>
                <Option value="北京市">北京市</Option>
                <Option value="上海市">上海市</Option>
                <Option value="广州市">广州市</Option>
                <Option value="深圳市">深圳市</Option>
                <Option value="杭州市">杭州市</Option>
                {/* 更多城市... */}
              </Select>
            </Form.Item>
            <Form.Item
              name={['address', 'district']}
              noStyle
              rules={[{ required: true, message: '请选择区/县' }]}
            >
              <Select placeholder="区/县" style={{ width: '25%' }}>
                <Option value="朝阳区">朝阳区</Option>
                <Option value="海淀区">海淀区</Option>
                <Option value="浦东新区">浦东新区</Option>
                <Option value="天河区">天河区</Option>
                {/* 更多区县... */}
              </Select>
            </Form.Item>
            <Form.Item
              name={['address', 'detail']}
              noStyle
              rules={[{ required: true, message: '请输入详细地址' }]}
            >
              <Input placeholder="详细地址" style={{ width: '25%' }} />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        {/* 联系信息 */}
        <Form.Item
          name="contactPerson"
          label="联系人"
          rules={[{ required: true, message: '请输入联系人' }]}
        >
          <Input placeholder="请输入联系人" />
        </Form.Item>

        <Form.Item
          name="contactPhone"
          label="联系电话"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
          ]}
        >
          <Input placeholder="请输入联系电话" />
        </Form.Item>

        {/* 服务区域 */}
        <Form.Item label="服务区域">
          <Form.Item
            name={['serviceArea', 'radius']}
            label="服务半径(公里)"
            rules={[{ required: true, message: '请输入服务半径' }]}
          >
            <InputNumber min={1} max={500} />
          </Form.Item>
          <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
            注：详细的服务区域可在地图上进行绘制
          </p>
        </Form.Item>

        {/* 产能信息 */}
        <Form.Item label="产能信息">
          <Input.Group compact>
            <Form.Item
              name={['capacity', 'dailyProduction']}
              label="日产能(吨)"
              rules={[{ required: true, message: '请输入日产能' }]}
            >
              <InputNumber min={0} />
            </Form.Item>
            <Form.Item
              name={['capacity', 'currentInventory']}
              label="当前库存(吨)"
              rules={[{ required: true, message: '请输入当前库存' }]}
            >
              <InputNumber min={0} />
            </Form.Item>
            <Form.Item
              name={['capacity', 'maxInventory']}
              label="最大库存容量(吨)"
              rules={[{ required: true, message: '请输入最大库存容量' }]}
            >
              <InputNumber min={0} />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        {/* 生产线 */}
        <Form.Item label="生产线">
          <Form.List name="productionLines">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space
                    key={key}
                    style={{ display: 'flex', marginBottom: 8 }}
                    align="baseline"
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'name']}
                      rules={[{ required: true, message: '请输入生产线名称' }]}
                    >
                      <Input placeholder="生产线名称" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'status']}
                      rules={[{ required: true, message: '请选择状态' }]}
                    >
                      <Select placeholder="状态" style={{ width: 120 }}>
                        <Option value="active">运行中</Option>
                        <Option value="maintenance">维护中</Option>
                        <Option value="offline">已停用</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'capacity']}
                      rules={[{ required: true, message: '请输入产能' }]}
                    >
                      <InputNumber placeholder="产能(吨/天)" min={0} />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加生产线
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form.Item>

        {/* 认证信息 */}
        <Form.Item name="certifications" label="认证信息">
          <Select mode="tags" placeholder="请输入或选择认证信息">
            <Option value="ISO9001">ISO9001</Option>
            <Option value="ISO14001">ISO14001</Option>
            <Option value="食品安全认证">食品安全认证</Option>
            <Option value="HACCP">HACCP</Option>
            <Option value="有机认证">有机认证</Option>
          </Select>
        </Form.Item>

        {/* 评分 */}
        <Form.Item name="rating" label="评分">
          <InputNumber min={1} max={5} step={0.1} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FactoryForm;
