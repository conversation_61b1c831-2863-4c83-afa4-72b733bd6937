/**
 * 工厂信息管理页面
 * 用于管理冰块生产工厂的信息，包括基本信息、服务区域、产能监控和生产线管理
 */
import { factoryService } from '@/services/iceOrder/enhanced';
import { Factory } from '@/services/iceOrder/types/enhanced';
import {
  BarChartOutlined,
  DeleteOutlined,
  EditOutlined,
  EnvironmentOutlined,
  PlusOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  message,
  Popconfirm,
  Row,
  Space,
  Statistic,
  Table,
  Tabs,
  Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';
import CapacityDashboard from './components/CapacityDashboard';
import FactoryForm from './components/FactoryForm';
import FactoryMap from './components/FactoryMap';
import ProductionLineTable from './components/ProductionLineTable';

const { TabPane } = Tabs;

const FactoryManagement: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState<boolean>(true); // 加载状态
  const [factories, setFactories] = useState<Factory[]>([]); // 工厂列表数据
  const [selectedFactory, setSelectedFactory] = useState<Factory | null>(null); // 当前选中的工厂
  const [formVisible, setFormVisible] = useState<boolean>(false); // 表单显示状态
  const [activeTab, setActiveTab] = useState<string>('list'); // 当前激活的标签页

  /**
   * 加载工厂列表数据
   * 从服务器获取工厂列表，如果失败则使用模拟数据
   */
  const loadFactories = async () => {
    setLoading(true);
    try {
      const response = await factoryService.getFactoryList();
      if (response.status === 200) {
        setFactories(response.data);
      }
    } catch (error) {
      console.error('加载工厂列表失败:', error);
      message.error('加载工厂列表失败');

      // 使用模拟数据
      setFactories([
        {
          id: 1,
          name: '北京冰工厂',
          address: {
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '建国路88号',
          },
          contactPerson: '张经理',
          contactPhone: '13800138001',
          serviceArea: {
            center: { longitude: 116.4, latitude: 39.9 },
            radius: 50,
          },
          capacity: {
            dailyProduction: 500,
            currentInventory: 300,
            maxInventory: 1000,
          },
          productionLines: [
            { id: 1, name: '生产线A', status: 'active', capacity: 200 },
            { id: 2, name: '生产线B', status: 'maintenance', capacity: 150 },
            { id: 3, name: '生产线C', status: 'active', capacity: 150 },
          ],
          certifications: ['ISO9001', '食品安全认证'],
          rating: 4.5,
          createdAt: '2023-01-15T08:30:00',
        },
        {
          id: 2,
          name: '上海冷冻厂',
          address: {
            province: '上海市',
            city: '上海市',
            district: '浦东新区',
            detail: '陆家嘴1号',
          },
          contactPerson: '李经理',
          contactPhone: '13900139002',
          serviceArea: {
            center: { longitude: 121.5, latitude: 31.2 },
            radius: 30,
          },
          capacity: {
            dailyProduction: 800,
            currentInventory: 600,
            maxInventory: 1500,
          },
          productionLines: [
            { id: 1, name: '生产线A', status: 'active', capacity: 300 },
            { id: 2, name: '生产线B', status: 'active', capacity: 300 },
            { id: 3, name: '生产线C', status: 'offline', capacity: 200 },
          ],
          certifications: ['ISO9001', 'ISO14001', '食品安全认证'],
          rating: 4.8,
          createdAt: '2023-02-20T10:15:00',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 页面初始化时加载工厂列表数据
   */
  useEffect(() => {
    loadFactories();
  }, []);

  /**
   * 处理添加工厂操作
   * 清空选中工厂并显示表单
   */
  const handleAddFactory = () => {
    setSelectedFactory(null);
    setFormVisible(true);
  };

  /**
   * 处理编辑工厂操作
   * 设置选中工厂并显示表单
   * @param factory 要编辑的工厂对象
   */
  const handleEditFactory = (factory: Factory) => {
    setSelectedFactory(factory);
    setFormVisible(true);
  };

  /**
   * 处理删除工厂操作
   * 调用API删除指定工厂
   * @param id 要删除的工厂ID
   */
  const handleDeleteFactory = async (id: number) => {
    try {
      const response = await factoryService.deleteFactory(id);
      if (response.status === 200) {
        message.success('删除成功');
        loadFactories();
      }
    } catch (error) {
      console.error('删除工厂失败:', error);
      message.error('删除工厂失败');
    }
  };

  /**
   * 表单提交成功回调
   * 关闭表单并重新加载工厂列表
   */
  const handleFormSuccess = () => {
    setFormVisible(false);
    loadFactories();
  };

  /**
   * 表格列定义
   * 定义工厂列表的各个列及其渲染方式
   */
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工厂名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (address: Factory['address']) =>
        address
          ? `${address.province} ${address.city} ${address.district} ${address.detail}`
          : '-',
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
    },
    {
      title: '日产能(吨)',
      dataIndex: ['capacity', 'dailyProduction'],
      key: 'dailyProduction',
    },
    {
      title: '当前库存(吨)',
      dataIndex: ['capacity', 'currentInventory'],
      key: 'currentInventory',
      render: (inventory: number, record: Factory) => {
        const percentage = (inventory / record.capacity.maxInventory) * 100;
        let color = 'green';
        if (percentage < 30) color = 'red';
        else if (percentage < 60) color = 'orange';
        return <Tag color={color}>{inventory}</Tag>;
      },
    },
    {
      title: '认证',
      dataIndex: 'certifications',
      key: 'certifications',
      render: (certifications: string[]) => (
        <>
          {certifications.map((cert) => (
            <Tag color="blue" key={cert}>
              {cert}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Factory) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditFactory(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<EnvironmentOutlined />}
            onClick={() => {
              setSelectedFactory(record);
              setActiveTab('map');
            }}
          >
            服务区域
          </Button>
          <Button
            type="text"
            icon={<BarChartOutlined />}
            onClick={() => {
              setSelectedFactory(record);
              setActiveTab('capacity');
            }}
          >
            产能
          </Button>
          <Popconfirm
            title="确定要删除这个工厂吗？"
            onConfirm={() => handleDeleteFactory(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  /**
   * 统计数据计算
   * 计算工厂总数、总产能、总库存和活跃生产线数量
   */
  const statistics = {
    totalFactories: factories.length,
    totalCapacity: factories.reduce(
      (sum, factory) => sum + factory.capacity.dailyProduction,
      0,
    ),
    totalInventory: factories.reduce(
      (sum, factory) => sum + factory.capacity.currentInventory,
      0,
    ),
    activeProductionLines: factories.reduce(
      (sum, factory) =>
        sum +
        factory.productionLines.filter((line) => line.status === 'active')
          .length,
      0,
    ),
  };

  return (
    <PageContainer
      header={{
        title: '工厂信息管理',
        subTitle: '管理冰块生产工厂信息',
      }}
    >
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="工厂总数" value={statistics.totalFactories} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总日产能(吨)" value={statistics.totalCapacity} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总库存(吨)" value={statistics.totalInventory} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃生产线"
              value={statistics.activeProductionLines}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="工厂列表" key="list">
            <div style={{ marginBottom: 16, textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddFactory}
              >
                添加工厂
              </Button>
            </div>
            <Table
              columns={columns}
              dataSource={factories}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab="服务区域地图" key="map">
            <FactoryMap
              factories={factories}
              selectedFactory={selectedFactory}
              onSave={loadFactories}
            />
          </TabPane>
          <TabPane tab="产能监控" key="capacity">
            <CapacityDashboard
              factories={factories}
              selectedFactory={selectedFactory}
              onSave={loadFactories}
            />
          </TabPane>
          <TabPane tab="生产线管理" key="productionLines">
            <ProductionLineTable
              factories={factories}
              selectedFactory={selectedFactory}
              onSave={loadFactories}
            />
          </TabPane>
          <TabPane
            tab={
              <span>
                <WarningOutlined />
                预警信息
              </span>
            }
            key="warnings"
          >
            <Card title="产能预警">
              {factories
                .filter(
                  (factory) =>
                    factory.capacity.currentInventory <
                    factory.capacity.maxInventory * 0.3,
                )
                .map((factory) => (
                  <div key={factory.id} style={{ marginBottom: 16 }}>
                    <Tag color="red">库存不足</Tag>
                    <span style={{ marginLeft: 8 }}>{factory.name}</span>
                    <span style={{ marginLeft: 8 }}>
                      当前库存: {factory.capacity.currentInventory}吨 (
                      {Math.round(
                        (factory.capacity.currentInventory /
                          factory.capacity.maxInventory) *
                          100,
                      )}
                      %)
                    </span>
                  </div>
                ))}
              {factories
                .filter((factory) =>
                  factory.productionLines.some(
                    (line) => line.status === 'maintenance',
                  ),
                )
                .map((factory) => (
                  <div
                    key={`${factory.id}-maintenance`}
                    style={{ marginBottom: 16 }}
                  >
                    <Tag color="orange">生产线维护中</Tag>
                    <span style={{ marginLeft: 8 }}>{factory.name}</span>
                    <span style={{ marginLeft: 8 }}>
                      {factory.productionLines
                        .filter((line) => line.status === 'maintenance')
                        .map((line) => line.name)
                        .join(', ')}
                    </span>
                  </div>
                ))}
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 工厂表单 */}
      <FactoryForm
        visible={formVisible}
        initialValues={selectedFactory}
        onCancel={() => setFormVisible(false)}
        onSuccess={handleFormSuccess}
      />
    </PageContainer>
  );
};

export default FactoryManagement;
