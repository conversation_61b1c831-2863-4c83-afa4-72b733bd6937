/**
 * 动态定价机制页面
 * 管理冰块产品的价格策略，包括基础价格、阶梯定价、季节性系数和紧急情况因素
 */
import {
  enhancedIceTypeService,
  reportService,
} from '@/services/iceOrder/enhanced';
import {
  EnhancedIceType,
  PriceInfo,
  WeatherData,
} from '@/services/iceOrder/types/enhanced';
import {
  AreaChartOutlined,
  EditOutlined,
  LineChartOutlined,
  SaveOutlined,
  ThunderboltOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Alert,
  Button,
  Card,
  Col,
  Divider,
  Empty,
  Form,
  InputNumber,
  message,
  Row,
  Select,
  Spin,
  Statistic,
  Tabs,
  Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';
import BulkDiscountTable from './components/BulkDiscountTable';
import EmergencyPriceModal from './components/EmergencyPriceModal';
import PriceChart from './components/PriceChart';
import SeasonalFactorTable from './components/SeasonalFactorTable';

const { TabPane } = Tabs;
const { Option } = Select;

const PricingManagement: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState<boolean>(true); // 加载状态
  const [iceTypes, setIceTypes] = useState<EnhancedIceType[]>([]); // 冰块类型列表
  const [selectedIceType, setSelectedIceType] =
    useState<EnhancedIceType | null>(null); // 当前选中的冰块类型
  const [priceInfo, setPriceInfo] = useState<PriceInfo | null>(null); // 价格信息
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null); // 天气数据
  const [editingBasePrice, setEditingBasePrice] = useState<boolean>(false); // 是否正在编辑基础价格
  const [newBasePrice, setNewBasePrice] = useState<number>(0); // 新的基础价格
  const [emergencyModalVisible, setEmergencyModalVisible] =
    useState<boolean>(false); // 紧急情况弹窗显示状态
  const [form] = Form.useForm(); // 表单实例

  /**
   * 加载冰块类型列表
   * 从服务器获取冰块类型列表，如果失败则使用模拟数据
   */
  const loadIceTypes = async () => {
    setLoading(true);
    try {
      const response = await enhancedIceTypeService.getEnhancedIceTypeList();
      if (response.status === 200) {
        setIceTypes(response.data);
        if (response.data.length > 0) {
          setSelectedIceType(response.data[0]);
        }
      }
    } catch (error) {
      console.error('加载冰块类型列表失败:', error);
      message.error('加载冰块类型列表失败');

      // 使用模拟数据
      const mockIceTypes = [
        {
          id: 1,
          name: '方块冰',
          description: '标准方形冰块，适合各种饮品',
          category: 'food',
          specifications: {
            shape: '方形',
            size: '2x2x2cm',
            weight: '20g/块',
            temperatureRange: {
              min: -10,
              max: 0,
            },
            duration: 2,
          },
          certifications: ['食品级认证'],
          images: ['https://example.com/ice1.jpg'],
        },
        {
          id: 2,
          name: '碎冰',
          description: '小颗粒碎冰，适合刨冰和冰沙',
          category: 'food',
          specifications: {
            shape: '不规则',
            size: '0.5-1cm',
            weight: '5g/块',
            temperatureRange: {
              min: -8,
              max: 0,
            },
            duration: 1,
          },
          certifications: ['食品级认证'],
          images: ['https://example.com/ice2.jpg'],
        },
        {
          id: 3,
          name: '工业冰块',
          description: '大型工业冰块，适合工业降温',
          category: 'industrial',
          specifications: {
            shape: '方形',
            size: '10x10x10cm',
            weight: '1kg/块',
            temperatureRange: {
              min: -15,
              max: -5,
            },
            duration: 8,
          },
          certifications: ['工业标准认证'],
          images: ['https://example.com/ice3.jpg'],
        },
        {
          id: 4,
          name: '降温冰袋',
          description: '专业降温冰袋，适合运输和保存',
          category: 'cooling',
          specifications: {
            shape: '袋状',
            size: '20x30cm',
            weight: '500g/袋',
            temperatureRange: {
              min: -20,
              max: -10,
            },
            duration: 12,
          },
          certifications: ['工业标准认证', '医用级认证'],
          images: ['https://example.com/ice4.jpg'],
        },
      ];
      setIceTypes(mockIceTypes as EnhancedIceType[]);
      if (mockIceTypes.length > 0) {
        setSelectedIceType(mockIceTypes[0] as EnhancedIceType);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载冰块价格信息
   * 根据冰块类型ID从服务器获取价格信息，如果失败则使用模拟数据
   * @param iceTypeId 冰块类型ID
   */
  const loadPriceInfo = async (iceTypeId: number) => {
    setLoading(true);
    try {
      const response = await enhancedIceTypeService.getIceTypePrice(iceTypeId);
      if (response.status === 200) {
        setPriceInfo(response.data);
        setNewBasePrice(response.data.basePrice);
      }
    } catch (error) {
      console.error('加载价格信息失败:', error);

      // 使用模拟数据
      const mockPriceInfo = {
        basePrice: 100,
        unit: '元/吨',
        bulkDiscounts: [
          { quantity: 10, discount: 0.95 },
          { quantity: 50, discount: 0.9 },
          { quantity: 100, discount: 0.85 },
        ],
        seasonalFactors: [
          { season: 'spring', factor: 1.0 },
          { season: 'summer', factor: 1.2 },
          { season: 'autumn', factor: 1.0 },
          { season: 'winter', factor: 0.9 },
        ],
        emergencyFactor: 1.5,
        historyPrices: [
          { date: '2023-01-01', price: 95 },
          { date: '2023-02-01', price: 95 },
          { date: '2023-03-01', price: 98 },
          { date: '2023-04-01', price: 100 },
          { date: '2023-05-01', price: 105 },
          { date: '2023-06-01', price: 110 },
          { date: '2023-07-01', price: 115 },
        ],
      };
      setPriceInfo(mockPriceInfo as PriceInfo);
      setNewBasePrice(mockPriceInfo.basePrice);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载天气数据
   * 从服务器获取天气数据，如果失败则使用模拟数据
   */
  const loadWeatherData = async () => {
    try {
      const response = await reportService.getWeatherData('北京市');
      if (response.status === 200) {
        setWeatherData(response.data);
      }
    } catch (error) {
      console.error('加载天气数据失败:', error);

      // 使用模拟数据
      setWeatherData({
        city: '北京市',
        date: new Date().toISOString(),
        temperature: 32,
        humidity: 60,
        weatherType: '晴',
        heatWarningLevel: 'orange',
      });
    }
  };

  /**
   * 页面初始化时加载冰块类型列表和天气数据
   */
  useEffect(() => {
    loadIceTypes();
    loadWeatherData();
  }, []);

  /**
   * 当选中的冰块类型变化时，加载对应的价格信息
   */
  useEffect(() => {
    if (selectedIceType) {
      loadPriceInfo(selectedIceType.id);
    }
  }, [selectedIceType]);

  /**
   * 处理冰块类型选择变化
   * 当用户选择不同的冰块类型时更新选中的冰块类型
   * @param iceTypeId 选中的冰块类型ID
   */
  const handleIceTypeChange = (iceTypeId: number) => {
    const iceType = iceTypes.find((item) => item.id === iceTypeId);
    if (iceType) {
      setSelectedIceType(iceType);
    }
  };

  /**
   * 保存基础价格
   * 将新的基础价格保存到服务器
   */
  const handleSaveBasePrice = async () => {
    if (!selectedIceType || !priceInfo) return;

    setLoading(true);
    try {
      const updatedPriceInfo = {
        ...priceInfo,
        basePrice: newBasePrice,
      };

      await enhancedIceTypeService.updateIceTypePrice(
        selectedIceType.id,
        updatedPriceInfo,
      );
      message.success('基础价格更新成功');
      setPriceInfo(updatedPriceInfo);
      setEditingBasePrice(false);
    } catch (error) {
      console.error('更新基础价格失败:', error);
      message.error('更新基础价格失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        const updatedPriceInfo = {
          ...priceInfo,
          basePrice: newBasePrice,
        };
        setPriceInfo(updatedPriceInfo);
        setEditingBasePrice(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // 保存批量折扣
  const handleSaveBulkDiscounts = async (
    bulkDiscounts: PriceInfo['bulkDiscounts'],
  ) => {
    if (!selectedIceType || !priceInfo) return;

    setLoading(true);
    try {
      const updatedPriceInfo = {
        ...priceInfo,
        bulkDiscounts,
      };

      await enhancedIceTypeService.updateIceTypePrice(
        selectedIceType.id,
        updatedPriceInfo,
      );
      message.success('批量折扣更新成功');
      setPriceInfo(updatedPriceInfo);
    } catch (error) {
      console.error('更新批量折扣失败:', error);
      message.error('更新批量折扣失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        const updatedPriceInfo = {
          ...priceInfo,
          bulkDiscounts,
        };
        setPriceInfo(updatedPriceInfo);
      }
    } finally {
      setLoading(false);
    }
  };

  // 保存季节性因素
  const handleSaveSeasonalFactors = async (
    seasonalFactors: PriceInfo['seasonalFactors'],
  ) => {
    if (!selectedIceType || !priceInfo) return;

    setLoading(true);
    try {
      const updatedPriceInfo = {
        ...priceInfo,
        seasonalFactors,
      };

      await enhancedIceTypeService.updateIceTypePrice(
        selectedIceType.id,
        updatedPriceInfo,
      );
      message.success('季节性因素更新成功');
      setPriceInfo(updatedPriceInfo);
    } catch (error) {
      console.error('更新季节性因素失败:', error);
      message.error('更新季节性因素失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        const updatedPriceInfo = {
          ...priceInfo,
          seasonalFactors,
        };
        setPriceInfo(updatedPriceInfo);
      }
    } finally {
      setLoading(false);
    }
  };

  // 保存紧急情况因素
  const handleSaveEmergencyFactor = async (emergencyFactor: number) => {
    if (!selectedIceType || !priceInfo) return;

    setLoading(true);
    try {
      const updatedPriceInfo = {
        ...priceInfo,
        emergencyFactor,
      };

      await enhancedIceTypeService.updateIceTypePrice(
        selectedIceType.id,
        updatedPriceInfo,
      );
      message.success('紧急情况因素更新成功');
      setPriceInfo(updatedPriceInfo);
      setEmergencyModalVisible(false);
    } catch (error) {
      console.error('更新紧急情况因素失败:', error);
      message.error('更新紧急情况因素失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟更新成功');
        const updatedPriceInfo = {
          ...priceInfo,
          emergencyFactor,
        };
        setPriceInfo(updatedPriceInfo);
        setEmergencyModalVisible(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取当前季节
  const getCurrentSeason = () => {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  };

  // 获取当前季节因素
  const getCurrentSeasonalFactor = () => {
    if (!priceInfo) return 1;

    const currentSeason = getCurrentSeason();
    const factor = priceInfo.seasonalFactors.find(
      (item) => item.season === currentSeason,
    );
    return factor ? factor.factor : 1;
  };

  // 计算实际价格
  const calculateActualPrice = (quantity: number) => {
    if (!priceInfo) return 0;

    // 基础价格
    let price = priceInfo.basePrice;

    // 应用季节性因素
    price *= getCurrentSeasonalFactor();

    // 应用批量折扣
    const discounts = [...priceInfo.bulkDiscounts].sort(
      (a, b) => b.quantity - a.quantity,
    );
    for (const discount of discounts) {
      if (quantity >= discount.quantity) {
        price *= discount.discount;
        break;
      }
    }

    // 应用紧急情况因素（如果有高温预警）
    if (weatherData && weatherData.heatWarningLevel !== 'none') {
      price *= priceInfo.emergencyFactor;
    }

    return Math.round(price * 100) / 100;
  };

  // 季节名称映射
  const seasonNameMap = {
    spring: '春季',
    summer: '夏季',
    autumn: '秋季',
    winter: '冬季',
  };

  // 高温预警等级映射
  const heatWarningLevelMap = {
    none: '无预警',
    yellow: '黄色预警',
    orange: '橙色预警',
    red: '红色预警',
  };

  // 高温预警等级颜色映射
  const heatWarningLevelColorMap = {
    none: '',
    yellow: 'yellow',
    orange: 'orange',
    red: 'red',
  };

  return (
    <PageContainer
      header={{
        title: '动态定价机制',
        subTitle: '管理冰块产品的价格策略',
      }}
    >
      <Spin spinning={loading}>
        {/* 天气信息卡片 */}
        {weatherData && (
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="当前城市"
                  value={weatherData.city}
                  valueStyle={{ fontSize: '16px' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="当前温度"
                  value={weatherData.temperature}
                  suffix="°C"
                  valueStyle={{
                    color: weatherData.temperature > 30 ? '#ff4d4f' : '#1890ff',
                  }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="天气状况"
                  value={weatherData.weatherType}
                  valueStyle={{ fontSize: '16px' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="高温预警"
                  value={heatWarningLevelMap[weatherData.heatWarningLevel]}
                  valueStyle={{
                    color:
                      heatWarningLevelColorMap[weatherData.heatWarningLevel],
                    fontSize: '16px',
                  }}
                  prefix={
                    weatherData.heatWarningLevel !== 'none' && (
                      <WarningOutlined />
                    )
                  }
                />
                {weatherData.heatWarningLevel !== 'none' && (
                  <Tag
                    color={
                      heatWarningLevelColorMap[weatherData.heatWarningLevel]
                    }
                    style={{ marginTop: 8 }}
                  >
                    紧急模式已启动，价格上浮{' '}
                    {priceInfo
                      ? `${(priceInfo.emergencyFactor - 1) * 100}%`
                      : ''}
                  </Tag>
                )}
              </Col>
            </Row>
          </Card>
        )}

        {/* 产品选择和价格信息 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ marginBottom: 16 }}>
                <span style={{ marginRight: 8 }}>选择产品:</span>
                <Select
                  style={{ width: 200 }}
                  value={selectedIceType?.id}
                  onChange={handleIceTypeChange}
                >
                  {iceTypes.map((iceType) => (
                    <Option key={iceType.id} value={iceType.id}>
                      {iceType.name}
                    </Option>
                  ))}
                </Select>
              </div>
              {selectedIceType && (
                <div>
                  <p>
                    <strong>产品类别:</strong>{' '}
                    {selectedIceType.category === 'food'
                      ? '食用冰'
                      : selectedIceType.category === 'industrial'
                      ? '工业冰'
                      : '降温冰'}
                  </p>
                  <p>
                    <strong>规格:</strong>{' '}
                    {selectedIceType.specifications.shape},{' '}
                    {selectedIceType.specifications.size}
                  </p>
                  <p>
                    <strong>重量:</strong>{' '}
                    {selectedIceType.specifications.weight}
                  </p>
                  <p>
                    <strong>温度范围:</strong>{' '}
                    {selectedIceType.specifications.temperatureRange.min}°C 至{' '}
                    {selectedIceType.specifications.temperatureRange.max}°C
                  </p>
                  <p>
                    <strong>持续时间:</strong> 约{' '}
                    {selectedIceType.specifications.duration} 小时
                  </p>
                </div>
              )}
            </Col>
            <Col span={8}>
              {priceInfo && (
                <div>
                  <div
                    style={{
                      marginBottom: 16,
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <span style={{ marginRight: 8 }}>
                      <strong>基础价格:</strong>
                    </span>
                    {editingBasePrice ? (
                      <>
                        <InputNumber
                          value={newBasePrice}
                          onChange={(value) => setNewBasePrice(value || 0)}
                          min={0}
                          style={{ width: 120 }}
                        />
                        <span style={{ margin: '0 8px' }}>
                          {priceInfo.unit}
                        </span>
                        <Button
                          type="primary"
                          icon={<SaveOutlined />}
                          onClick={handleSaveBasePrice}
                          size="small"
                        >
                          保存
                        </Button>
                        <Button
                          onClick={() => {
                            setEditingBasePrice(false);
                            setNewBasePrice(priceInfo.basePrice);
                          }}
                          size="small"
                          style={{ marginLeft: 8 }}
                        >
                          取消
                        </Button>
                      </>
                    ) : (
                      <>
                        <span>
                          {priceInfo.basePrice} {priceInfo.unit}
                        </span>
                        <Button
                          type="link"
                          icon={<EditOutlined />}
                          onClick={() => setEditingBasePrice(true)}
                          size="small"
                        >
                          编辑
                        </Button>
                      </>
                    )}
                  </div>
                  <p>
                    <strong>当前季节:</strong>{' '}
                    {seasonNameMap[getCurrentSeason()]}
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      系数: {getCurrentSeasonalFactor()}
                    </Tag>
                  </p>
                  <p>
                    <strong>紧急情况系数:</strong> {priceInfo.emergencyFactor}
                    <Button
                      type="link"
                      icon={<EditOutlined />}
                      onClick={() => setEmergencyModalVisible(true)}
                      size="small"
                    >
                      编辑
                    </Button>
                  </p>
                  <Divider />
                  <div>
                    <h4>价格计算器</h4>
                    <Form form={form} layout="inline">
                      <Form.Item label="数量">
                        <InputNumber min={1} defaultValue={1} />
                      </Form.Item>
                      <Form.Item>
                        <Button
                          type="primary"
                          onClick={() => {
                            const quantity =
                              form.getFieldValue('quantity') || 1;
                            message.info(
                              `${quantity} 吨的实际价格为: ${calculateActualPrice(
                                quantity,
                              )} 元/吨`,
                            );
                          }}
                        >
                          计算价格
                        </Button>
                      </Form.Item>
                    </Form>
                  </div>
                </div>
              )}
            </Col>
            <Col span={8}>
              {weatherData && weatherData.heatWarningLevel !== 'none' && (
                <Alert
                  message="高温预警"
                  description={
                    <>
                      <p>当前温度: {weatherData.temperature}°C</p>
                      <p>
                        预警等级:{' '}
                        {heatWarningLevelMap[weatherData.heatWarningLevel]}
                      </p>
                      <p>
                        紧急模式已启动，价格上浮{' '}
                        {priceInfo
                          ? `${(priceInfo.emergencyFactor - 1) * 100}%`
                          : ''}
                      </p>
                    </>
                  }
                  type="warning"
                  showIcon
                  icon={<ThunderboltOutlined />}
                />
              )}
            </Col>
          </Row>
        </Card>

        {/* 价格策略管理 */}
        <Card>
          <Tabs defaultActiveKey="priceHistory">
            <TabPane
              tab={
                <span>
                  <LineChartOutlined />
                  价格走势
                </span>
              }
              key="priceHistory"
            >
              {priceInfo ? (
                <PriceChart historyPrices={priceInfo.historyPrices} />
              ) : (
                <Empty description="暂无价格历史数据" />
              )}
            </TabPane>
            <TabPane
              tab={
                <span>
                  <AreaChartOutlined />
                  阶梯定价
                </span>
              }
              key="bulkDiscounts"
            >
              {priceInfo ? (
                <BulkDiscountTable
                  bulkDiscounts={priceInfo.bulkDiscounts}
                  onSave={handleSaveBulkDiscounts}
                />
              ) : (
                <Empty description="暂无阶梯定价数据" />
              )}
            </TabPane>
            <TabPane
              tab={
                <span>
                  <AreaChartOutlined />
                  季节性系数
                </span>
              }
              key="seasonalFactors"
            >
              {priceInfo ? (
                <SeasonalFactorTable
                  seasonalFactors={priceInfo.seasonalFactors}
                  onSave={handleSaveSeasonalFactors}
                />
              ) : (
                <Empty description="暂无季节性系数数据" />
              )}
            </TabPane>
          </Tabs>
        </Card>

        {/* 紧急情况系数模态框 */}
        <EmergencyPriceModal
          visible={emergencyModalVisible}
          initialValue={priceInfo?.emergencyFactor || 1.5}
          onCancel={() => setEmergencyModalVisible(false)}
          onSave={handleSaveEmergencyFactor}
        />
      </Spin>
    </PageContainer>
  );
};

export default PricingManagement;
