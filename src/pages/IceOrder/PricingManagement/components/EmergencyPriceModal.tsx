import { WarningOutlined } from '@ant-design/icons';
import { Alert, InputNumber, Modal, Slider, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Text } = Typography;

interface EmergencyPriceModalProps {
  visible: boolean;
  initialValue: number;
  onCancel: () => void;
  onSave: (value: number) => void;
}

const EmergencyPriceModal: React.FC<EmergencyPriceModalProps> = ({
  visible,
  initialValue,
  onCancel,
  onSave,
}) => {
  const [value, setValue] = useState<number>(initialValue);

  // 当初始值变化时，更新当前值
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  // 处理保存
  const handleSave = () => {
    onSave(value);
  };

  // 计算价格变化百分比
  const calculatePercentage = (factor: number) => {
    return ((factor - 1) * 100).toFixed(0);
  };

  return (
    <Modal
      title={
        <div>
          <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
          设置紧急情况价格系数
        </div>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSave}
      okText="保存"
      cancelText="取消"
    >
      <Alert
        message="紧急情况说明"
        description="紧急情况价格系数将在高温预警等紧急情况下自动应用。系数越高，价格上浮越多。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <div style={{ marginBottom: 16 }}>
        <Text>紧急情况价格系数：</Text>
        <InputNumber
          min={1}
          max={3}
          step={0.1}
          value={value}
          onChange={(newValue) => setValue(newValue || 1)}
          style={{ width: 100, margin: '0 16px' }}
        />
        <Text type="danger">价格上浮 {calculatePercentage(value)}%</Text>
      </div>

      <Slider
        min={1}
        max={3}
        step={0.1}
        value={value}
        onChange={(newValue) => setValue(newValue)}
        marks={{
          1: '不上浮',
          1.5: '上浮50%',
          2: '上浮100%',
          2.5: '上浮150%',
          3: '上浮200%',
        }}
      />

      <div style={{ marginTop: 16 }}>
        <Text type="secondary">
          建议值：
          <ul>
            <li>黄色预警：1.2-1.5（上浮20%-50%）</li>
            <li>橙色预警：1.5-2.0（上浮50%-100%）</li>
            <li>红色预警：2.0-3.0（上浮100%-200%）</li>
          </ul>
        </Text>
      </div>
    </Modal>
  );
};

export default EmergencyPriceModal;
