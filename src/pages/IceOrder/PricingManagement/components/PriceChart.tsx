import { PriceInfo } from '@/services/iceOrder/types/enhanced';
import { Card, Empty, Table, Typography } from 'antd';
import React from 'react';

interface PriceChartProps {
  historyPrices: PriceInfo['historyPrices'];
}

const { Text } = Typography;

const PriceChart: React.FC<PriceChartProps> = ({ historyPrices }) => {
  if (!historyPrices || historyPrices.length === 0) {
    return <Empty description="暂无价格历史数据" />;
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
      2,
      '0',
    )}-${String(date.getDate()).padStart(2, '0')}`;
  };

  // 准备表格数据
  const dataSource = historyPrices.map((item, index) => ({
    key: index,
    date: formatDate(item.date),
    price: item.price,
    change: index > 0 ? item.price - historyPrices[index - 1].price : 0,
    changePercent:
      index > 0
        ? (
            ((item.price - historyPrices[index - 1].price) /
              historyPrices[index - 1].price) *
            100
          ).toFixed(2) + '%'
        : '-',
  }));

  // 表格列定义
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price}`,
    },
    {
      title: '价格变化',
      dataIndex: 'change',
      key: 'change',
      render: (change: number) => {
        if (change === 0) return '-';
        const color = change > 0 ? '#f5222d' : '#52c41a';
        const prefix = change > 0 ? '+' : '';
        return (
          <Text style={{ color }}>
            {prefix}
            {change.toFixed(2)}
          </Text>
        );
      },
    },
    {
      title: '变化百分比',
      dataIndex: 'changePercent',
      key: 'changePercent',
      render: (percent: string, record: any) => {
        if (record.change === 0) return '-';
        const color = record.change > 0 ? '#f5222d' : '#52c41a';
        return <Text style={{ color }}>{percent}</Text>;
      },
    },
  ];

  // 计算价格统计信息
  const prices = historyPrices.map((item) => item.price);
  const maxPrice = Math.max(...prices);
  const minPrice = Math.min(...prices);
  const avgPrice =
    prices.reduce((sum, price) => sum + price, 0) / prices.length;
  const latestPrice = prices[prices.length - 1];
  const priceChange =
    prices.length > 1 ? latestPrice - prices[prices.length - 2] : 0;
  const priceChangePercent =
    prices.length > 1
      ? ((priceChange / prices[prices.length - 2]) * 100).toFixed(2)
      : 0;

  return (
    <Card
      title="历史价格走势"
      extra={
        <Text>
          最新价格:{' '}
          <Text
            strong
            style={{
              color:
                priceChange > 0
                  ? '#f5222d'
                  : priceChange < 0
                  ? '#52c41a'
                  : 'inherit',
            }}
          >
            ¥{latestPrice}{' '}
            {priceChange !== 0
              ? `(${priceChange > 0 ? '+' : ''}${priceChangePercent}%)`
              : ''}
          </Text>
        </Text>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Text>最高价: ¥{maxPrice}</Text>
        <Text style={{ marginLeft: 16 }}>最低价: ¥{minPrice}</Text>
        <Text style={{ marginLeft: 16 }}>平均价: ¥{avgPrice.toFixed(2)}</Text>
      </div>
      <Table
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        size="small"
      />
    </Card>
  );
};

export default PriceChart;
