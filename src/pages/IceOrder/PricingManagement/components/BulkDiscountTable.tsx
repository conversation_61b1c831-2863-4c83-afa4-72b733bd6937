import { PriceInfo } from '@/services/iceOrder/types/enhanced';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  InputNumber,
  message,
  Popconfirm,
  Space,
  Table,
} from 'antd';
import React, { useEffect, useState } from 'react';

interface BulkDiscountTableProps {
  bulkDiscounts: PriceInfo['bulkDiscounts'];
  onSave: (bulkDiscounts: PriceInfo['bulkDiscounts']) => void;
}

interface EditableBulkDiscount {
  key: string;
  quantity: number;
  discount: number;
  isNew?: boolean;
  isEditing?: boolean;
}

const BulkDiscountTable: React.FC<BulkDiscountTableProps> = ({
  bulkDiscounts,
  onSave,
}) => {
  const [dataSource, setDataSource] = useState<EditableBulkDiscount[]>([]);
  const [editingKey, setEditingKey] = useState<string>('');

  // 初始化数据
  useEffect(() => {
    const data = bulkDiscounts.map((item, index) => ({
      key: `${index}`,
      quantity: item.quantity,
      discount: item.discount,
    }));
    setDataSource(data);
  }, [bulkDiscounts]);

  // 是否正在编辑
  const isEditing = (record: EditableBulkDiscount) => record.key === editingKey;

  // 开始编辑
  const edit = (record: EditableBulkDiscount) => {
    setEditingKey(record.key);
  };

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
    // 移除新添加但未保存的行
    setDataSource((prev) =>
      prev.filter((item) => !item.isNew || item.key !== editingKey),
    );
  };

  // 保存行
  const save = (key: string) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => key === item.key);
    if (index > -1) {
      const item = newData[index];
      newData.splice(index, 1, { ...item, isEditing: false, isNew: false });
      setDataSource(newData);
      setEditingKey('');
    }
  };

  // 添加新行
  const addRow = () => {
    const newKey = `new-${Date.now()}`;
    const newRow: EditableBulkDiscount = {
      key: newKey,
      quantity: 0,
      discount: 1,
      isNew: true,
      isEditing: true,
    };
    setDataSource([...dataSource, newRow]);
    setEditingKey(newKey);
  };

  // 删除行
  const deleteRow = (key: string) => {
    setDataSource(dataSource.filter((item) => item.key !== key));
  };

  // 保存所有更改
  const saveAll = () => {
    // 验证数据
    for (const item of dataSource) {
      if (item.quantity <= 0) {
        message.error('数量必须大于0');
        return;
      }
      if (item.discount <= 0 || item.discount > 1) {
        message.error('折扣必须在0-1之间');
        return;
      }
    }

    // 转换为API格式
    const newBulkDiscounts = dataSource.map((item) => ({
      quantity: item.quantity,
      discount: item.discount,
    }));

    // 按数量排序
    newBulkDiscounts.sort((a, b) => a.quantity - b.quantity);

    // 保存
    onSave(newBulkDiscounts);
  };

  // 表格列定义
  const columns = [
    {
      title: '数量阈值(吨)',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (text: number, record: EditableBulkDiscount) => {
        const editable = isEditing(record);
        return editable ? (
          <InputNumber
            min={1}
            defaultValue={text}
            onChange={(value) => {
              const newData = [...dataSource];
              const index = newData.findIndex(
                (item) => record.key === item.key,
              );
              if (index > -1) {
                newData[index].quantity = value || 0;
                setDataSource(newData);
              }
            }}
          />
        ) : (
          text
        );
      },
    },
    {
      title: '折扣系数',
      dataIndex: 'discount',
      key: 'discount',
      render: (text: number, record: EditableBulkDiscount) => {
        const editable = isEditing(record);
        return editable ? (
          <InputNumber
            min={0.1}
            max={1}
            step={0.01}
            defaultValue={text}
            onChange={(value) => {
              const newData = [...dataSource];
              const index = newData.findIndex(
                (item) => record.key === item.key,
              );
              if (index > -1) {
                newData[index].discount = value || 1;
                setDataSource(newData);
              }
            }}
          />
        ) : (
          text
        );
      },
    },
    {
      title: '折扣比例',
      key: 'discountPercentage',
      render: (_: any, record: EditableBulkDiscount) => {
        return `${(1 - record.discount) * 100}%`;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: EditableBulkDiscount) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              type="link"
              icon={<SaveOutlined />}
              onClick={() => save(record.key)}
            >
              保存
            </Button>
            <Popconfirm
              title="确定取消吗？"
              onConfirm={cancel}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                取消
              </Button>
            </Popconfirm>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              disabled={editingKey !== ''}
              icon={<EditOutlined />}
              onClick={() => edit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => deleteRow(record.key)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <Card
      title="阶梯定价设置"
      extra={
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addRow}
            disabled={editingKey !== ''}
          >
            添加阶梯
          </Button>
          <Button type="primary" onClick={saveAll} disabled={editingKey !== ''}>
            保存所有更改
          </Button>
        </Space>
      }
    >
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="key"
        pagination={false}
      />
      <div style={{ marginTop: 16 }}>
        <p>说明：</p>
        <p>1. 数量阈值：当订单数量大于等于此值时，应用对应的折扣系数</p>
        <p>2. 折扣系数：实际价格 = 基础价格 × 折扣系数</p>
        <p>3. 折扣比例：表示相对于基础价格的折扣百分比</p>
      </div>
    </Card>
  );
};

export default BulkDiscountTable;
