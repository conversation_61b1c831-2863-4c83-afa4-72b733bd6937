import { PriceInfo } from '@/services/iceOrder/types/enhanced';
import { SaveOutlined } from '@ant-design/icons';
import { Button, Card, InputNumber, message, Table } from 'antd';
import React, { useEffect, useState } from 'react';

interface SeasonalFactorTableProps {
  seasonalFactors: PriceInfo['seasonalFactors'];
  onSave: (seasonalFactors: PriceInfo['seasonalFactors']) => void;
}

interface EditableSeasonalFactor {
  key: string;
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  factor: number;
  name: string;
}

const SeasonalFactorTable: React.FC<SeasonalFactorTableProps> = ({
  seasonalFactors,
  onSave,
}) => {
  const [dataSource, setDataSource] = useState<EditableSeasonalFactor[]>([]);
  const [changed, setChanged] = useState<boolean>(false);

  // 季节名称映射
  const seasonNameMap = {
    spring: '春季',
    summer: '夏季',
    autumn: '秋季',
    winter: '冬季',
  };

  // 初始化数据
  useEffect(() => {
    const data = seasonalFactors.map((item, index) => ({
      key: `${index}`,
      season: item.season,
      factor: item.factor,
      name: seasonNameMap[item.season],
    }));
    setDataSource(data);
    setChanged(false);
  }, [seasonalFactors]);

  // 更新系数
  const updateFactor = (key: string, value: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => key === item.key);
    if (index > -1) {
      newData[index].factor = value;
      setDataSource(newData);
      setChanged(true);
    }
  };

  // 保存所有更改
  const saveAll = () => {
    // 验证数据
    for (const item of dataSource) {
      if (item.factor <= 0) {
        message.error('系数必须大于0');
        return;
      }
    }

    // 转换为API格式
    const newSeasonalFactors = dataSource.map((item) => ({
      season: item.season,
      factor: item.factor,
    }));

    // 保存
    onSave(newSeasonalFactors);
    setChanged(false);
  };

  // 表格列定义
  const columns = [
    {
      title: '季节',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '价格系数',
      dataIndex: 'factor',
      key: 'factor',
      render: (text: number, record: EditableSeasonalFactor) => (
        <InputNumber
          min={0.1}
          step={0.1}
          value={text}
          onChange={(value) => updateFactor(record.key, value || 1)}
        />
      ),
    },
    {
      title: '价格变化',
      key: 'priceChange',
      render: (_: any, record: EditableSeasonalFactor) => {
        const change = (record.factor - 1) * 100;
        const color = change > 0 ? '#ff4d4f' : change < 0 ? '#52c41a' : '';
        const prefix = change > 0 ? '+' : '';
        return (
          <span style={{ color }}>
            {prefix}
            {change.toFixed(0)}%
          </span>
        );
      },
    },
    {
      title: '说明',
      key: 'description',
      render: (_: any, record: EditableSeasonalFactor) => {
        if (record.season === 'summer') {
          return '夏季需求高，价格上浮';
        } else if (record.season === 'winter') {
          return '冬季需求低，价格下浮';
        } else {
          return '标准季节价格';
        }
      },
    },
  ];

  return (
    <Card
      title="季节性系数设置"
      extra={
        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={saveAll}
          disabled={!changed}
        >
          保存更改
        </Button>
      }
    >
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="key"
        pagination={false}
      />
      <div style={{ marginTop: 16 }}>
        <p>说明：</p>
        <p>1. 价格系数：实际价格 = 基础价格 × 季节性系数</p>
        <p>2. 系数大于1表示价格上浮，小于1表示价格下浮</p>
        <p>3. 当前季节的系数会自动应用到所有订单中</p>
      </div>
    </Card>
  );
};

export default SeasonalFactorTable;
