import { IceType } from './types';

const API_BASE = '/api/orders';

export const fetchIceTypes = async (): Promise<IceType[]> => {
  try {
    const response = await fetch(`${API_BASE}/ice-types`);
    if (!response.ok) {
      throw new Error('Failed to fetch ice types');
    }
    const data = await response.json();

    // 数据验证
    if (!Array.isArray(data)) {
      throw new Error('Invalid ice types data format');
    }

    return data.map(item => ({
      id: Number(item.id),
      name: String(item.name)
    }));
  } catch (error) {
    console.error('Error fetching ice types:', error);
    return []; // 返回空数组而不是抛出错误
  }
};

// 其他订单相关API...
