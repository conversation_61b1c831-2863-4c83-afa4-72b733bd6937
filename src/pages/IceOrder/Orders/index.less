.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.statusTag {
  min-width: 70px;
  text-align: center;
}

.orderItem {
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.orderItemHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.orderItemContent {
  display: flex;
  justify-content: space-between;
}

.orderItemQuantity {
  font-weight: bold;
}

.orderItemSize {
  color: rgba(0, 0, 0, 45%);
}
