import { OrderStatusColorMap, OrderStatusMap } from '@/services/iceOrder';
import {
  deleteOrder,
  getOrderDetail,
  getOrderList,
  updateOrderStatus,
} from '@/services/iceOrder/iceOrderService';
import { getIceTypeList, IceType } from '@/services/iceOrder/iceTypeService';
import { IceOrder, OrderStatus } from '@/services/iceOrder/types';
import { getUserList, User } from '@/services/iceOrder/userService';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Descriptions,
  Drawer,
  List,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import OrderForm from './components/OrderForm';

const { Title } = Typography;
const { Option } = Select;

const OrderManagement: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [orders, setOrders] = useState<IceOrder[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [iceTypes, setIceTypes] = useState<IceType[]>([]);
  const [currentOrder, setCurrentOrder] = useState<IceOrder | null | undefined>(
    null,
  );
  const [formVisible, setFormVisible] = useState<boolean>(false);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [statusModalVisible, setStatusModalVisible] = useState<boolean>(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | null>(
    null,
  );

  // 加载订单列表
  const loadOrders = async () => {
    setLoading(true);
    const response = await getOrderList();
    if (response.status === 200 && response.data) {
      setOrders(response.data);
    } else {
      message.error(response.error || '加载订单列表失败');
      console.error('加载订单列表失败:', response.error);
    }
    setLoading(false);
  };

  // 加载用户和冰块类型数据
  const loadMasterData = async () => {
    const [userResponse, iceTypeResponse] = await Promise.all([
      getUserList(),
      getIceTypeList(),
    ]);

    if (userResponse.status === 200 && userResponse.data) {
      setUsers(userResponse.data);
    } else {
      message.error(userResponse.error || '加载用户数据失败');
      console.error('加载用户数据失败:', userResponse.error);
    }

    if (iceTypeResponse.status === 200 && iceTypeResponse.data) {
      setIceTypes(iceTypeResponse.data);
    } else {
      message.error(iceTypeResponse.error || '加载冰块类型数据失败');
      console.error('加载冰块类型数据失败:', iceTypeResponse.error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOrders();
    loadMasterData();
  }, []);

  // 查看订单详情
  const handleViewOrder = async (id: number) => {
    try {
      const response = await getOrderDetail(id);
      if (response.status === 200 && response.data) {
        setCurrentOrder(response.data);
        setDetailVisible(true);
      } else {
        setCurrentOrder(null);
        message.error('获取订单详情失败');
      }
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('获取订单详情失败');
      setCurrentOrder(null);
    }
  };

  // 打开新增订单表单
  const handleAddOrder = () => {
    setCurrentOrder(null);
    setFormVisible(true);
  };

  // 打开编辑订单表单
  const handleEditOrder = async (id: number) => {
    try {
      const response = await getOrderDetail(id);
      if (response.status === 200 && response.data) {
        setCurrentOrder(response.data);
        setFormVisible(true);
      } else {
        setCurrentOrder(null);
        message.error('获取订单详情失败');
      }
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('获取订单详情失败');
      setCurrentOrder(null);
    }
  };

  // 删除订单
  const handleDeleteOrder = async (id: number) => {
    try {
      const response = await deleteOrder(id);
      if (response.status === 200) {
        message.success('删除成功');
        loadOrders();
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      message.error('删除订单失败');
    }
  };

  // 打开更新状态模态框
  const handleUpdateStatusClick = (order: IceOrder) => {
    setCurrentOrder(order);
    setSelectedStatus(order.status);
    setStatusModalVisible(true);
  };

  // 更新订单状态
  const handleUpdateStatus = async () => {
    if (!currentOrder || !selectedStatus) return;

    try {
      const response = await updateOrderStatus(currentOrder.id, selectedStatus);
      if (response.status === 200) {
        message.success('状态更新成功');
        setStatusModalVisible(false);
        loadOrders();
      }
    } catch (error) {
      console.error('更新订单状态失败:', error);
      message.error('更新订单状态失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '送达日期',
      dataIndex: 'deliveryDateTime',
      key: 'deliveryDateTime',
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '送达地址',
      dataIndex: 'deliveryAddress',
      key: 'deliveryAddress',
      ellipsis: true,
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: OrderStatus) => (
        <Tag color={OrderStatusColorMap[status]}>{OrderStatusMap[status]}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: IceOrder) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record.id)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditOrder(record.id)}
          >
            编辑
          </Button>
          <Button type="text" onClick={() => handleUpdateStatusClick(record)}>
            更新状态
          </Button>
          <Popconfirm
            title="确定要删除这个订单吗？"
            onConfirm={() => handleDeleteOrder(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '订单管理',
        subTitle: '管理冰块订单信息',
      }}
    >
      <Card>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}
        >
          <Title level={4}>订单列表</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddOrder}
          >
            新增订单
          </Button>
        </div>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={orders}
            rowKey="id"
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        </Spin>
      </Card>

      {/* 订单表单 */}
      <OrderForm
        visible={formVisible}
        onCancel={() => {
          setFormVisible(false);
          setCurrentOrder(null);
        }}
        onSuccess={() => {
          setFormVisible(false);
          setCurrentOrder(null);
          loadOrders();
        }}
        initialValues={
          currentOrder || {
            id: 0,
            userId: 0,
            username: users.length > 0 ? users[0].username : '',
            deliveryDateTime: new Date().toISOString(),
            deliveryAddress: '',
            contactPerson: '',
            contactPhone: '',
            status: OrderStatus.PENDING,
            items: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        }
        users={users}
        iceTypes={iceTypes}
      />

      {/* 订单详情抽屉 */}
      <Drawer
        title="订单详情"
        width={600}
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setDetailVisible(false)}>关闭</Button>
          </Space>
        }
      >
        {currentOrder && (
          <>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="订单ID">
                {currentOrder.id}
              </Descriptions.Item>
              <Descriptions.Item label="用户">
                {currentOrder.username}
              </Descriptions.Item>
              <Descriptions.Item label="送达日期">
                {new Date(currentOrder.deliveryDateTime).toLocaleString(
                  'zh-CN',
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={OrderStatusColorMap[currentOrder.status]}>
                  {OrderStatusMap[currentOrder.status]}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="送达地址" span={2}>
                {currentOrder.deliveryAddress}
              </Descriptions.Item>
              <Descriptions.Item label="联系人">
                {currentOrder.contactPerson}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                {currentOrder.contactPhone}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(currentOrder.createdAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(currentOrder.updatedAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
            </Descriptions>

            <Title level={5} style={{ margin: '16px 0' }}>
              订单项
            </Title>
            <List
              bordered
              dataSource={currentOrder.items}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={`${item.iceTypeName} (${item.size})`}
                    description={`数量: ${item.quantity}`}
                  />
                </List.Item>
              )}
            />
          </>
        )}
      </Drawer>

      {/* 更新状态模态框 */}
      <Modal
        title="更新订单状态"
        open={statusModalVisible}
        onOk={handleUpdateStatus}
        onCancel={() => setStatusModalVisible(false)}
      >
        <Select
          style={{ width: '100%' }}
          placeholder="选择订单状态"
          value={selectedStatus}
          onChange={(value) => setSelectedStatus(value)}
        >
          {Object.values(OrderStatus).map((status) => (
            <Option key={status} value={status}>
              <Tag color={OrderStatusColorMap[status]}>
                {OrderStatusMap[status]}
              </Tag>
            </Option>
          ))}
        </Select>
      </Modal>
    </PageContainer>
  );
};

export default OrderManagement;
