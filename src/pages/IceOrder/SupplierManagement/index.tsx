/**
 * 供应商信用体系页面
 * 管理供应商信息、信用评分、评价和黑名单
 */
import { supplierService } from '@/services/iceOrder/enhanced';
import { Supplier } from '@/services/iceOrder/types/enhanced';
import {
  ClockCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
  StarOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  Input,
  message,
  Popconfirm,
  Progress,
  Rate,
  Row,
  Space,
  Statistic,
  Table,
  Tabs,
  Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';
import BlacklistModal from './components/BlacklistModal';
import SupplierForm from './components/SupplierForm';
import SupplierRatings from './components/SupplierRatings';

const { TabPane } = Tabs;

const SupplierManagement: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState<boolean>(true); // 加载状态
  const [suppliers, setSuppliers] = useState<Supplier[]>([]); // 供应商列表数据
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(
    null,
  ); // 当前选中的供应商
  const [formVisible, setFormVisible] = useState<boolean>(false); // 表单显示状态
  const [ratingsVisible, setRatingsVisible] = useState<boolean>(false); // 评价弹窗显示状态
  const [blacklistVisible, setBlacklistVisible] = useState<boolean>(false); // 黑名单弹窗显示状态
  const [searchText, setSearchText] = useState<string>(''); // 搜索文本
  const [activeTab, setActiveTab] = useState<string>('all'); // 当前激活的标签页

  /**
   * 加载供应商列表数据
   * 从服务器获取供应商列表，如果失败则使用模拟数据
   */
  const loadSuppliers = async () => {
    setLoading(true);
    try {
      const response = await supplierService.getSupplierList();
      if (response.status === 200) {
        setSuppliers(response.data);
      }
    } catch (error) {
      console.error('加载供应商列表失败:', error);
      message.error('加载供应商列表失败');

      // 使用模拟数据
      setSuppliers([
        {
          id: 1,
          factoryId: 1,
          name: '北冰洋冰业',
          description: '专业生产各类食用冰和工业冰',
          contactPerson: '张经理',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '建国路88号',
          },
          businessLicense: '**********',
          creditScore: 85,
          transactionVolume: 5000,
          starRating: 4.5,
          onTimeDeliveryRate: 95,
          products: [1, 2, 3],
          blacklisted: false,
          createdAt: '2023-01-15T08:30:00',
        },
        {
          id: 2,
          factoryId: 2,
          name: '上海冷冻厂',
          description: '上海地区最大的工业冰生产商',
          contactPerson: '李经理',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '上海市',
            city: '上海市',
            district: '浦东新区',
            detail: '陆家嘴1号',
          },
          businessLicense: '**********',
          creditScore: 92,
          transactionVolume: 8000,
          starRating: 4.8,
          onTimeDeliveryRate: 98,
          products: [1, 4, 5],
          blacklisted: false,
          createdAt: '2023-02-20T10:15:00',
        },
        {
          id: 3,
          factoryId: 3,
          name: '广州冰块厂',
          description: '专注于食用冰生产',
          contactPerson: '王经理',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '广东省',
            city: '广州市',
            district: '天河区',
            detail: '天河路123号',
          },
          businessLicense: '**********',
          creditScore: 75,
          transactionVolume: 3000,
          starRating: 3.5,
          onTimeDeliveryRate: 85,
          products: [2, 3],
          blacklisted: false,
          createdAt: '2023-03-10T14:20:00',
        },
        {
          id: 4,
          factoryId: 4,
          name: '黑心冰厂',
          description: '多次违规生产',
          contactPerson: '赵某',
          contactPhone: '***********',
          email: '<EMAIL>',
          address: {
            province: '河北省',
            city: '石家庄市',
            district: '长安区',
            detail: '长安路456号',
          },
          businessLicense: '**********',
          creditScore: 30,
          transactionVolume: 1000,
          starRating: 1.5,
          onTimeDeliveryRate: 60,
          products: [1],
          blacklisted: true,
          blacklistReason: '多次使用不合格原料，产品质量不达标',
          createdAt: '2023-04-05T09:45:00',
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 页面初始化时加载供应商列表数据
   */
  useEffect(() => {
    loadSuppliers();
  }, []);

  /**
   * 处理添加供应商操作
   * 清空选中供应商并显示表单
   */
  const handleAddSupplier = () => {
    setSelectedSupplier(null);
    setFormVisible(true);
  };

  /**
   * 处理编辑供应商操作
   * 设置选中供应商并显示表单
   * @param supplier 要编辑的供应商对象
   */
  const handleEditSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setFormVisible(true);
  };

  /**
   * 处理删除供应商操作
   * 调用API删除指定供应商
   * @param id 要删除的供应商ID
   */
  const handleDeleteSupplier = async (id: number) => {
    try {
      const response = await supplierService.deleteSupplier(id);
      if (response.status === 200) {
        message.success('删除成功');
        loadSuppliers();
      }
    } catch (error) {
      console.error('删除供应商失败:', error);
      message.error('删除供应商失败');
    }
  };

  /**
   * 处理查看供应商评价操作
   * 设置选中供应商并显示评价弹窗
   * @param supplier 要查看评价的供应商对象
   */
  const handleViewRatings = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setRatingsVisible(true);
  };

  /**
   * 处理将供应商加入黑名单操作
   * 设置选中供应商并显示黑名单弹窗
   * @param supplier 要加入黑名单的供应商对象
   */
  const handleBlacklist = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setBlacklistVisible(true);
  };

  /**
   * 处理将供应商从黑名单移除操作
   * 调用API将指定供应商从黑名单移除
   * @param id 要移除黑名单的供应商ID
   */
  const handleRemoveFromBlacklist = async (id: number) => {
    try {
      const response = await supplierService.removeSupplierFromBlacklist(id);
      if (response.status === 200) {
        message.success('已从黑名单移除');
        loadSuppliers();
      }
    } catch (error) {
      console.error('从黑名单移除失败:', error);
      message.error('从黑名单移除失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟从黑名单移除成功');
        loadSuppliers();
      }
    }
  };

  /**
   * 表单提交成功回调
   * 关闭表单并重新加载供应商列表
   */
  const handleFormSuccess = () => {
    setFormVisible(false);
    loadSuppliers();
  };

  /**
   * 黑名单操作成功回调
   * 关闭黑名单弹窗并重新加载供应商列表
   */
  const handleBlacklistSuccess = () => {
    setBlacklistVisible(false);
    loadSuppliers();
  };

  /**
   * 过滤供应商列表
   * 根据标签和搜索文本过滤供应商列表
   */
  const filteredSuppliers = suppliers.filter((supplier) => {
    // 根据标签过滤
    if (activeTab === 'blacklisted' && !supplier.blacklisted) return false;
    if (activeTab === 'normal' && supplier.blacklisted) return false;
    if (activeTab === 'highCredit' && supplier.creditScore < 80) return false;
    if (activeTab === 'lowCredit' && supplier.creditScore >= 60) return false;

    // 根据搜索文本过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      return (
        supplier.name.toLowerCase().includes(searchLower) ||
        supplier.description.toLowerCase().includes(searchLower) ||
        supplier.contactPerson.toLowerCase().includes(searchLower) ||
        supplier.contactPhone.includes(searchText) ||
        supplier.email.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  /**
   * 获取信用评分对应的颜色
   * 根据信用评分返回不同的颜色代码
   * @param score 信用评分
   * @returns 颜色代码
   */
  const getCreditScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a'; // 绿色
    if (score >= 60) return '#faad14'; // 黄色
    return '#f5222d'; // 红色
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '供应商名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
    },
    {
      title: '信用评分',
      dataIndex: 'creditScore',
      key: 'creditScore',
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          status={score < 60 ? 'exception' : 'normal'}
          strokeColor={getCreditScoreColor(score)}
          format={(percent) => `${percent}分`}
        />
      ),
      sorter: (a: Supplier, b: Supplier) => a.creditScore - b.creditScore,
    },
    {
      title: '星级评价',
      dataIndex: 'starRating',
      key: 'starRating',
      render: (rating: number) => (
        <Rate disabled defaultValue={rating} allowHalf />
      ),
      sorter: (a: Supplier, b: Supplier) => a.starRating - b.starRating,
    },
    {
      title: '准时交付率',
      dataIndex: 'onTimeDeliveryRate',
      key: 'onTimeDeliveryRate',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate < 80 ? 'exception' : 'normal'}
          strokeColor={
            rate >= 90 ? '#52c41a' : rate >= 80 ? '#faad14' : '#f5222d'
          }
        />
      ),
      sorter: (a: Supplier, b: Supplier) =>
        a.onTimeDeliveryRate - b.onTimeDeliveryRate,
    },
    {
      title: '状态',
      dataIndex: 'blacklisted',
      key: 'blacklisted',
      render: (blacklisted: boolean) =>
        blacklisted ? (
          <Tag color="red">黑名单</Tag>
        ) : (
          <Tag color="green">正常</Tag>
        ),
      filters: [
        { text: '正常', value: false },
        { text: '黑名单', value: true },
      ],
      onFilter: (value: boolean, record: Supplier) =>
        record.blacklisted === value,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Supplier) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditSupplier(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<StarOutlined />}
            onClick={() => handleViewRatings(record)}
          >
            评价
          </Button>
          {record.blacklisted ? (
            <Popconfirm
              title="确定要将此供应商从黑名单中移除吗？"
              onConfirm={() => handleRemoveFromBlacklist(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<StopOutlined />}
                style={{ color: 'green' }}
              >
                移出黑名单
              </Button>
            </Popconfirm>
          ) : (
            <Button
              type="text"
              danger
              icon={<StopOutlined />}
              onClick={() => handleBlacklist(record)}
            >
              加入黑名单
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个供应商吗？"
            onConfirm={() => handleDeleteSupplier(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 统计数据
  const statistics = {
    totalSuppliers: suppliers.length,
    blacklistedSuppliers: suppliers.filter((s) => s.blacklisted).length,
    highCreditSuppliers: suppliers.filter((s) => s.creditScore >= 80).length,
    lowCreditSuppliers: suppliers.filter((s) => s.creditScore < 60).length,
    averageRating:
      suppliers.length > 0
        ? (
            suppliers.reduce((sum, s) => sum + s.starRating, 0) /
            suppliers.length
          ).toFixed(1)
        : 0,
    averageDeliveryRate:
      suppliers.length > 0
        ? Math.round(
            suppliers.reduce((sum, s) => sum + s.onTimeDeliveryRate, 0) /
              suppliers.length,
          )
        : 0,
  };

  return (
    <PageContainer
      header={{
        title: '供应商信用体系',
        subTitle: '管理供应商信息和信用评价',
      }}
    >
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic title="供应商总数" value={statistics.totalSuppliers} />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="黑名单供应商"
              value={statistics.blacklistedSuppliers}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="高信用供应商"
              value={statistics.highCreditSuppliers}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="低信用供应商"
              value={statistics.lowCreditSuppliers}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="平均评分"
              value={statistics.averageRating}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="平均准时率"
              value={statistics.averageDeliveryRate}
              suffix="%"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="全部供应商" key="all" />
          <TabPane tab="正常供应商" key="normal" />
          <TabPane tab="黑名单供应商" key="blacklisted" />
          <TabPane tab="高信用供应商" key="highCredit" />
          <TabPane tab="低信用供应商" key="lowCredit" />
        </Tabs>

        <div
          style={{
            marginBottom: 16,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Input
            placeholder="搜索供应商名称、联系人、电话等"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
            allowClear
          />

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddSupplier}
          >
            添加供应商
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredSuppliers}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          rowClassName={(record) =>
            record.blacklisted ? 'table-row-blacklisted' : ''
          }
        />
      </Card>

      {/* 供应商表单 */}
      <SupplierForm
        visible={formVisible}
        initialValues={selectedSupplier}
        onCancel={() => setFormVisible(false)}
        onSuccess={handleFormSuccess}
      />

      {/* 供应商评价 */}
      <SupplierRatings
        visible={ratingsVisible}
        supplier={selectedSupplier}
        onClose={() => setRatingsVisible(false)}
      />

      {/* 黑名单操作 */}
      <BlacklistModal
        visible={blacklistVisible}
        supplier={selectedSupplier}
        onCancel={() => setBlacklistVisible(false)}
        onSuccess={handleBlacklistSuccess}
      />

      {/* 自定义样式 */}
      <style>
        {`
          .table-row-blacklisted {
            background-color: #fff1f0;
          }
        `}
      </style>
    </PageContainer>
  );
};

export default SupplierManagement;
