import { factoryService, supplierService } from '@/services/iceOrder/enhanced';
import { Factory, Supplier } from '@/services/iceOrder/types/enhanced';
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
  Spin,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;
const { TextArea } = Input;

interface SupplierFormProps {
  visible: boolean;
  initialValues: Supplier | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const SupplierForm: React.FC<SupplierFormProps> = ({
  visible,
  initialValues,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [factories, setFactories] = useState<Factory[]>([]);
  const [loadingFactories, setLoadingFactories] = useState<boolean>(false);
  const isEdit = !!initialValues;

  // 加载工厂列表
  const loadFactories = async () => {
    setLoadingFactories(true);
    try {
      const response = await factoryService.getFactoryList();
      if (response.status === 200) {
        setFactories(response.data);
      }
    } catch (error) {
      console.error('加载工厂列表失败:', error);

      // 使用模拟数据
      setFactories([
        {
          id: 1,
          name: '北京冰工厂',
          address: {
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '建国路88号',
          },
          contactPerson: '张经理',
          contactPhone: '13800138001',
          serviceArea: {
            center: { longitude: 116.4, latitude: 39.9 },
            radius: 50,
          },
          capacity: {
            dailyProduction: 500,
            currentInventory: 300,
            maxInventory: 1000,
          },
          productionLines: [],
          certifications: [],
          rating: 4.5,
          createdAt: '2023-01-15T08:30:00',
        },
        {
          id: 2,
          name: '上海冷冻厂',
          address: {
            province: '上海市',
            city: '上海市',
            district: '浦东新区',
            detail: '陆家嘴1号',
          },
          contactPerson: '李经理',
          contactPhone: '13900139002',
          serviceArea: {
            center: { longitude: 121.5, latitude: 31.2 },
            radius: 30,
          },
          capacity: {
            dailyProduction: 800,
            currentInventory: 600,
            maxInventory: 1500,
          },
          productionLines: [],
          certifications: [],
          rating: 4.8,
          createdAt: '2023-02-20T10:15:00',
        },
      ]);
    } finally {
      setLoadingFactories(false);
    }
  };

  // 当表单显示时，加载工厂列表
  useEffect(() => {
    if (visible) {
      loadFactories();
    }
  }, [visible]);

  // 当表单显示或初始值变化时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      setLoading(true);

      if (isEdit) {
        // 更新供应商
        await supplierService.updateSupplier(initialValues.id, values);
        message.success('供应商信息更新成功');
      } else {
        // 创建供应商
        await supplierService.createSupplier(values);
        message.success('供应商创建成功');
      }

      onSuccess();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交失败，请检查表单');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟提交成功');
        onSuccess();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={isEdit ? '编辑供应商信息' : '添加供应商'}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={loading}
        >
          保存
        </Button>,
      ]}
    >
      <Spin spinning={loadingFactories}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            creditScore: 80,
            transactionVolume: 0,
            starRating: 5,
            onTimeDeliveryRate: 100,
            blacklisted: false,
          }}
        >
          {/* 基本信息 */}
          <Form.Item
            name="factoryId"
            label="关联工厂"
            rules={[{ required: true, message: '请选择关联工厂' }]}
          >
            <Select placeholder="请选择关联工厂">
              {factories.map((factory) => (
                <Option key={factory.id} value={factory.id}>
                  {factory.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="name"
            label="供应商名称"
            rules={[{ required: true, message: '请输入供应商名称' }]}
          >
            <Input placeholder="请输入供应商名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="供应商描述"
            rules={[{ required: true, message: '请输入供应商描述' }]}
          >
            <TextArea rows={4} placeholder="请输入供应商描述" />
          </Form.Item>

          {/* 联系信息 */}
          <Form.Item
            name="contactPerson"
            label="联系人"
            rules={[{ required: true, message: '请输入联系人' }]}
          >
            <Input placeholder="请输入联系人" />
          </Form.Item>

          <Form.Item
            name="contactPhone"
            label="联系电话"
            rules={[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
            ]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="email"
            label="电子邮箱"
            rules={[
              { required: true, message: '请输入电子邮箱' },
              { type: 'email', message: '请输入有效的电子邮箱' },
            ]}
          >
            <Input placeholder="请输入电子邮箱" />
          </Form.Item>

          {/* 地址信息 */}
          <Form.Item label="地址信息">
            <Input.Group compact>
              <Form.Item
                name={['address', 'province']}
                noStyle
                rules={[{ required: true, message: '请选择省份' }]}
              >
                <Select placeholder="省份" style={{ width: '25%' }}>
                  <Option value="北京市">北京市</Option>
                  <Option value="上海市">上海市</Option>
                  <Option value="广东省">广东省</Option>
                  <Option value="江苏省">江苏省</Option>
                  <Option value="浙江省">浙江省</Option>
                  {/* 更多省份... */}
                </Select>
              </Form.Item>
              <Form.Item
                name={['address', 'city']}
                noStyle
                rules={[{ required: true, message: '请选择城市' }]}
              >
                <Select placeholder="城市" style={{ width: '25%' }}>
                  <Option value="北京市">北京市</Option>
                  <Option value="上海市">上海市</Option>
                  <Option value="广州市">广州市</Option>
                  <Option value="深圳市">深圳市</Option>
                  <Option value="杭州市">杭州市</Option>
                  {/* 更多城市... */}
                </Select>
              </Form.Item>
              <Form.Item
                name={['address', 'district']}
                noStyle
                rules={[{ required: true, message: '请选择区/县' }]}
              >
                <Select placeholder="区/县" style={{ width: '25%' }}>
                  <Option value="朝阳区">朝阳区</Option>
                  <Option value="海淀区">海淀区</Option>
                  <Option value="浦东新区">浦东新区</Option>
                  <Option value="天河区">天河区</Option>
                  {/* 更多区县... */}
                </Select>
              </Form.Item>
              <Form.Item
                name={['address', 'detail']}
                noStyle
                rules={[{ required: true, message: '请输入详细地址' }]}
              >
                <Input placeholder="详细地址" style={{ width: '25%' }} />
              </Form.Item>
            </Input.Group>
          </Form.Item>

          {/* 营业执照 */}
          <Form.Item
            name="businessLicense"
            label="营业执照号"
            rules={[{ required: true, message: '请输入营业执照号' }]}
          >
            <Input placeholder="请输入营业执照号" />
          </Form.Item>

          {/* 信用信息 */}
          <Form.Item
            name="creditScore"
            label="信用评分"
            rules={[{ required: true, message: '请输入信用评分' }]}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="transactionVolume"
            label="交易量(吨)"
            rules={[{ required: true, message: '请输入交易量' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="starRating"
            label="星级评分"
            rules={[{ required: true, message: '请输入星级评分' }]}
          >
            <InputNumber min={1} max={5} step={0.1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="onTimeDeliveryRate"
            label="准时交付率(%)"
            rules={[{ required: true, message: '请输入准时交付率' }]}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>

          {/* 黑名单状态 */}
          {isEdit && (
            <Form.Item
              name="blacklisted"
              label="黑名单状态"
              valuePropName="checked"
            >
              <Select>
                <Option value={false}>正常</Option>
                <Option value={true}>黑名单</Option>
              </Select>
            </Form.Item>
          )}

          {/* 黑名单原因 */}
          {isEdit && form.getFieldValue('blacklisted') && (
            <Form.Item
              name="blacklistReason"
              label="黑名单原因"
              rules={[{ required: true, message: '请输入黑名单原因' }]}
            >
              <TextArea rows={4} placeholder="请输入黑名单原因" />
            </Form.Item>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default SupplierForm;
