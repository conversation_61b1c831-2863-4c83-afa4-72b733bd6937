import { supplierService } from '@/services/iceOrder/enhanced';
import { Supplier } from '@/services/iceOrder/types/enhanced';
import {
  DislikeOutlined,
  LikeOutlined,
  StarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  Divider,
  Empty,
  List,
  Modal,
  Rate,
  Row,
  Spin,
  Statistic,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import React, { useEffect, useState } from 'react';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

interface SupplierRatingsProps {
  visible: boolean;
  supplier: Supplier | null;
  onClose: () => void;
}

interface Rating {
  id: number;
  userId: number;
  username: string;
  avatar?: string;
  rating: number;
  comment: string;
  createdAt: string;
  likes: number;
  dislikes: number;
}

const SupplierRatings: React.FC<SupplierRatingsProps> = ({
  visible,
  supplier,
  onClose,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [ratings, setRatings] = useState<Rating[]>([]);

  // 加载评价列表
  const loadRatings = async () => {
    if (!supplier) return;

    setLoading(true);
    try {
      const response = await supplierService.getSupplierRatings(supplier.id);
      if (response.status === 200) {
        setRatings(response.data);
      }
    } catch (error) {
      console.error('加载评价列表失败:', error);

      // 使用模拟数据
      setRatings([
        {
          id: 1,
          userId: 101,
          username: '张三',
          avatar:
            'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
          rating: 5,
          comment: '冰块质量非常好，送货速度快，服务态度好。',
          createdAt: '2023-05-15T10:30:00',
          likes: 12,
          dislikes: 0,
        },
        {
          id: 2,
          userId: 102,
          username: '李四',
          rating: 4,
          comment: '冰块质量不错，但是送货时间有点延迟。',
          createdAt: '2023-05-10T14:20:00',
          likes: 5,
          dislikes: 1,
        },
        {
          id: 3,
          userId: 103,
          username: '王五',
          avatar:
            'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
          rating: 5,
          comment: '服务很好，冰块质量也很好，会继续合作。',
          createdAt: '2023-05-05T09:15:00',
          likes: 8,
          dislikes: 0,
        },
        {
          id: 4,
          userId: 104,
          username: '赵六',
          rating: 3,
          comment: '冰块质量一般，有些已经融化了。',
          createdAt: '2023-04-28T16:40:00',
          likes: 2,
          dislikes: 3,
        },
        {
          id: 5,
          userId: 105,
          username: '钱七',
          avatar:
            'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
          rating: 5,
          comment: '非常满意，冰块质量好，送货及时。',
          createdAt: '2023-04-20T11:10:00',
          likes: 15,
          dislikes: 0,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 当供应商变化时，加载评价列表
  useEffect(() => {
    if (visible && supplier) {
      loadRatings();
    }
  }, [visible, supplier]);

  // 计算评分统计
  const calculateRatingStats = () => {
    if (ratings.length === 0) return { avg: 0, counts: [0, 0, 0, 0, 0] };

    const counts = [0, 0, 0, 0, 0]; // 1星到5星的数量
    let sum = 0;

    ratings.forEach((rating) => {
      const index = Math.floor(rating.rating) - 1;
      if (index >= 0 && index < 5) {
        counts[index]++;
      }
      sum += rating.rating;
    });

    return {
      avg: parseFloat((sum / ratings.length).toFixed(1)),
      counts,
    };
  };

  const stats = calculateRatingStats();

  return (
    <Modal
      title={supplier ? `${supplier.name} - 客户评价` : '客户评价'}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      <Spin spinning={loading}>
        {supplier && (
          <>
            <Card style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="平均评分"
                    value={stats.avg}
                    precision={1}
                    prefix={<StarOutlined />}
                    suffix="/ 5"
                  />
                  <Rate
                    disabled
                    allowHalf
                    defaultValue={stats.avg}
                    style={{ marginTop: 8 }}
                  />
                </Col>
                <Col span={16}>
                  <div>
                    {[5, 4, 3, 2, 1].map((star) => (
                      <div
                        key={star}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: 8,
                        }}
                      >
                        <div style={{ width: 60 }}>
                          {star} 星
                          <StarOutlined
                            style={{ marginLeft: 4, color: '#fadb14' }}
                          />
                        </div>
                        <div style={{ flex: 1, marginLeft: 8, marginRight: 8 }}>
                          <div
                            style={{
                              backgroundColor: '#f0f0f0',
                              height: 16,
                              borderRadius: 8,
                              overflow: 'hidden',
                            }}
                          >
                            <div
                              style={{
                                backgroundColor: '#fadb14',
                                height: '100%',
                                width: `${
                                  ratings.length > 0
                                    ? (stats.counts[star - 1] /
                                        ratings.length) *
                                      100
                                    : 0
                                }%`,
                              }}
                            />
                          </div>
                        </div>
                        <div style={{ width: 40, textAlign: 'right' }}>
                          {stats.counts[star - 1]}
                        </div>
                      </div>
                    ))}
                  </div>
                </Col>
              </Row>
            </Card>

            <Divider orientation="left">评价列表</Divider>

            {ratings.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={ratings}
                renderItem={(item) => (
                  <List.Item
                    key={item.id}
                    actions={[
                      <Tooltip key="like" title="点赞">
                        <span>
                          <LikeOutlined />
                          <span style={{ paddingLeft: 8, cursor: 'auto' }}>
                            {item.likes}
                          </span>
                        </span>
                      </Tooltip>,
                      <Tooltip key="dislike" title="不喜欢">
                        <span>
                          <DislikeOutlined />
                          <span style={{ paddingLeft: 8, cursor: 'auto' }}>
                            {item.dislikes}
                          </span>
                        </span>
                      </Tooltip>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={item.avatar}
                          icon={!item.avatar && <UserOutlined />}
                        >
                          {item.username[0]}
                        </Avatar>
                      }
                      title={<a>{item.username}</a>}
                      description={
                        <Tooltip
                          title={dayjs(item.createdAt).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )}
                        >
                          <span>{dayjs(item.createdAt).fromNow()}</span>
                        </Tooltip>
                      }
                    />
                    <div style={{ marginTop: 8 }}>
                      <Rate disabled defaultValue={item.rating} />
                      <p style={{ marginTop: 8 }}>{item.comment}</p>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无评价" />
            )}
          </>
        )}
      </Spin>
    </Modal>
  );
};

export default SupplierRatings;
