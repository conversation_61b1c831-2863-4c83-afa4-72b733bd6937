import { supplierService } from '@/services/iceOrder/enhanced';
import { Supplier } from '@/services/iceOrder/types/enhanced';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Alert, Form, Input, message, Modal } from 'antd';
import React, { useState } from 'react';

const { TextArea } = Input;

interface BlacklistModalProps {
  visible: boolean;
  supplier: Supplier | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const BlacklistModal: React.FC<BlacklistModalProps> = ({
  visible,
  supplier,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);

  // 提交表单
  const handleSubmit = async () => {
    if (!supplier) return;

    try {
      const values = await form.validateFields();

      setLoading(true);

      await supplierService.blacklistSupplier(supplier.id, values.reason);
      message.success('供应商已加入黑名单');
      onSuccess();
    } catch (error) {
      console.error('加入黑名单失败:', error);
      message.error('加入黑名单失败');

      // 模拟成功
      if (process.env.NODE_ENV === 'development') {
        message.success('开发环境：模拟加入黑名单成功');
        onSuccess();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <div>
          <ExclamationCircleOutlined
            style={{ color: '#ff4d4f', marginRight: 8 }}
          />
          加入黑名单
        </div>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      okText="确认加入黑名单"
      okButtonProps={{ danger: true, loading }}
      cancelText="取消"
    >
      {supplier && (
        <>
          <Alert
            message="警告"
            description={`您正在将供应商 "${supplier.name}" 加入黑名单。加入黑名单后，该供应商将无法参与新的交易。`}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form form={form} layout="vertical">
            <Form.Item
              name="reason"
              label="黑名单原因"
              rules={[
                { required: true, message: '请输入将该供应商加入黑名单的原因' },
              ]}
            >
              <TextArea
                rows={4}
                placeholder="请详细说明将该供应商加入黑名单的原因，例如：产品质量问题、违反合同、欺诈行为等"
              />
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  );
};

export default BlacklistModal;
