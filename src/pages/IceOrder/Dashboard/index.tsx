import Header from '@/components/Header';
import { getOrderList } from '@/services/iceOrder/iceOrderService';
import { getIceTypeList } from '@/services/iceOrder/iceTypeService';
import {
  IceOrder,
  OrderStatus,
  OrderStatusColorMap,
  OrderStatusMap,
} from '@/services/iceOrder/types';
import { getUserList } from '@/services/iceOrder/userService';
import {
  AppstoreOutlined,
  CheckCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Col, Row, Spin, Statistic, Table, Tag, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [orders, setOrders] = useState<IceOrder[]>([]);
  const [userCount, setUserCount] = useState<number>(0);
  const [iceTypeCount, setIceTypeCount] = useState<number>(0);
  const [recentOrders, setRecentOrders] = useState<IceOrder[]>([]);

  // 加载数据
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // 获取订单数据
        const orderResponse = await getOrderList();
        if (orderResponse.status === 200 && orderResponse.data) {
          setOrders(orderResponse.data || []);
          // 获取最近5个订单
          setRecentOrders(orderResponse.data.slice(0, 5));
        } else {
          setOrders([]);
          setRecentOrders([]);
        }

        // 获取用户数量
        const userResponse = await getUserList();
        if (userResponse.status === 200 && userResponse.data) {
          setUserCount(userResponse.data.length);
        } else {
          setUserCount(0);
        }

        // 获取冰块类型数量
        const iceTypeResponse = await getIceTypeList();
        if (iceTypeResponse.status === 200 && iceTypeResponse.data) {
          setIceTypeCount(iceTypeResponse.data.length);
        } else {
          setIceTypeCount(0);
        }
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // 计算各状态订单数量
  const getOrderCountByStatus = (status: OrderStatus) => {
    return orders.filter((order) => order.status === status).length;
  };

  // 最近订单表格列
  const columns = [
    {
      title: '订单ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '送达日期',
      dataIndex: 'deliveryDateTime',
      key: 'deliveryDateTime',
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: OrderStatus) => (
        <Tag color={OrderStatusColorMap[status]}>{OrderStatusMap[status]}</Tag>
      ),
    },
  ];

  return (
    <>
      <Header />
      <PageContainer
        header={{
          title: '冰块订单管理系统',
          subTitle: '系统概览',
        }}
      >
        <Spin spinning={loading}>
          {/* 统计卡片 */}
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总订单数"
                  value={orders.length}
                  prefix={<ShoppingCartOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="用户数"
                  value={userCount}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="冰块类型数"
                  value={iceTypeCount}
                  prefix={<AppstoreOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="已完成订单"
                  value={getOrderCountByStatus(OrderStatus.DELIVERED)}
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
          </Row>

          {/* 订单状态统计 */}
          <Card style={{ marginTop: 16 }}>
            <Title level={4}>订单状态统计</Title>
            <Row gutter={16}>
              {Object.values(OrderStatus).map((status) => (
                <Col span={4} key={status}>
                  <Card>
                    <Statistic
                      title={OrderStatusMap[status]}
                      value={getOrderCountByStatus(status)}
                      valueStyle={{ color: OrderStatusColorMap[status] }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>

          {/* 最近订单 */}
          <Card style={{ marginTop: 16 }}>
            <Title level={4}>最近订单</Title>
            <Table
              columns={columns}
              dataSource={recentOrders}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </Spin>
      </PageContainer>
    </>
  );
};

export default Dashboard;
