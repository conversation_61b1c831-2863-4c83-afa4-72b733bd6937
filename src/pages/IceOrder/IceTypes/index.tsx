import {
  createIceType,
  deleteIceType,
  getIceTypeList,
  updateIceType,
} from '@/services/iceOrder/iceTypeService';
import { IceType } from '@/services/iceOrder/types';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Space,
  Spin,
  Table,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title } = Typography;
const { TextArea } = Input;

const IceTypeManagement: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [iceTypes, setIceTypes] = useState<IceType[]>([]);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>('新增冰块类型');
  const [form] = Form.useForm();
  const [currentIceType, setCurrentIceType] = useState<IceType | null>(null);

  // 加载冰块类型列表
  const loadIceTypes = async () => {
    setLoading(true);
    try {
      const response = await getIceTypeList();
      if (response.status === 200) {
        // 确保数据是数组类型
        const data = Array.isArray(response.data) ? response.data : [];
        setIceTypes(data);
      } else {
        setIceTypes([]);
      }
    } catch (error) {
      console.error('加载冰块类型列表失败:', error);
      message.error('加载冰块类型列表失败');
      setIceTypes([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadIceTypes();
  }, []);

  // 打开新增冰块类型模态框
  const handleAdd = () => {
    setModalTitle('新增冰块类型');
    setCurrentIceType(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑冰块类型模态框
  const handleEdit = (iceType: IceType) => {
    setModalTitle('编辑冰块类型');
    setCurrentIceType(iceType);
    form.setFieldsValue(iceType);
    setModalVisible(true);
  };

  // 删除冰块类型
  const handleDelete = async (id: number) => {
    try {
      const response = await deleteIceType(id);
      if (response.status === 200) {
        message.success('删除成功');
        loadIceTypes();
      }
    } catch (error) {
      console.error('删除冰块类型失败:', error);
      message.error('删除冰块类型失败');
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (currentIceType) {
        // 更新冰块类型
        const response = await updateIceType(currentIceType.id, values);
        if (response.status === 200) {
          message.success('更新成功');
          setModalVisible(false);
          loadIceTypes();
        }
      } else {
        // 创建冰块类型
        const response = await createIceType(values);
        if (response.status === 201) {
          message.success('创建成功');
          setModalVisible(false);
          loadIceTypes();
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: IceType) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个冰块类型吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '冰块类型管理',
        subTitle: '管理系统冰块类型信息',
      }}
    >
      <Card>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: 16,
          }}
        >
          <Title level={4}>冰块类型列表</Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增冰块类型
          </Button>
        </div>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={iceTypes}
            rowKey="id"
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        </Spin>
      </Card>

      {/* 冰块类型表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入冰块类型名称' }]}
          >
            <Input placeholder="请输入冰块类型名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入冰块类型描述' }]}
          >
            <TextArea rows={4} placeholder="请输入冰块类型描述" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default IceTypeManagement;
