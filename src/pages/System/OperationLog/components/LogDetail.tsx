import { OPERATION_ACTION_COLOR, OPERATION_ACTION_TEXT } from '@/constants';
import type { OperationLog } from '@/services/operationLog';
import { Descriptions, Divider, Drawer, Tag, Typography } from 'antd';
import React from 'react';
import ReactJson from 'react-json-view';

const { Text } = Typography;

interface LogDetailProps {
  visible: boolean;
  log?: OperationLog;
  onClose: () => void;
}

const LogDetail: React.FC<LogDetailProps> = ({ visible, log, onClose }) => {
  if (!log) return null;

  return (
    <Drawer
      title="操作日志详情"
      placement="right"
      width={600}
      onClose={onClose}
      open={visible}
    >
      <Descriptions title="基本信息" column={2}>
        <Descriptions.Item label="操作时间">{log.createdAt}</Descriptions.Item>
        <Descriptions.Item label="操作人">{log.username}</Descriptions.Item>
        <Descriptions.Item label="操作类型">
          <Tag color={OPERATION_ACTION_COLOR[log.action] || 'default'}>
            {OPERATION_ACTION_TEXT[log.action] || log.action}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="操作模块">{log.module}</Descriptions.Item>
        <Descriptions.Item label="资源ID">
          <Text copyable>{log.resourceId}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="资源类型">
          {log.resourceType}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <Descriptions title="操作详情" column={1}>
        <Descriptions.Item label="操作描述">
          {log.description}
        </Descriptions.Item>
        {log.details && (
          <Descriptions.Item label="变更详情">
            <ReactJson
              src={log.details}
              name={false}
              theme="rjv-default"
              displayDataTypes={false}
              displayObjectSize={false}
              enableClipboard={false}
              style={{
                backgroundColor: 'transparent',
                marginTop: '8px',
              }}
            />
          </Descriptions.Item>
        )}
      </Descriptions>

      <Divider />

      <Descriptions title="环境信息" column={2}>
        <Descriptions.Item label="IP地址">
          <Text copyable>{log.ipAddress}</Text>
        </Descriptions.Item>
        <Descriptions.Item label="User Agent">
          <Text ellipsis={{ tooltip: log.userAgent }}>{log.userAgent}</Text>
        </Descriptions.Item>
      </Descriptions>
    </Drawer>
  );
};

export default LogDetail;
