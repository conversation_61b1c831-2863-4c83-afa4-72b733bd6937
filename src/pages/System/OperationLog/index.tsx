import {
  OPERATION_ACTION_COLOR,
  OPERATION_ACTION_TEXT,
  OPERATION_ACTION_TYPES,
} from '@/constants';

import { getOperationLogs } from '@/services/operationLog';
import {
  OperationLog,
  OperationLogQueryParams,
} from '@/services/operationLog/type';
import { EyeOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Tag, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import LogDetail from './components/LogDetail';

const OperationLogPage: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [currentLog, setCurrentLog] = useState<OperationLog | undefined>(
    undefined,
  );

  // 查看日志详情
  const handleViewDetail = (record: OperationLog) => {
    setCurrentLog(record);
    setDetailVisible(true);
  };

  const columns: ProColumns<OperationLog>[] = [
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      sorter: true,
      defaultSortOrder: 'descend',
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
      renderFormItem: (_, { type }) => {
        if (type === 'form') {
          return null;
        }
        return undefined;
      },
    },
    {
      title: '操作人',
      dataIndex: 'username',
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'action',
      valueEnum: {
        [OPERATION_ACTION_TYPES.CREATE]: {
          text: OPERATION_ACTION_TEXT[OPERATION_ACTION_TYPES.CREATE],
          status: 'Success',
        },
        [OPERATION_ACTION_TYPES.UPDATE]: {
          text: OPERATION_ACTION_TEXT[OPERATION_ACTION_TYPES.UPDATE],
          status: 'Processing',
        },
        [OPERATION_ACTION_TYPES.DELETE]: {
          text: OPERATION_ACTION_TEXT[OPERATION_ACTION_TYPES.DELETE],
          status: 'Error',
        },
        [OPERATION_ACTION_TYPES.READ]: {
          text: OPERATION_ACTION_TEXT[OPERATION_ACTION_TYPES.READ],
          status: 'Default',
        },
      },
      render: (_, record) => (
        <Tag color={OPERATION_ACTION_COLOR[record.action] || 'default'}>
          {OPERATION_ACTION_TEXT[record.action] || record.action}
        </Tag>
      ),
    },
    {
      title: '操作模块',
      dataIndex: 'module',
      ellipsis: true,
    },
    {
      title: '资源ID',
      dataIndex: 'resourceId',
      ellipsis: true,
      search: false,
    },
    {
      title: '资源类型',
      dataIndex: 'resourceType',
      ellipsis: true,
    },
    {
      title: '操作描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      ellipsis: true,
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <Tooltip title="查看详情" key="detail">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </Tooltip>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<OperationLog, OperationLogQueryParams>
        headerTitle="操作日志"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => []}
        request={async (params) => {
          // 处理分页参数
          const { current, pageSize, ...rest } = params;

          // 调用API获取数据
          const result = await getOperationLogs({
            current,
            pageSize,
            ...rest,
          });

          return {
            data: result.data?.list || [],
            success: result.status === 200,
            total: result.data?.total || 0,
          };
        }}
        columns={columns}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
        }}
      />

      {/* 日志详情抽屉 */}
      <LogDetail
        visible={detailVisible}
        log={currentLog}
        onClose={() => setDetailVisible(false)}
      />
    </PageContainer>
  );
};

export default OperationLogPage;
