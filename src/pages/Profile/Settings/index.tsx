import React, { useState } from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Button,
  message,
  Divider,
  Typography,
  Space,
  TimePicker,
  Row,
  Col,
  Alert,
  Modal,
  List,
  Avatar,
} from 'antd';
import {
  SettingOutlined,
  BellOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  MoonOutlined,
  SunOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { PersonalSettings } from '@/types/profile';
import ProfileLayout from '../components/ProfileLayout';
import styles from './index.less';

const { Title, Text } = Typography;
const { Option } = Select;

// 模拟个人设置数据
const mockSettings: PersonalSettings = {
  theme: 'light',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24h',
  notifications: {
    email: true,
    sms: false,
    push: true,
    desktop: true,
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '08:00',
    },
    types: {
      system: true,
      content: true,
      customer: true,
      team: false,
      approval: true,
    },
  },
  privacy: {
    profileVisible: true,
    contactVisible: false,
    activityVisible: true,
    allowSearch: true,
  },
  workspace: {
    layout: 'grid',
    density: 'normal',
    sidebarCollapsed: false,
    showQuickActions: true,
    customWidgets: ['todo', 'stats', 'calendar'],
  },
};

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [settings, setSettings] = useState<PersonalSettings>(mockSettings);
  const [loading, setLoading] = useState<boolean>(false);
  const [exportModalVisible, setExportModalVisible] = useState<boolean>(false);

  // 主题选项
  const themeOptions = [
    { value: 'light', label: '浅色主题', icon: <SunOutlined /> },
    { value: 'dark', label: '深色主题', icon: <MoonOutlined /> },
    { value: 'auto', label: '跟随系统', icon: <SettingOutlined /> },
  ];

  // 语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '简体中文' },
    { value: 'en-US', label: 'English' },
  ];

  // 时区选项
  const timezoneOptions = [
    { value: 'Asia/Shanghai', label: '中国标准时间 (UTC+8)' },
    { value: 'America/New_York', label: '美国东部时间 (UTC-5)' },
    { value: 'Europe/London', label: '英国时间 (UTC+0)' },
    { value: 'Asia/Tokyo', label: '日本时间 (UTC+9)' },
  ];

  // 保存设置
  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      console.log('保存设置:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSettings(prev => ({ ...prev, ...values }));
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 重置设置
  const handleResetSettings = () => {
    Modal.confirm({
      title: '确认重置设置',
      content: '重置后将恢复到默认设置，确定要继续吗？',
      icon: <ExclamationCircleOutlined />,
      onOk: () => {
        form.resetFields();
        setSettings(mockSettings);
        message.success('设置已重置');
      },
    });
  };

  // 导出个人数据
  const handleExportData = (format: 'json' | 'excel') => {
    console.log('导出个人数据:', format);
    message.success(`正在导出${format.toUpperCase()}格式的数据...`);
    setExportModalVisible(false);
  };

  // 删除账号
  const handleDeleteAccount = () => {
    Modal.confirm({
      title: '确认删除账号',
      content: '删除账号后，您的所有数据将被永久删除且无法恢复。请确认您已备份重要数据。',
      icon: <ExclamationCircleOutlined />,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        message.error('账号删除功能暂未开放，请联系管理员');
      },
    });
  };

  // 初始化表单数据
  React.useEffect(() => {
    form.setFieldsValue({
      ...settings,
      'notifications.quietHours.start': settings.notifications.quietHours.start ? dayjs(settings.notifications.quietHours.start, 'HH:mm') : undefined,
      'notifications.quietHours.end': settings.notifications.quietHours.end ? dayjs(settings.notifications.quietHours.end, 'HH:mm') : undefined,
    });
  }, [settings, form]);

  return (
    <ProfileLayout>
      <div className={styles.settings}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveSettings}
        >
          {/* 外观设置 */}
          <Card title="外观设置" className={styles.settingCard}>
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item name="theme" label="主题模式">
                  <Select>
                    {themeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <Space>
                          {option.icon}
                          {option.label}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item name="language" label="语言">
                  <Select>
                    {languageOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item name="timezone" label="时区">
                  <Select>
                    {timezoneOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item name="timeFormat" label="时间格式">
                  <Select>
                    <Option value="12h">12小时制</Option>
                    <Option value="24h">24小时制</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 通知设置 */}
          <Card title="通知设置" className={styles.settingCard}>
            <div className={styles.notificationSettings}>
              <div className={styles.settingGroup}>
                <Title level={5}>通知渠道</Title>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <BellOutlined />
                      <span>邮件通知</span>
                    </div>
                    <Form.Item name={['notifications', 'email']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <BellOutlined />
                      <span>短信通知</span>
                    </div>
                    <Form.Item name={['notifications', 'sms']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <BellOutlined />
                      <span>推送通知</span>
                    </div>
                    <Form.Item name={['notifications', 'push']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <BellOutlined />
                      <span>桌面通知</span>
                    </div>
                    <Form.Item name={['notifications', 'desktop']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                </Space>
              </div>

              <Divider />

              <div className={styles.settingGroup}>
                <Title level={5}>免打扰时间</Title>
                <div className={styles.settingItem}>
                  <div className={styles.settingLabel}>
                    <span>启用免打扰</span>
                  </div>
                  <Form.Item name={['notifications', 'quietHours', 'enabled']} valuePropName="checked" style={{ margin: 0 }}>
                    <Switch />
                  </Form.Item>
                </div>
                <Row gutter={16} style={{ marginTop: 16 }}>
                  <Col span={12}>
                    <Form.Item name={['notifications', 'quietHours', 'start']} label="开始时间">
                      <TimePicker format="HH:mm" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['notifications', 'quietHours', 'end']} label="结束时间">
                      <TimePicker format="HH:mm" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <Divider />

              <div className={styles.settingGroup}>
                <Title level={5}>通知类型</Title>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <span>系统通知</span>
                    </div>
                    <Form.Item name={['notifications', 'types', 'system']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <span>内容通知</span>
                    </div>
                    <Form.Item name={['notifications', 'types', 'content']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <span>客户通知</span>
                    </div>
                    <Form.Item name={['notifications', 'types', 'customer']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <span>团队通知</span>
                    </div>
                    <Form.Item name={['notifications', 'types', 'team']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                  <div className={styles.settingItem}>
                    <div className={styles.settingLabel}>
                      <span>审批通知</span>
                    </div>
                    <Form.Item name={['notifications', 'types', 'approval']} valuePropName="checked" style={{ margin: 0 }}>
                      <Switch />
                    </Form.Item>
                  </div>
                </Space>
              </div>
            </div>
          </Card>

          {/* 隐私设置 */}
          <Card title="隐私设置" className={styles.settingCard}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className={styles.settingItem}>
                <div className={styles.settingLabel}>
                  <EyeOutlined />
                  <span>个人资料可见</span>
                  <Text type="secondary">允许其他用户查看您的个人资料</Text>
                </div>
                <Form.Item name={['privacy', 'profileVisible']} valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>
              <div className={styles.settingItem}>
                <div className={styles.settingLabel}>
                  <EyeOutlined />
                  <span>联系方式可见</span>
                  <Text type="secondary">允许其他用户查看您的联系方式</Text>
                </div>
                <Form.Item name={['privacy', 'contactVisible']} valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>
              <div className={styles.settingItem}>
                <div className={styles.settingLabel}>
                  <EyeOutlined />
                  <span>活动记录可见</span>
                  <Text type="secondary">允许其他用户查看您的活动记录</Text>
                </div>
                <Form.Item name={['privacy', 'activityVisible']} valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>
              <div className={styles.settingItem}>
                <div className={styles.settingLabel}>
                  <GlobalOutlined />
                  <span>允许搜索</span>
                  <Text type="secondary">允许其他用户通过搜索找到您</Text>
                </div>
                <Form.Item name={['privacy', 'allowSearch']} valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>
            </Space>
          </Card>

          {/* 数据管理 */}
          <Card title="数据管理" className={styles.settingCard}>
            <Alert
              message="数据安全提示"
              description="您可以导出个人数据或删除账号。删除账号后，所有数据将被永久删除且无法恢复。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <List
              itemLayout="horizontal"
              dataSource={[
                {
                  key: 'export',
                  title: '导出个人数据',
                  description: '下载您在平台上的所有个人数据',
                  icon: <DownloadOutlined />,
                  action: () => setExportModalVisible(true),
                  actionText: '导出数据',
                },
                {
                  key: 'delete',
                  title: '删除账号',
                  description: '永久删除您的账号和所有相关数据',
                  icon: <DeleteOutlined />,
                  action: handleDeleteAccount,
                  actionText: '删除账号',
                  danger: true,
                },
              ]}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button
                      key="action"
                      type={item.danger ? 'default' : 'primary'}
                      danger={item.danger}
                      onClick={item.action}
                    >
                      {item.actionText}
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={item.icon} />}
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </Card>

          {/* 操作按钮 */}
          <Card className={styles.actionCard}>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存设置
              </Button>
              <Button onClick={handleResetSettings}>
                重置设置
              </Button>
            </Space>
          </Card>
        </Form>

        {/* 导出数据模态框 */}
        <Modal
          title="导出个人数据"
          open={exportModalVisible}
          onCancel={() => setExportModalVisible(false)}
          footer={null}
        >
          <div className={styles.exportModal}>
            <Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
              选择导出格式：
            </Text>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                block
                icon={<DownloadOutlined />}
                onClick={() => handleExportData('json')}
              >
                导出为 JSON 格式
              </Button>
              <Button
                block
                icon={<DownloadOutlined />}
                onClick={() => handleExportData('excel')}
              >
                导出为 Excel 格式
              </Button>
            </Space>
          </div>
        </Modal>
      </div>
    </ProfileLayout>
  );
};

export default Settings;
