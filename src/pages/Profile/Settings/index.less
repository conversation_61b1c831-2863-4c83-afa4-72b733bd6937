.settings {
  .settingCard {
    margin-bottom: 16px;
  }

  .notificationSettings {
    .settingGroup {
      .ant-typography-title {
        margin-bottom: 16px;
        font-size: 16px;
      }
    }

    .settingItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .settingLabel {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .anticon {
          color: #666;
        }

        span {
          font-weight: 500;
        }

        .ant-typography {
          display: block;
          margin-top: 4px;
          font-size: 12px;
        }
      }
    }
  }

  .actionCard {
    text-align: center;
    background: #fafafa;
  }

  .exportModal {
    .ant-btn {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings {
    .settingItem {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .settingLabel {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .settings {
    .settingItem {
      padding: 8px 0;

      .settingLabel {
        gap: 6px;

        .anticon {
          font-size: 14px;
        }

        span {
          font-size: 13px;
        }

        .ant-typography {
          font-size: 11px;
        }
      }
    }
  }
}
