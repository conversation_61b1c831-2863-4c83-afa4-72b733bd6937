.basicInfo {
  .personalCard,
  .companyCard {
    margin-bottom: 16px;
  }

  .avatarSection {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 24px;

    .avatarUploader {
      position: relative;

      .ant-upload {
        border: none !important;
        background: transparent !important;
      }

      .avatarOverlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
        cursor: pointer;

        .anticon {
          font-size: 20px;
          margin-bottom: 4px;
        }

        div {
          font-size: 12px;
        }
      }

      &:hover .avatarOverlay {
        opacity: 1;
      }
    }

    .avatarInfo {
      flex: 1;

      .ant-typography-title {
        margin-bottom: 4px;
      }

      .userStatus {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  .managerInfo,
  .teamInfo {
    padding: 12px 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .basicInfo {
    .avatarSection {
      flex-direction: column;
      text-align: center;
      gap: 16px;

      .avatarInfo {
        .userStatus {
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .managerInfo,
    .teamInfo {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
}

@media (max-width: 576px) {
  .basicInfo {
    .avatarSection {
      gap: 12px;

      .avatarUploader {
        .ant-avatar {
          width: 80px !important;
          height: 80px !important;
        }
      }
    }
  }
}
