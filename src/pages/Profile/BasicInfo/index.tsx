import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Select,
  DatePicker,
  Row,
  Col,
  message,
  Space,
  Divider,
  Typography,
  Tag,
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  SaveOutlined,
  EditOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  WechatOutlined,
  GithubOutlined,
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import dayjs from 'dayjs';
import type { UserProfile, CompanyInfo } from '@/types/profile';
import ProfileLayout from '../components/ProfileLayout';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

// 模拟用户数据
const mockUserProfile: UserProfile = {
  id: '1',
  username: 'zhang<PERSON>',
  email: '<EMAIL>',
  phone: '13812345678',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhangsan',
  realName: '张三',
  nickname: '产品小王子',
  gender: 'male',
  birthday: '1990-05-15',
  address: '上海市浦东新区张江高科技园区世纪大道1000号',
  bio: '专注产品设计与用户体验，热爱技术创新，致力于打造优秀的数字化产品。拥有5年产品管理经验，擅长用户研究和数据分析。',
  website: 'https://zhangsan.dev',
  socialLinks: {
    wechat: 'zhangsan_wx',
    github: 'zhangsan',
    weibo: 'zhangsan_weibo',
  },
  createTime: '2022-01-15 10:30:00',
  updateTime: '2024-01-15 14:20:00',
  lastLoginTime: '2024-01-15 09:15:00',
  status: 'active',
};

const mockCompanyInfo: CompanyInfo = {
  id: '1',
  name: '前端科技有限公司',
  department: '产品技术部',
  position: '高级产品经理',
  level: 'P7',
  employeeId: 'FE2022001',
  hireDate: '2022-01-15',
  workLocation: '上海张江办公区',
  manager: {
    id: '2',
    name: '李四',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisi',
  },
  team: {
    id: '1',
    name: '产品创新团队',
    memberCount: 12,
  },
};

const BasicInfo: React.FC = () => {
  const [form] = Form.useForm();
  const [companyForm] = Form.useForm();
  const [userProfile, setUserProfile] = useState<UserProfile>(mockUserProfile);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>(mockCompanyInfo);
  const [loading, setLoading] = useState<boolean>(false);
  const [editingPersonal, setEditingPersonal] = useState<boolean>(false);
  const [editingCompany, setEditingCompany] = useState<boolean>(false);

  // 头像上传配置
  const uploadProps: UploadProps = {
    name: 'avatar',
    listType: 'picture-card',
    className: styles.avatarUploader,
    showUploadList: false,
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!');
        return false;
      }
      return false; // 阻止自动上传
    },
    onChange: (info) => {
      if (info.file.status === 'done') {
        // 模拟上传成功
        const newAvatarUrl = URL.createObjectURL(info.file.originFileObj!);
        setUserProfile(prev => ({ ...prev, avatar: newAvatarUrl }));
        message.success('头像上传成功');
      }
    },
  };

  // 保存个人信息
  const handleSavePersonal = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 处理生日格式
      if (values.birthday) {
        values.birthday = dayjs(values.birthday).format('YYYY-MM-DD');
      }

      console.log('保存个人信息:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUserProfile(prev => ({ ...prev, ...values }));
      setEditingPersonal(false);
      message.success('个人信息保存成功');
    } catch (error) {
      message.error('请完善必填信息');
    } finally {
      setLoading(false);
    }
  };

  // 保存企业信息
  const handleSaveCompany = async () => {
    try {
      setLoading(true);
      const values = await companyForm.validateFields();
      
      console.log('保存企业信息:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setCompanyInfo(prev => ({ ...prev, ...values }));
      setEditingCompany(false);
      message.success('企业信息保存成功');
    } catch (error) {
      message.error('请完善必填信息');
    } finally {
      setLoading(false);
    }
  };

  // 初始化表单数据
  React.useEffect(() => {
    form.setFieldsValue({
      ...userProfile,
      birthday: userProfile.birthday ? dayjs(userProfile.birthday) : undefined,
    });
    companyForm.setFieldsValue(companyInfo);
  }, [userProfile, companyInfo, form, companyForm]);

  return (
    <ProfileLayout>
      <div className={styles.basicInfo}>
        {/* 个人基本信息 */}
        <Card
          title="个人基本信息"
          className={styles.personalCard}
          extra={
            <Button
              type={editingPersonal ? 'default' : 'primary'}
              icon={editingPersonal ? <SaveOutlined /> : <EditOutlined />}
              loading={loading}
              onClick={editingPersonal ? handleSavePersonal : () => setEditingPersonal(true)}
            >
              {editingPersonal ? '保存' : '编辑'}
            </Button>
          }
        >
          <Form
            form={form}
            layout="vertical"
            disabled={!editingPersonal}
          >
            <Row gutter={24}>
              <Col span={24}>
                {/* 头像上传 */}
                <div className={styles.avatarSection}>
                  <Upload {...uploadProps} disabled={!editingPersonal}>
                    <Avatar
                      size={100}
                      src={userProfile.avatar}
                      icon={<UserOutlined />}
                    />
                    {editingPersonal && (
                      <div className={styles.avatarOverlay}>
                        <CameraOutlined />
                        <div>更换头像</div>
                      </div>
                    )}
                  </Upload>
                  <div className={styles.avatarInfo}>
                    <Title level={4}>{userProfile.realName}</Title>
                    <Text type="secondary">@{userProfile.nickname}</Text>
                    <div className={styles.userStatus}>
                      <Tag color="green">活跃用户</Tag>
                      <Text type="secondary">注册时间：{userProfile.createTime}</Text>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>

            <Divider />

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="realName"
                  label="真实姓名"
                  rules={[{ required: true, message: '请输入真实姓名' }]}
                >
                  <Input placeholder="请输入真实姓名" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="nickname"
                  label="昵称"
                  rules={[{ required: true, message: '请输入昵称' }]}
                >
                  <Input placeholder="请输入昵称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="email"
                  label="邮箱地址"
                  rules={[
                    { required: true, message: '请输入邮箱地址' },
                    { type: 'email', message: '请输入正确的邮箱格式' },
                  ]}
                >
                  <Input prefix={<MailOutlined />} placeholder="请输入邮箱地址" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="phone"
                  label="手机号码"
                  rules={[
                    { required: true, message: '请输入手机号码' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
                  ]}
                >
                  <Input prefix={<PhoneOutlined />} placeholder="请输入手机号码" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item name="gender" label="性别">
                  <Select placeholder="请选择性别">
                    <Option value="male">男</Option>
                    <Option value="female">女</Option>
                    <Option value="other">其他</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item name="birthday" label="生日">
                  <DatePicker placeholder="请选择生日" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="address" label="地址">
                  <Input prefix={<EnvironmentOutlined />} placeholder="请输入地址" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="bio" label="个人简介">
                  <TextArea
                    rows={4}
                    placeholder="请输入个人简介"
                    maxLength={200}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="website" label="个人网站">
                  <Input prefix={<GlobalOutlined />} placeholder="请输入个人网站" />
                </Form.Item>
              </Col>
            </Row>

            {/* 社交链接 */}
            <Divider orientation="left">社交链接</Divider>
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item name={['socialLinks', 'wechat']} label="微信号">
                  <Input prefix={<WechatOutlined />} placeholder="请输入微信号" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item name={['socialLinks', 'github']} label="GitHub">
                  <Input prefix={<GithubOutlined />} placeholder="请输入GitHub用户名" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 企业信息 */}
        <Card
          title="企业信息"
          className={styles.companyCard}
          extra={
            <Button
              type={editingCompany ? 'default' : 'primary'}
              icon={editingCompany ? <SaveOutlined /> : <EditOutlined />}
              loading={loading}
              onClick={editingCompany ? handleSaveCompany : () => setEditingCompany(true)}
            >
              {editingCompany ? '保存' : '编辑'}
            </Button>
          }
        >
          <Form
            form={companyForm}
            layout="vertical"
            disabled={!editingCompany}
          >
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="name"
                  label="公司名称"
                  rules={[{ required: true, message: '请输入公司名称' }]}
                >
                  <Input placeholder="请输入公司名称" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="employeeId"
                  label="员工编号"
                  rules={[{ required: true, message: '请输入员工编号' }]}
                >
                  <Input placeholder="请输入员工编号" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="department"
                  label="所属部门"
                  rules={[{ required: true, message: '请输入所属部门' }]}
                >
                  <Input placeholder="请输入所属部门" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name="position"
                  label="职位"
                  rules={[{ required: true, message: '请输入职位' }]}
                >
                  <Input placeholder="请输入职位" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item name="level" label="职级">
                  <Select placeholder="请选择职级">
                    <Option value="P5">P5</Option>
                    <Option value="P6">P6</Option>
                    <Option value="P7">P7</Option>
                    <Option value="P8">P8</Option>
                    <Option value="P9">P9</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item name="workLocation" label="工作地点">
                  <Input placeholder="请输入工作地点" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <div className={styles.managerInfo}>
                  <Text strong>直属上级：</Text>
                  <Space>
                    <Avatar src={companyInfo.manager?.avatar} size="small" />
                    <Text>{companyInfo.manager?.name}</Text>
                  </Space>
                </div>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <div className={styles.teamInfo}>
                  <Text strong>所属团队：</Text>
                  <Space>
                    <Text>{companyInfo.team?.name}</Text>
                    <Tag>{companyInfo.team?.memberCount}人</Tag>
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </Card>
      </div>
    </ProfileLayout>
  );
};

export default BasicInfo;
