.profileLayout {
  .profileSider {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 10;

    .userCard {
      margin: 16px 12px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-body {
        padding: 16px;
      }

      .userInfo {
        text-align: center;

        .avatarSection {
          position: relative;
          margin-bottom: 12px;

          .editAvatar {
            position: absolute;
            bottom: -8px;
            right: 50%;
            transform: translateX(50%);
            font-size: 12px;
            padding: 2px 8px;
            height: 24px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #d9d9d9;

            &:hover {
              background: #fff;
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }

        .userDetails {
          .nickname {
            font-size: 12px;
            display: block;
            margin-bottom: 12px;
          }

          .companyInfo {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;

            .department {
              font-size: 12px;
            }
          }

          .contactInfo {
            .contactItem {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;

              .anticon {
                color: #999;
                width: 14px;
              }
            }
          }
        }
      }
    }

    .profileMenu {
      border-right: none;
      margin: 0 12px;

      .ant-menu-item {
        border-radius: 8px;
        margin-bottom: 4px;
        height: 40px;
        line-height: 40px;

        &:hover {
          background: #f0f8ff;
        }

        &.ant-menu-item-selected {
          background: #e6f7ff;
          color: #1890ff;

          &::after {
            display: none;
          }
        }
      }

      .ant-menu-item-icon {
        font-size: 16px;
      }
    }
  }

  .profileContent {
    background: #f5f5f5;

    .contentHeader {
      background: #fff;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 16px;

      .ant-breadcrumb {
        .ant-breadcrumb-link {
          color: #666;

          &:hover {
            color: #1890ff;
            cursor: pointer;
          }
        }

        .ant-breadcrumb-separator {
          color: #999;
        }
      }
    }

    .contentBody {
      padding: 0 24px 24px;
      min-height: calc(100vh - 120px);
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .profileLayout {
    .profileSider {
      width: 240px !important;
      min-width: 240px !important;
      max-width: 240px !important;

      .userCard {
        margin: 12px 8px;

        .ant-card-body {
          padding: 12px;
        }
      }

      .profileMenu {
        margin: 0 8px;
      }
    }

    .profileContent {
      .contentHeader {
        padding: 12px 16px;
      }

      .contentBody {
        padding: 0 16px 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .profileLayout {
    .profileSider {
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.ant-layout-sider-collapsed {
        transform: translateX(0);
      }
    }

    .profileContent {
      margin-left: 0;

      .contentHeader {
        padding: 8px 12px;
      }

      .contentBody {
        padding: 0 12px 12px;
      }
    }
  }
}

@media (max-width: 576px) {
  .profileLayout {
    .userCard {
      .userInfo {
        .userDetails {
          .contactInfo {
            .contactItem {
              font-size: 11px;
              gap: 6px;

              .anticon {
                width: 12px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .profileMenu {
      .ant-menu-item {
        height: 36px;
        line-height: 36px;
        font-size: 13px;
      }

      .ant-menu-item-icon {
        font-size: 14px;
      }
    }
  }
}

// 暗色主题支持
[data-theme='dark'] {
  .profileLayout {
    .profileSider {
      background: #141414;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);

      .userCard {
        background: #1f1f1f;
        border: 1px solid #303030;

        .userDetails {
          .companyInfo {
            border-bottom-color: #303030;
          }
        }
      }

      .profileMenu {
        background: transparent;

        .ant-menu-item {
          color: #fff;

          &:hover {
            background: #262626;
            color: #1890ff;
          }

          &.ant-menu-item-selected {
            background: #1890ff;
            color: #fff;
          }
        }
      }
    }

    .profileContent {
      background: #000;

      .contentHeader {
        background: #141414;
        border-bottom-color: #303030;
      }
    }
  }
}
