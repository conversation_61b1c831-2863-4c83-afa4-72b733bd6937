import { systemAPI } from '@/services';
import { Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';

interface LogItem {
  id: string;
  module: string;
  action: string;
  content: string;
  createdAt: string;
  ip: string;
}

const OperationLogs = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<LogItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const columns: ColumnsType<LogItem> = [
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 120,
      render: (module) => <Tag color="blue">{module}</Tag>,
    },
    {
      title: '操作类型',
      dataIndex: 'action',
      key: 'action',
      width: 120,
    },
    {
      title: '操作内容',
      dataIndex: 'content',
      key: 'content',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
    },
  ];

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await systemAPI.getOperationLogs({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
      setData(res.data?.list || []);
      setPagination({
        ...pagination,
        total: res.data?.total || 0,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  const handleTableChange = (pagination: any) => {
    setPagination(pagination);
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      pagination={pagination}
      onChange={handleTableChange}
      bordered
      size="middle"
    />
  );
};

export default OperationLogs;
