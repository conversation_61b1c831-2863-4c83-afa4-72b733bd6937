import React, { useState } from 'react';
import { Card, Tabs, Form, Input, Button, message, List } from 'antd';
import { LockOutlined, SafetyOutlined, DesktopOutlined } from '@ant-design/icons';
import { changePassword, getLoginDevices, revokeDevice } from '../services/profile';

const { TabPane } = Tabs;

const PasswordForm: React.FC = () => {
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      await changePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      });
      message.success('密码修改成功');
      form.resetFields();
    } catch (error) {
      message.error(error.response?.data?.message || '密码修改失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} onFinish={onFinish}>
      <Form.Item
        name="currentPassword"
        rules={[{ required: true, message: '请输入当前密码' }]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="当前密码"
        />
      </Form.Item>
      <Form.Item
        name="newPassword"
        rules={[
          { required: true, message: '请输入新密码' },
          { min: 8, message: '密码至少8个字符' },
          { pattern: /(?=.*[0-9])(?=.*[A-Z])/, message: '需包含数字和大写字母' }
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="新密码"
        />
      </Form.Item>
      <Form.Item
        name="confirmPassword"
        dependencies={['newPassword']}
        rules={[
          { required: true, message: '请确认新密码' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('两次输入的密码不一致'));
            },
          }),
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="确认新密码"
        />
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit">
          修改密码
        </Button>
      </Form.Item>
    </Form>
  );
};

const DeviceList: React.FC = () => {
  const [devices, setDevices] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchDevices = async () => {
      setLoading(true);
      try {
        const response = await getLoginDevices();
        setDevices(response.data);
      } catch (error) {
        message.error('获取设备列表失败');
      } finally {
        setLoading(false);
      }
    };
    fetchDevices();
  }, []);

  const handleRevoke = async (deviceId: string) => {
    try {
      await revokeDevice(deviceId);
      setDevices(devices.filter(d => d.id !== deviceId));
      message.success('设备已撤销');
    } catch (error) {
      message.error('撤销设备失败');
    }
  };

  return (
    <div>
      <List
        loading={loading}
        dataSource={devices}
        renderItem={device => (
          <List.Item
            actions={[
              <Button 
                danger 
                size="small"
                onClick={() => handleRevoke(device.id)}
              >
                撤销
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={<DesktopOutlined />}
              title={device.deviceName}
              description={`最后登录: ${device.lastLoginTime} (IP: ${device.ipAddress})`}
            />
          </List.Item>
        )}
      />
    </div>
  );
};

const MFASettings: React.FC = () => {
  return (
    <div>
      <Button type="primary" icon={<SafetyOutlined />}>
        绑定认证器应用
      </Button>
      <Button style={{ marginLeft: 16 }}>
        绑定手机短信验证
      </Button>
    </div>
  );
};

const SecurityCenter: React.FC = () => {
  return (
    <Card title="安全中心" style={{ marginTop: 16 }}>
      <Tabs defaultActiveKey="1">
        <TabPane tab="密码修改" key="1">
          <PasswordForm />
        </TabPane>
        <TabPane tab="MFA绑定" key="2">
          <MFASettings />
        </TabPane>
        <TabPane tab="登录设备" key="3">
          <DeviceList />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default SecurityCenter;
