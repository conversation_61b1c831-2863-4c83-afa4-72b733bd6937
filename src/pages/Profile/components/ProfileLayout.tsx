import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'umi';
import {
  Layout,
  Menu,
  Avatar,
  Card,
  Typography,
  Tag,
  Space,
  Button,
  Breadcrumb,
  Badge,
} from 'antd';
import {
  UserOutlined,
  SafetyOutlined,
  TeamOutlined,
  SettingOutlined,
  FileTextOutlined,
  DashboardOutlined,
  CrownOutlined,
  EditOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
} from '@ant-design/icons';
import type { UserProfile, CompanyInfo } from '@/types/profile';
import styles from './ProfileLayout.less';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

// 模拟用户数据
const mockUserProfile: UserProfile = {
  id: '1',
  username: 'zhang<PERSON>',
  email: '<EMAIL>',
  phone: '138****8888',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhangsan',
  realName: '张三',
  nickname: '产品小王子',
  gender: 'male',
  birthday: '1990-05-15',
  address: '上海市浦东新区张江高科技园区',
  bio: '专注产品设计与用户体验，热爱技术创新',
  website: 'https://zhangsan.dev',
  socialLinks: {
    wechat: 'zhangsan_wx',
    github: 'zhangsan',
  },
  createTime: '2022-01-15 10:30:00',
  updateTime: '2024-01-15 14:20:00',
  lastLoginTime: '2024-01-15 09:15:00',
  status: 'active',
};

const mockCompanyInfo: CompanyInfo = {
  id: '1',
  name: '前端科技有限公司',
  department: '产品技术部',
  position: '高级产品经理',
  level: 'P7',
  employeeId: 'FE2022001',
  hireDate: '2022-01-15',
  workLocation: '上海张江办公区',
  manager: {
    id: '2',
    name: '李四',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisi',
  },
  team: {
    id: '1',
    name: '产品创新团队',
    memberCount: 12,
  },
};

const ProfileLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [userProfile] = useState<UserProfile>(mockUserProfile);
  const [companyInfo] = useState<CompanyInfo>(mockCompanyInfo);
  const [collapsed, setCollapsed] = useState<boolean>(false);

  // 菜单配置
  const menuItems = [
    {
      key: '/profile/overview',
      icon: <DashboardOutlined />,
      label: '个人概览',
    },
    {
      key: '/profile/basic',
      icon: <UserOutlined />,
      label: '基本信息',
    },
    {
      key: '/profile/security',
      icon: <SafetyOutlined />,
      label: '账号安全',
    },
    {
      key: '/profile/permissions',
      icon: <CrownOutlined />,
      label: '权限管理',
    },
    {
      key: '/profile/workspace',
      icon: <DashboardOutlined />,
      label: '我的工作台',
    },
    {
      key: '/profile/content',
      icon: <FileTextOutlined />,
      label: '我的内容',
    },
    {
      key: '/profile/team',
      icon: <TeamOutlined />,
      label: '团队协作',
    },
    {
      key: '/profile/settings',
      icon: <SettingOutlined />,
      label: '个人设置',
    },
  ];

  // 面包屑配置
  const getBreadcrumbItems = () => {
    const pathMap: Record<string, string> = {
      '/profile/overview': '个人概览',
      '/profile/basic': '基本信息',
      '/profile/security': '账号安全',
      '/profile/permissions': '权限管理',
      '/profile/workspace': '我的工作台',
      '/profile/content': '我的内容',
      '/profile/team': '团队协作',
      '/profile/settings': '个人设置',
    };

    return [
      {
        title: '个人中心',
        onClick: () => navigate('/profile/overview'),
      },
      {
        title: pathMap[location.pathname] || '未知页面',
      },
    ];
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <div className={styles.profileLayout}>
      <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
        <Sider
          width={280}
          collapsed={collapsed}
          onCollapse={setCollapsed}
          className={styles.profileSider}
          theme="light"
        >
          {/* 用户信息卡片 */}
          <Card className={styles.userCard} bordered={false}>
            <div className={styles.userInfo}>
              <div className={styles.avatarSection}>
                <Badge
                  dot
                  status="success"
                  offset={[-8, 8]}
                >
                  <Avatar
                    size={collapsed ? 40 : 80}
                    src={userProfile.avatar}
                    icon={<UserOutlined />}
                  />
                </Badge>
                {!collapsed && (
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    className={styles.editAvatar}
                    onClick={() => navigate('/profile/basic')}
                  >
                    编辑
                  </Button>
                )}
              </div>
              
              {!collapsed && (
                <div className={styles.userDetails}>
                  <Title level={4} style={{ margin: '8px 0 4px' }}>
                    {userProfile.realName}
                  </Title>
                  <Text type="secondary" className={styles.nickname}>
                    @{userProfile.nickname}
                  </Text>
                  
                  <div className={styles.companyInfo}>
                    <Space direction="vertical" size="small">
                      <div>
                        <Tag color="blue">{companyInfo.position}</Tag>
                        <Tag color="green">{companyInfo.level}</Tag>
                      </div>
                      <Text type="secondary" className={styles.department}>
                        {companyInfo.department}
                      </Text>
                    </Space>
                  </div>

                  <div className={styles.contactInfo}>
                    <Space direction="vertical" size="small">
                      <div className={styles.contactItem}>
                        <MailOutlined />
                        <Text type="secondary">{userProfile.email}</Text>
                      </div>
                      <div className={styles.contactItem}>
                        <PhoneOutlined />
                        <Text type="secondary">{userProfile.phone}</Text>
                      </div>
                      <div className={styles.contactItem}>
                        <EnvironmentOutlined />
                        <Text type="secondary">{companyInfo.workLocation}</Text>
                      </div>
                    </Space>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* 导航菜单 */}
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className={styles.profileMenu}
          />
        </Sider>

        <Layout className={styles.profileContent}>
          <Content>
            <div className={styles.contentHeader}>
              <Breadcrumb items={getBreadcrumbItems()} />
            </div>
            <div className={styles.contentBody}>
              <Outlet />
            </div>
          </Content>
        </Layout>
      </Layout>
    </div>
  );
};

export default ProfileLayout;
