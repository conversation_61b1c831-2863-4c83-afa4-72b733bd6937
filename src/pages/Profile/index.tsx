import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions } from 'antd';
import SecurityCenter from './components/SecurityCenter';

const ProfilePage: React.FC = () => {
  // TODO: 从HR系统获取数据
  const userInfo = {
    name: '张三',
    employeeId: '10086',
    department: '技术部',
    position: '前端工程师'
  };

  return (
    <PageContainer>
      <Card title="账户与安全" bordered={false}>
        <Descriptions title="基本信息" column={1}>
          <Descriptions.Item label="姓名">{userInfo.name}</Descriptions.Item>
          <Descriptions.Item label="工号">{userInfo.employeeId}</Descriptions.Item>
          <Descriptions.Item label="部门">{userInfo.department}</Descriptions.Item>
          <Descriptions.Item label="职位">{userInfo.position}</Descriptions.Item>
        </Descriptions>
      </Card>
      <SecurityCenter />
    </PageContainer>
  );
};

export default ProfilePage;
