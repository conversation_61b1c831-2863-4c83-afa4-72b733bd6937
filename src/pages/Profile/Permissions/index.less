.permissions {
  .overviewCard,
  .permissionCard,
  .detailCard {
    margin-bottom: 16px;
  }

  .roleInfo {
    .roleName {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      margin-bottom: 4px;

      .anticon {
        color: #1890ff;
      }
    }

    .roleDesc {
      font-size: 12px;
    }
  }

  .scopeDetail {
    margin-top: 4px;

    .ant-tag {
      margin-bottom: 2px;
    }
  }

  .permissionList {
    .permissionTag {
      margin-bottom: 4px;
    }
  }

  .permissionDetails {
    .roleDetail {
      margin-bottom: 24px;
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-typography-title {
        margin-bottom: 16px;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .permissionGrid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 12px;

        .permissionItem {
          padding: 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #f0f0f0;

          .permissionName {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            margin-bottom: 4px;

            .anticon {
              color: #52c41a;
            }
          }

          .permissionCode {
            font-size: 12px;
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            margin-bottom: 4px;
            display: inline-block;
          }

          .permissionScope {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .permissions {
    .permissionDetails {
      .roleDetail {
        padding: 12px;

        .permissionGrid {
          grid-template-columns: 1fr;
          gap: 8px;

          .permissionItem {
            padding: 8px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .permissions {
    .roleInfo {
      .roleName {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }

    .permissionDetails {
      .roleDetail {
        .permissionGrid {
          .permissionItem {
            .permissionName {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;
            }
          }
        }
      }
    }
  }
}
