import React, { useState } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Alert,
  Modal,
  Form,
  Input,
  message,
  Descriptions,
  Badge,
} from 'antd';
import {
  CrownOutlined,
  SafetyOutlined,
  TeamOutlined,
  FileTextOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { RolePermission } from '@/types/profile';
import styles from './index.less';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 模拟权限数据
const mockPermissions: RolePermission[] = [
  {
    id: '1',
    name: '高级产品经理',
    code: 'senior_product_manager',
    description: '负责产品规划、需求分析和团队协作',
    type: 'business',
    permissions: [
      { id: '1', name: '内容管理', code: 'content_manage', resource: 'content', action: 'manage' },
      { id: '2', name: '用户管理', code: 'user_manage', resource: 'user', action: 'manage' },
      { id: '3', name: '数据查看', code: 'data_view', resource: 'analytics', action: 'view' },
    ],
    dataScope: {
      type: 'department',
      departments: ['产品技术部'],
    },
  },
  {
    id: '2',
    name: '内容审核员',
    code: 'content_reviewer',
    description: '负责内容审核和质量管控',
    type: 'business',
    permissions: [
      { id: '4', name: '内容审核', code: 'content_review', resource: 'content', action: 'review' },
      { id: '5', name: '内容查看', code: 'content_view', resource: 'content', action: 'view' },
    ],
    dataScope: {
      type: 'all',
    },
  },
];

const Permissions: React.FC = () => {
  const [form] = Form.useForm();
  const [permissions] = useState<RolePermission[]>(mockPermissions);
  const [requestModalVisible, setRequestModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 权限类型配置
  const typeConfig = {
    system: { color: 'red', text: '系统权限' },
    business: { color: 'blue', text: '业务权限' },
    data: { color: 'green', text: '数据权限' },
  };

  // 数据范围配置
  const scopeConfig = {
    all: { text: '全部数据', color: 'red' },
    department: { text: '部门数据', color: 'blue' },
    self: { text: '个人数据', color: 'green' },
    custom: { text: '自定义', color: 'orange' },
  };

  // 权限表格列配置
  const columns: ColumnsType<RolePermission> = [
    {
      title: '角色信息',
      key: 'role',
      render: (_, record) => (
        <div className={styles.roleInfo}>
          <div className={styles.roleName}>
            <CrownOutlined />
            <span>{record.name}</span>
          </div>
          <Text type="secondary" className={styles.roleDesc}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: '权限类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: keyof typeof typeConfig) => (
        <Tag color={typeConfig[type].color}>{typeConfig[type].text}</Tag>
      ),
    },
    {
      title: '数据范围',
      key: 'dataScope',
      render: (_, record) => (
        <div>
          <Tag color={scopeConfig[record.dataScope?.type || 'self'].color}>
            {scopeConfig[record.dataScope?.type || 'self'].text}
          </Tag>
          {record.dataScope?.departments && (
            <div className={styles.scopeDetail}>
              {record.dataScope.departments.map(dept => (
                <Tag key={dept} size="small">{dept}</Tag>
              ))}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '具体权限',
      key: 'permissions',
      render: (_, record) => (
        <div className={styles.permissionList}>
          {record.permissions.map(permission => (
            <Tag key={permission.id} className={styles.permissionTag}>
              {permission.name}
            </Tag>
          ))}
        </div>
      ),
    },
  ];

  // 申请权限
  const handleRequestPermission = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      console.log('申请权限:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('权限申请已提交，请等待审批');
      setRequestModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('请完善申请信息');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.permissions}>
        {/* 权限概览 */}
        <Card title="权限概览" className={styles.overviewCard}>
          <Alert
            message="权限说明"
            description="您当前拥有以下角色权限。如需申请新的权限，请点击申请权限按钮。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Descriptions column={2} bordered>
            <Descriptions.Item label="当前角色数量">
              <Badge count={permissions.length} showZero color="blue" />
            </Descriptions.Item>
            <Descriptions.Item label="权限状态">
              <Badge status="success" text="正常" />
            </Descriptions.Item>
            <Descriptions.Item label="数据访问范围">
              部门级别
            </Descriptions.Item>
            <Descriptions.Item label="最后更新时间">
              2024-01-15 10:30:00
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 角色权限列表 */}
        <Card 
          title="我的角色权限"
          className={styles.permissionCard}
          extra={
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setRequestModalVisible(true)}
            >
              申请权限
            </Button>
          }
        >
          <Table
            dataSource={permissions}
            columns={columns}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 权限详情 */}
        <Card title="权限详细说明" className={styles.detailCard}>
          <div className={styles.permissionDetails}>
            {permissions.map(role => (
              <div key={role.id} className={styles.roleDetail}>
                <Title level={5}>
                  <CrownOutlined /> {role.name}
                </Title>
                <div className={styles.permissionGrid}>
                  {role.permissions.map(permission => (
                    <div key={permission.id} className={styles.permissionItem}>
                      <div className={styles.permissionName}>
                        <SafetyOutlined />
                        {permission.name}
                      </div>
                      <Text type="secondary" className={styles.permissionCode}>
                        {permission.code}
                      </Text>
                      <div className={styles.permissionScope}>
                        <Text type="secondary">
                          资源：{permission.resource} | 操作：{permission.action}
                        </Text>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 申请权限模态框 */}
        <Modal
          title="申请权限"
          open={requestModalVisible}
          onOk={handleRequestPermission}
          onCancel={() => {
            setRequestModalVisible(false);
            form.resetFields();
          }}
          confirmLoading={loading}
          width={600}
        >
          <Alert
            message="权限申请说明"
            description="请详细说明申请权限的原因和用途，管理员将根据您的申请进行审批。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form form={form} layout="vertical">
            <Form.Item
              name="permissionType"
              label="申请权限类型"
              rules={[{ required: true, message: '请选择权限类型' }]}
            >
              <Input placeholder="例如：内容管理权限、用户管理权限等" />
            </Form.Item>
            
            <Form.Item
              name="reason"
              label="申请原因"
              rules={[{ required: true, message: '请输入申请原因' }]}
            >
              <TextArea
                rows={4}
                placeholder="请详细说明申请该权限的原因和用途..."
                maxLength={500}
                showCount
              />
            </Form.Item>
            
            <Form.Item
              name="urgency"
              label="紧急程度"
              rules={[{ required: true, message: '请选择紧急程度' }]}
            >
              <Input placeholder="例如：紧急、一般、不紧急" />
            </Form.Item>
          </Form>
        </Modal>
    </div>
  );
};

export default Permissions;
