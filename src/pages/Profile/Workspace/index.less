.workspace {
  .welcomeCard {
    margin-bottom: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .ant-card-body {
      padding: 24px;
    }

    .ant-typography-title {
      color: white !important;
      margin-bottom: 8px;
    }

    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.8);
    }

    .ant-statistic-content {
      color: white;
    }
  }

  .todoCard,
  .quickActionsCard,
  .progressCard,
  .activityCard,
  .notificationCard {
    margin-bottom: 16px;
  }

  .todoTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .todoDesc {
    .todoMeta {
      margin-top: 8px;
      font-size: 12px;
    }
  }

  .quickActionsCard {
    .actionCard {
      text-align: center;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .actionContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .actionLabel {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .progressCard {
    .progressItem {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .progressHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
      }
    }
  }

  .activityCard {
    .activityContent {
      font-size: 14px;
      color: #262626;
      margin-bottom: 4px;
    }

    .activityTime {
      font-size: 12px;
    }
  }

  .notificationCard {
    .notificationTitle {
      font-size: 13px;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workspace {
    .welcomeCard {
      .ant-row {
        flex-direction: column;
        gap: 16px;
      }
    }

    .todoTitle {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .quickActionsCard {
      .actionCard {
        .actionContent {
          .actionLabel {
            font-size: 11px;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .workspace {
    .welcomeCard {
      .ant-card-body {
        padding: 16px;
      }
    }

    .progressCard {
      .progressItem {
        .progressHeader {
          font-size: 12px;
        }
      }
    }
  }
}
