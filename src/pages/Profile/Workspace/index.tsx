import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Badge,
  Empty,
  Statistic,
  Progress,
  Calendar,
  Timeline,
  Tooltip,
} from 'antd';
import {
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  BellOutlined,
  TrophyOutlined,
  TeamOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import type { TodoItem, PersonalStats } from '@/types/profile';
import ProfileLayout from '../components/ProfileLayout';
import styles from './index.less';

const { Title, Text } = Typography;

// 模拟数据
const mockTodos: TodoItem[] = [
  {
    id: '1',
    title: '审核新用户发布的视频内容',
    description: '需要审核3个新用户发布的短视频内容',
    type: 'content_review',
    priority: 'high',
    status: 'pending',
    assignee: 'current_user',
    assigneeName: '张三',
    dueDate: '2024-01-16 18:00:00',
    createTime: '2024-01-15 14:30:00',
    updateTime: '2024-01-15 14:30:00',
  },
  {
    id: '2',
    title: '跟进重点客户合作进展',
    description: '联系ABC公司确认合作方案细节',
    type: 'customer_follow',
    priority: 'urgent',
    status: 'pending',
    assignee: 'current_user',
    assigneeName: '张三',
    dueDate: '2024-01-16 10:00:00',
    createTime: '2024-01-15 09:15:00',
    updateTime: '2024-01-15 09:15:00',
  },
  {
    id: '3',
    title: '准备月度运营报告',
    description: '整理本月运营数据并制作PPT',
    type: 'other',
    priority: 'normal',
    status: 'in_progress',
    assignee: 'current_user',
    assigneeName: '张三',
    dueDate: '2024-01-18 17:00:00',
    createTime: '2024-01-14 10:00:00',
    updateTime: '2024-01-15 11:20:00',
  },
];

const mockStats: PersonalStats = {
  contentStats: {
    totalContent: 156,
    publishedContent: 142,
    draftContent: 14,
    totalViews: 125680,
    totalLikes: 8945,
    totalComments: 2156,
    monthlyGrowth: 15.6,
  },
  customerStats: {
    totalCustomers: 89,
    activeCustomers: 67,
    newCustomers: 12,
    conversionRate: 23.5,
    totalValue: 1256000,
    monthlyValue: 156000,
  },
  taskStats: {
    totalTasks: 245,
    completedTasks: 198,
    pendingTasks: 32,
    overdueTask: 15,
    completionRate: 80.8,
    avgCompletionTime: 2.5,
  },
  teamStats: {
    teamSize: 12,
    activeProjects: 8,
    completedProjects: 25,
    teamPerformance: 92.5,
  },
};

const Workspace: React.FC = () => {
  const [todos] = useState<TodoItem[]>(mockTodos);
  const [stats] = useState<PersonalStats>(mockStats);

  // 优先级配置
  const priorityConfig = {
    low: { color: 'default', text: '低' },
    normal: { color: 'blue', text: '普通' },
    high: { color: 'orange', text: '高' },
    urgent: { color: 'red', text: '紧急' },
  };

  // 任务类型配置
  const taskTypeConfig = {
    content_review: { color: 'blue', text: '内容审核', icon: <FileTextOutlined /> },
    customer_follow: { color: 'green', text: '客户跟进', icon: <UserOutlined /> },
    activity_task: { color: 'purple', text: '活动任务', icon: <CalendarOutlined /> },
    approval: { color: 'orange', text: '审批流程', icon: <CheckCircleOutlined /> },
    other: { color: 'default', text: '其他', icon: <ExclamationCircleOutlined /> },
  };

  // 状态配置
  const statusConfig = {
    pending: { color: 'default', text: '待处理', icon: <ClockCircleOutlined /> },
    in_progress: { color: 'processing', text: '进行中', icon: <ExclamationCircleOutlined /> },
    completed: { color: 'success', text: '已完成', icon: <CheckCircleOutlined /> },
    cancelled: { color: 'error', text: '已取消', icon: <ExclamationCircleOutlined /> },
  };

  // 处理任务操作
  const handleTodoAction = (todoId: string, action: string) => {
    console.log('处理任务:', todoId, action);
    // 这里可以调用API更新任务状态
  };

  // 快速操作
  const quickActions = [
    { key: 'create_content', label: '创建内容', icon: <FileTextOutlined />, color: '#1890ff' },
    { key: 'add_customer', label: '添加客户', icon: <UserOutlined />, color: '#52c41a' },
    { key: 'schedule_meeting', label: '安排会议', icon: <CalendarOutlined />, color: '#722ed1' },
    { key: 'team_collaborate', label: '团队协作', icon: <TeamOutlined />, color: '#fa8c16' },
  ];

  // 最近活动时间线
  const recentActivities = [
    {
      time: '2024-01-15 14:30',
      content: '完成了内容审核任务',
      type: 'success',
    },
    {
      time: '2024-01-15 11:20',
      content: '更新了月度运营报告进度',
      type: 'info',
    },
    {
      time: '2024-01-15 09:15',
      content: '创建了新的客户跟进任务',
      type: 'info',
    },
    {
      time: '2024-01-14 16:45',
      content: '发布了新的产品介绍文章',
      type: 'success',
    },
  ];

  return (
    <ProfileLayout>
      <div className={styles.workspace}>
        {/* 欢迎信息 */}
        <Card className={styles.welcomeCard}>
          <Row align="middle">
            <Col flex="auto">
              <Title level={3} style={{ margin: 0 }}>
                早上好，张三！
              </Title>
              <Text type="secondary">
                今天是 {new Date().toLocaleDateString('zh-CN', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long'
                })}
              </Text>
            </Col>
            <Col>
              <Space size="large">
                <Statistic
                  title="待处理任务"
                  value={stats.taskStats.pendingTasks}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
                <Statistic
                  title="今日完成"
                  value={8}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Space>
            </Col>
          </Row>
        </Card>

        <Row gutter={[16, 16]}>
          {/* 左侧内容 */}
          <Col xs={24} lg={16}>
            {/* 我的待办 */}
            <Card 
              title={
                <Space>
                  <ClockCircleOutlined />
                  我的待办
                  <Badge count={todos.filter(t => t.status === 'pending').length} />
                </Space>
              }
              className={styles.todoCard}
              extra={<Button type="link">查看全部</Button>}
            >
              {todos.length > 0 ? (
                <List
                  dataSource={todos}
                  renderItem={(item) => (
                    <List.Item
                      key={item.id}
                      actions={[
                        <Button 
                          type="link" 
                          size="small"
                          onClick={() => handleTodoAction(item.id, 'complete')}
                        >
                          {item.status === 'pending' ? '开始处理' : '查看详情'}
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            icon={taskTypeConfig[item.type].icon} 
                            style={{ backgroundColor: taskTypeConfig[item.type].color }}
                          />
                        }
                        title={
                          <div className={styles.todoTitle}>
                            <span>{item.title}</span>
                            <Space size="small">
                              <Tag color={priorityConfig[item.priority].color}>
                                {priorityConfig[item.priority].text}
                              </Tag>
                              <Tag color={statusConfig[item.status].color}>
                                {statusConfig[item.status].text}
                              </Tag>
                            </Space>
                          </div>
                        }
                        description={
                          <div className={styles.todoDesc}>
                            <Text type="secondary">{item.description}</Text>
                            <div className={styles.todoMeta}>
                              <Space size="small">
                                <ClockCircleOutlined />
                                <Text type="secondary">截止：{item.dueDate}</Text>
                              </Space>
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty description="暂无待办事项" />
              )}
            </Card>

            {/* 快速操作 */}
            <Card title="快速操作" className={styles.quickActionsCard}>
              <Row gutter={[16, 16]}>
                {quickActions.map(action => (
                  <Col xs={12} sm={6} key={action.key}>
                    <Card
                      hoverable
                      className={styles.actionCard}
                      onClick={() => console.log('快速操作:', action.key)}
                    >
                      <div className={styles.actionContent}>
                        <Avatar 
                          icon={action.icon} 
                          style={{ backgroundColor: action.color }}
                          size="large"
                        />
                        <Text className={styles.actionLabel}>{action.label}</Text>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>

          {/* 右侧内容 */}
          <Col xs={24} lg={8}>
            {/* 工作进度 */}
            <Card title="工作进度" className={styles.progressCard}>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>任务完成率</span>
                  <span>{stats.taskStats.completionRate}%</span>
                </div>
                <Progress 
                  percent={stats.taskStats.completionRate} 
                  strokeColor="#52c41a"
                />
              </div>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>客户转化率</span>
                  <span>{stats.customerStats.conversionRate}%</span>
                </div>
                <Progress 
                  percent={stats.customerStats.conversionRate} 
                  strokeColor="#1890ff"
                />
              </div>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>内容增长</span>
                  <span>+{stats.contentStats.monthlyGrowth}%</span>
                </div>
                <Progress 
                  percent={stats.contentStats.monthlyGrowth} 
                  strokeColor="#722ed1"
                />
              </div>
            </Card>

            {/* 最近活动 */}
            <Card title="最近活动" className={styles.activityCard}>
              <Timeline
                items={recentActivities.map(activity => ({
                  color: activity.type === 'success' ? 'green' : 'blue',
                  children: (
                    <div>
                      <div className={styles.activityContent}>{activity.content}</div>
                      <Text type="secondary" className={styles.activityTime}>
                        {activity.time}
                      </Text>
                    </div>
                  ),
                }))}
              />
            </Card>

            {/* 消息通知 */}
            <Card 
              title={
                <Space>
                  <BellOutlined />
                  消息通知
                  <Badge count={5} />
                </Space>
              }
              className={styles.notificationCard}
              extra={<Button type="link">查看全部</Button>}
            >
              <List
                size="small"
                dataSource={[
                  { id: '1', title: '您有新的评论待回复', time: '5分钟前', type: 'comment' },
                  { id: '2', title: '客户ABC公司回复了您的消息', time: '1小时前', type: 'message' },
                  { id: '3', title: '您的内容审核已通过', time: '2小时前', type: 'system' },
                ]}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={<Text className={styles.notificationTitle}>{item.title}</Text>}
                      description={<Text type="secondary">{item.time}</Text>}
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </div>
    </ProfileLayout>
  );
};

export default Workspace;
