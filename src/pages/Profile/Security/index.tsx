import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  message,
  Switch,
  Row,
  Col,
  Typography,
  Alert,
  Tabs,
  List,
  Avatar,
  Tooltip,
  Badge,
} from 'antd';
import {
  SafetyOutlined,
  LockOutlined,
  PhoneOutlined,
  MailOutlined,
  ShieldOutlined,
  DeleteOutlined,
  EyeOutlined,
  DesktopOutlined,
  MobileOutlined,
  GlobalOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { LoginDevice, OperationLog, SecuritySettings } from '@/types/profile';
import styles from './index.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 模拟登录设备数据
const mockDevices: LoginDevice[] = [
  {
    id: '1',
    deviceType: 'web',
    deviceName: 'Chrome 浏览器',
    browser: 'Chrome 120.0.0.0',
    os: 'macOS 14.2',
    ip: '*************',
    location: '上海市浦东新区',
    loginTime: '2024-01-15 09:15:00',
    lastActiveTime: '2024-01-15 14:30:00',
    isCurrent: true,
    status: 'active',
  },
  {
    id: '2',
    deviceType: 'mobile',
    deviceName: 'iPhone 15 Pro',
    os: 'iOS 17.2',
    ip: '*************',
    location: '上海市浦东新区',
    loginTime: '2024-01-14 18:20:00',
    lastActiveTime: '2024-01-15 08:45:00',
    isCurrent: false,
    status: 'active',
  },
  {
    id: '3',
    deviceType: 'desktop',
    deviceName: 'Windows PC',
    os: 'Windows 11',
    ip: '*********',
    location: '上海市徐汇区',
    loginTime: '2024-01-13 10:30:00',
    lastActiveTime: '2024-01-13 17:45:00',
    isCurrent: false,
    status: 'expired',
  },
];

// 模拟操作日志数据
const mockLogs: OperationLog[] = [
  {
    id: '1',
    action: '登录系统',
    module: '用户认证',
    description: '用户通过密码登录系统',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    location: '上海市浦东新区',
    result: 'success',
    createTime: '2024-01-15 09:15:00',
    duration: 1200,
  },
  {
    id: '2',
    action: '修改个人信息',
    module: '个人中心',
    description: '更新了个人简介信息',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    location: '上海市浦东新区',
    result: 'success',
    createTime: '2024-01-14 16:20:00',
    duration: 800,
  },
  {
    id: '3',
    action: '登录失败',
    module: '用户认证',
    description: '密码错误导致登录失败',
    ip: '***********',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    location: '北京市朝阳区',
    result: 'failure',
    errorMessage: '密码错误',
    createTime: '2024-01-14 10:30:00',
    duration: 500,
  },
];

// 模拟安全设置数据
const mockSecuritySettings: SecuritySettings = {
  passwordLastChanged: '2024-01-01 10:30:00',
  mfaEnabled: true,
  mfaType: 'totp',
  loginNotification: true,
  sessionTimeout: 7200,
  allowMultipleLogin: true,
  ipWhitelist: ['***********/24'],
  trustedDevices: ['device_001', 'device_002'],
};

const Security: React.FC = () => {
  const [passwordForm] = Form.useForm();
  const [phoneForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [devices] = useState<LoginDevice[]>(mockDevices);
  const [logs] = useState<OperationLog[]>(mockLogs);
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>(mockSecuritySettings);
  const [passwordModalVisible, setPasswordModalVisible] = useState<boolean>(false);
  const [phoneModalVisible, setPhoneModalVisible] = useState<boolean>(false);
  const [emailModalVisible, setEmailModalVisible] = useState<boolean>(false);
  const [mfaModalVisible, setMfaModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 设备类型图标
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'web':
        return <GlobalOutlined />;
      case 'mobile':
        return <MobileOutlined />;
      case 'desktop':
        return <DesktopOutlined />;
      default:
        return <DesktopOutlined />;
    }
  };

  // 设备表格列配置
  const deviceColumns: ColumnsType<LoginDevice> = [
    {
      title: '设备信息',
      key: 'device',
      render: (_, record) => (
        <div className={styles.deviceInfo}>
          <Space>
            <Avatar icon={getDeviceIcon(record.deviceType)} />
            <div>
              <div className={styles.deviceName}>
                {record.deviceName}
                {record.isCurrent && <Tag color="blue" size="small">当前设备</Tag>}
              </div>
              <Text type="secondary" className={styles.deviceDetails}>
                {record.browser && `${record.browser} • `}{record.os}
              </Text>
            </div>
          </Space>
        </div>
      ),
    },
    {
      title: '登录信息',
      key: 'login',
      render: (_, record) => (
        <div className={styles.loginInfo}>
          <div>IP: {record.ip}</div>
          <div>位置: {record.location}</div>
          <div>登录时间: {record.loginTime}</div>
          <div>最后活跃: {record.lastActiveTime}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '活跃' : '已过期'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {!record.isCurrent && (
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveDevice(record.id)}
            >
              移除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 操作日志表格列配置
  const logColumns: ColumnsType<OperationLog> = [
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string, record) => (
        <div className={styles.actionInfo}>
          <div className={styles.actionName}>
            {record.result === 'success' ? (
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
            ) : (
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            )}
            <span>{action}</span>
          </div>
          <Text type="secondary">{record.description}</Text>
        </div>
      ),
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
    },
    {
      title: '位置',
      key: 'location',
      render: (_, record) => (
        <div>
          <div>{record.location}</div>
          <Text type="secondary">{record.ip}</Text>
        </div>
      ),
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      render: (result: string, record) => (
        <div>
          <Tag color={result === 'success' ? 'success' : 'error'}>
            {result === 'success' ? '成功' : '失败'}
          </Tag>
          {record.errorMessage && (
            <Tooltip title={record.errorMessage}>
              <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginLeft: 8 }} />
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  // 修改密码
  const handleChangePassword = async () => {
    try {
      setLoading(true);
      const values = await passwordForm.validateFields();
      
      if (values.newPassword !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      console.log('修改密码:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error) {
      message.error('请完善必填信息');
    } finally {
      setLoading(false);
    }
  };

  // 绑定手机号
  const handleBindPhone = async () => {
    try {
      setLoading(true);
      const values = await phoneForm.validateFields();
      
      console.log('绑定手机号:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('手机号绑定成功');
      setPhoneModalVisible(false);
      phoneForm.resetFields();
    } catch (error) {
      message.error('请完善必填信息');
    } finally {
      setLoading(false);
    }
  };

  // 绑定邮箱
  const handleBindEmail = async () => {
    try {
      setLoading(true);
      const values = await emailForm.validateFields();
      
      console.log('绑定邮箱:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('邮箱绑定成功');
      setEmailModalVisible(false);
      emailForm.resetFields();
    } catch (error) {
      message.error('请完善必填信息');
    } finally {
      setLoading(false);
    }
  };

  // 移除设备
  const handleRemoveDevice = (deviceId: string) => {
    Modal.confirm({
      title: '确认移除设备',
      content: '移除后该设备将无法访问您的账号，确定要移除吗？',
      onOk: () => {
        console.log('移除设备:', deviceId);
        message.success('设备移除成功');
      },
    });
  };

  // 发送验证码
  const handleSendCode = (type: 'phone' | 'email') => {
    message.success(`验证码已发送到您的${type === 'phone' ? '手机' : '邮箱'}`);
  };

  // 切换MFA
  const handleToggleMFA = (enabled: boolean) => {
    if (enabled) {
      setMfaModalVisible(true);
    } else {
      Modal.confirm({
        title: '确认关闭双因子认证',
        content: '关闭后您的账号安全性将降低，确定要关闭吗？',
        onOk: () => {
          setSecuritySettings(prev => ({ ...prev, mfaEnabled: false }));
          message.success('双因子认证已关闭');
        },
      });
    }
  };

  return (
    <div className={styles.security}>
        <Tabs defaultActiveKey="password" className={styles.securityTabs}>
          <TabPane tab="密码安全" key="password">
            <Card title="密码管理">
              <Alert
                message="安全提示"
                description="为了您的账号安全，建议定期更换密码，密码应包含大小写字母、数字和特殊字符。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <div className={styles.passwordInfo}>
                <Row gutter={24}>
                  <Col span={12}>
                    <div className={styles.infoItem}>
                      <LockOutlined className={styles.infoIcon} />
                      <div>
                        <div className={styles.infoTitle}>登录密码</div>
                        <Text type="secondary">
                          上次修改：{securitySettings.passwordLastChanged}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Button 
                      type="primary" 
                      onClick={() => setPasswordModalVisible(true)}
                    >
                      修改密码
                    </Button>
                  </Col>
                </Row>
              </div>
            </Card>

            <Card title="双因子认证" style={{ marginTop: 16 }}>
              <div className={styles.mfaInfo}>
                <Row gutter={24} align="middle">
                  <Col span={12}>
                    <div className={styles.infoItem}>
                      <ShieldOutlined className={styles.infoIcon} />
                      <div>
                        <div className={styles.infoTitle}>
                          双因子认证
                          {securitySettings.mfaEnabled && (
                            <Badge status="success" text="已启用" style={{ marginLeft: 8 }} />
                          )}
                        </div>
                        <Text type="secondary">
                          {securitySettings.mfaEnabled 
                            ? `当前使用：${securitySettings.mfaType === 'totp' ? 'TOTP应用' : '短信验证'}`
                            : '增强账号安全性，防止账号被盗用'
                          }
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Switch
                      checked={securitySettings.mfaEnabled}
                      onChange={handleToggleMFA}
                      checkedChildren="已启用"
                      unCheckedChildren="已关闭"
                    />
                  </Col>
                </Row>
              </div>
            </Card>
          </TabPane>

          <TabPane tab="账号绑定" key="binding">
            <Card title="账号绑定">
              <List
                itemLayout="horizontal"
                dataSource={[
                  {
                    key: 'phone',
                    title: '手机号码',
                    description: '用于登录、找回密码、接收验证码',
                    value: '138****8888',
                    bound: true,
                    icon: <PhoneOutlined />,
                  },
                  {
                    key: 'email',
                    title: '邮箱地址',
                    description: '用于登录、找回密码、接收通知',
                    value: 'zhang***@company.com',
                    bound: true,
                    icon: <MailOutlined />,
                  },
                ]}
                renderItem={(item) => (
                  <List.Item
                    actions={[
                      <Button
                        key="action"
                        type={item.bound ? 'default' : 'primary'}
                        onClick={() => {
                          if (item.key === 'phone') {
                            setPhoneModalVisible(true);
                          } else {
                            setEmailModalVisible(true);
                          }
                        }}
                      >
                        {item.bound ? '更换' : '绑定'}
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<Avatar icon={item.icon} />}
                      title={
                        <div className={styles.bindingTitle}>
                          {item.title}
                          {item.bound && <Tag color="success">已绑定</Tag>}
                        </div>
                      }
                      description={
                        <div>
                          <div>{item.description}</div>
                          {item.bound && (
                            <Text type="secondary">当前绑定：{item.value}</Text>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </TabPane>

          <TabPane tab="登录设备" key="devices">
            <Card title="登录设备管理">
              <Alert
                message="安全提示"
                description="如发现异常登录设备，请立即移除并修改密码。"
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Table
                dataSource={devices}
                columns={deviceColumns}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </TabPane>

          <TabPane tab="操作日志" key="logs">
            <Card title="操作日志">
              <Table
                dataSource={logs}
                columns={logColumns}
                rowKey="id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            </Card>
          </TabPane>
        </Tabs>

        {/* 修改密码模态框 */}
        <Modal
          title="修改密码"
          open={passwordModalVisible}
          onOk={handleChangePassword}
          onCancel={() => {
            setPasswordModalVisible(false);
            passwordForm.resetFields();
          }}
          confirmLoading={loading}
        >
          <Form form={passwordForm} layout="vertical">
            <Form.Item
              name="oldPassword"
              label="当前密码"
              rules={[{ required: true, message: '请输入当前密码' }]}
            >
              <Input.Password placeholder="请输入当前密码" />
            </Form.Item>
            <Form.Item
              name="newPassword"
              label="新密码"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 8, message: '密码长度至少8位' },
                { 
                  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                  message: '密码必须包含大小写字母、数字和特殊字符'
                },
              ]}
            >
              <Input.Password placeholder="请输入新密码" />
            </Form.Item>
            <Form.Item
              name="confirmPassword"
              label="确认密码"
              rules={[{ required: true, message: '请确认新密码' }]}
            >
              <Input.Password placeholder="请再次输入新密码" />
            </Form.Item>
          </Form>
        </Modal>

        {/* 绑定手机号模态框 */}
        <Modal
          title="绑定手机号"
          open={phoneModalVisible}
          onOk={handleBindPhone}
          onCancel={() => {
            setPhoneModalVisible(false);
            phoneForm.resetFields();
          }}
          confirmLoading={loading}
        >
          <Form form={phoneForm} layout="vertical">
            <Form.Item
              name="phone"
              label="手机号码"
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
              ]}
            >
              <Input placeholder="请输入手机号码" />
            </Form.Item>
            <Form.Item
              name="code"
              label="验证码"
              rules={[{ required: true, message: '请输入验证码' }]}
            >
              <Row gutter={8}>
                <Col span={16}>
                  <Input placeholder="请输入验证码" />
                </Col>
                <Col span={8}>
                  <Button onClick={() => handleSendCode('phone')}>
                    发送验证码
                  </Button>
                </Col>
              </Row>
            </Form.Item>
          </Form>
        </Modal>

        {/* 绑定邮箱模态框 */}
        <Modal
          title="绑定邮箱"
          open={emailModalVisible}
          onOk={handleBindEmail}
          onCancel={() => {
            setEmailModalVisible(false);
            emailForm.resetFields();
          }}
          confirmLoading={loading}
        >
          <Form form={emailForm} layout="vertical">
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入正确的邮箱格式' },
              ]}
            >
              <Input placeholder="请输入邮箱地址" />
            </Form.Item>
            <Form.Item
              name="code"
              label="验证码"
              rules={[{ required: true, message: '请输入验证码' }]}
            >
              <Row gutter={8}>
                <Col span={16}>
                  <Input placeholder="请输入验证码" />
                </Col>
                <Col span={8}>
                  <Button onClick={() => handleSendCode('email')}>
                    发送验证码
                  </Button>
                </Col>
              </Row>
            </Form.Item>
          </Form>
        </Modal>
    </div>
  );
};

export default Security;
