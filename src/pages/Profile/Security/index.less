.security {
  .securityTabs {
    .ant-tabs-content-holder {
      padding-top: 16px;
    }
  }

  .passwordInfo,
  .mfaInfo {
    .infoItem {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;

      .infoIcon {
        font-size: 20px;
        color: #1890ff;
      }

      .infoTitle {
        font-weight: 500;
        margin-bottom: 4px;
      }
    }
  }

  .bindingTitle {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .deviceInfo {
    .deviceName {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .deviceDetails {
      font-size: 12px;
      color: #666;
    }
  }

  .loginInfo {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;
    }
  }

  .actionInfo {
    .actionName {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;

      .anticon {
        font-size: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .security {
    .passwordInfo,
    .mfaInfo {
      .infoItem {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .deviceInfo,
    .loginInfo {
      font-size: 11px;
    }
  }
}
