import React, { useState } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Image,
  Typography,
  Tooltip,
  Modal,
  message,
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  HeartOutlined,
  MessageOutlined,
  ShareAltOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { MyContent } from '@/types/profile';
import styles from './index.less';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

// 模拟内容数据
const mockContent: MyContent[] = [
  {
    id: '1',
    title: '2024年产品运营策略深度分析与实践指南',
    type: 'article',
    status: 'published',
    publishTime: '2024-01-15 10:30:00',
    createTime: '2024-01-14 16:20:00',
    updateTime: '2024-01-15 10:30:00',
    viewCount: 1256,
    likeCount: 89,
    commentCount: 23,
    shareCount: 45,
    cover: 'https://picsum.photos/300/200?random=1',
    tags: ['产品运营', '策略分析', '实践指南'],
    category: '运营',
  },
  {
    id: '2',
    title: '用户增长实战案例分析',
    type: 'video',
    status: 'published',
    publishTime: '2024-01-14 14:20:00',
    createTime: '2024-01-13 11:15:00',
    updateTime: '2024-01-14 14:20:00',
    viewCount: 2345,
    likeCount: 156,
    commentCount: 67,
    shareCount: 89,
    cover: 'https://picsum.photos/300/200?random=2',
    tags: ['用户增长', '案例分析'],
    category: '增长',
  },
  {
    id: '3',
    title: '数据驱动的产品决策方法论',
    type: 'article',
    status: 'draft',
    createTime: '2024-01-13 09:30:00',
    updateTime: '2024-01-15 11:45:00',
    viewCount: 0,
    likeCount: 0,
    commentCount: 0,
    shareCount: 0,
    cover: 'https://picsum.photos/300/200?random=3',
    tags: ['数据分析', '产品决策'],
    category: '产品',
  },
  {
    id: '4',
    title: 'B端产品设计思维与方法',
    type: 'video',
    status: 'pending',
    createTime: '2024-01-12 15:20:00',
    updateTime: '2024-01-12 15:20:00',
    viewCount: 0,
    likeCount: 0,
    commentCount: 0,
    shareCount: 0,
    cover: 'https://picsum.photos/300/200?random=4',
    tags: ['B端产品', '设计思维'],
    category: '设计',
  },
];

const MyContent: React.FC = () => {
  const [content] = useState<MyContent[]>(mockContent);
  const [filteredContent, setFilteredContent] = useState<MyContent[]>(mockContent);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 内容状态配置
  const statusConfig = {
    draft: { color: 'default', text: '草稿' },
    pending: { color: 'processing', text: '审核中' },
    published: { color: 'success', text: '已发布' },
    rejected: { color: 'error', text: '已拒绝' },
    archived: { color: 'warning', text: '已归档' },
  };

  // 内容类型配置
  const typeConfig = {
    article: { color: 'blue', text: '图文', icon: <FileTextOutlined /> },
    video: { color: 'red', text: '视频', icon: <FileTextOutlined /> },
    image: { color: 'green', text: '图片', icon: <FileTextOutlined /> },
  };

  // 表格列配置
  const columns: ColumnsType<MyContent> = [
    {
      title: '内容信息',
      key: 'content',
      width: 300,
      render: (_, record) => (
        <div className={styles.contentInfo}>
          <div className={styles.contentHeader}>
            <Image
              src={record.cover}
              alt={record.title}
              width={60}
              height={40}
              style={{ borderRadius: 4, objectFit: 'cover' }}
              preview={false}
            />
            <div className={styles.contentDetails}>
              <Tooltip title={record.title}>
                <div className={styles.contentTitle}>{record.title}</div>
              </Tooltip>
              <div className={styles.contentMeta}>
                <Tag color={typeConfig[record.type].color} size="small">
                  {typeConfig[record.type].text}
                </Tag>
                <Text type="secondary">{record.category}</Text>
              </div>
            </div>
          </div>
          <div className={styles.contentTags}>
            {record.tags.map(tag => (
              <Tag key={tag} size="small">{tag}</Tag>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusConfig) => (
        <Tag color={statusConfig[status].color}>{statusConfig[status].text}</Tag>
      ),
      filters: Object.entries(statusConfig).map(([key, config]) => ({
        text: config.text,
        value: key,
      })),
    },
    {
      title: '数据统计',
      key: 'stats',
      width: 200,
      render: (_, record) => (
        <div className={styles.contentStats}>
          <Space size="small">
            <span><EyeOutlined /> {record.viewCount}</span>
            <span><HeartOutlined /> {record.likeCount}</span>
            <span><MessageOutlined /> {record.commentCount}</span>
            <span><ShareAltOutlined /> {record.shareCount}</span>
          </Space>
        </div>
      ),
    },
    {
      title: '时间',
      key: 'time',
      width: 150,
      render: (_, record) => (
        <div className={styles.timeInfo}>
          <div>创建：{record.createTime}</div>
          {record.publishTime && (
            <div>发布：{record.publishTime}</div>
          )}
          <div>更新：{record.updateTime}</div>
        </div>
      ),
      sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" icon={<EyeOutlined />}>
            查看
          </Button>
          <Button type="link" size="small" icon={<EditOutlined />}>
            编辑
          </Button>
          <Button 
            type="link" 
            size="small" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 删除内容
  const handleDelete = (contentId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个内容吗？',
      onOk: () => {
        console.log('删除内容:', contentId);
        message.success('删除成功');
      },
    });
  };

  // 批量操作
  const handleBatchOperation = (operation: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要操作的内容');
      return;
    }

    console.log('批量操作:', operation, selectedRowKeys);
    message.success(`批量${operation}成功`);
    setSelectedRowKeys([]);
  };

  // 搜索筛选
  const handleSearch = (value: string) => {
    const filtered = content.filter(item =>
      item.title.toLowerCase().includes(value.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(value.toLowerCase()))
    );
    setFilteredContent(filtered);
  };

  // 状态筛选
  const handleStatusFilter = (status: string) => {
    if (status === 'all') {
      setFilteredContent(content);
    } else {
      const filtered = content.filter(item => item.status === status);
      setFilteredContent(filtered);
    }
  };

  return (
    <div className={styles.myContent}>
        {/* 筛选器 */}
        <Card className={styles.filterCard}>
          <Space size="middle" wrap>
            <Search
              placeholder="搜索内容标题或标签"
              allowClear
              style={{ width: 250 }}
              onSearch={handleSearch}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="内容状态"
              style={{ width: 120 }}
              onChange={handleStatusFilter}
              defaultValue="all"
            >
              <Option value="all">全部状态</Option>
              {Object.entries(statusConfig).map(([key, config]) => (
                <Option key={key} value={key}>{config.text}</Option>
              ))}
            </Select>
            <Select
              placeholder="内容类型"
              style={{ width: 120 }}
            >
              <Option value="all">全部类型</Option>
              {Object.entries(typeConfig).map(([key, config]) => (
                <Option key={key} value={key}>{config.text}</Option>
              ))}
            </Select>
            <RangePicker placeholder={['开始时间', '结束时间']} />
            <Button type="primary">
              创建内容
            </Button>
          </Space>
        </Card>

        {/* 内容列表 */}
        <Card 
          title="我的内容"
          className={styles.contentCard}
          extra={
            selectedRowKeys.length > 0 && (
              <Space>
                <span>已选择 {selectedRowKeys.length} 项</span>
                <Button size="small" onClick={() => handleBatchOperation('发布')}>
                  批量发布
                </Button>
                <Button size="small" onClick={() => handleBatchOperation('归档')}>
                  批量归档
                </Button>
                <Button size="small" danger onClick={() => handleBatchOperation('删除')}>
                  批量删除
                </Button>
              </Space>
            )
          }
        >
          <Table
            dataSource={filteredContent}
            columns={columns}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
            }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
          />
        </Card>
    </div>
  );
};

export default MyContent;
