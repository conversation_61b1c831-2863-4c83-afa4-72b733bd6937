.myContent {
  .filterCard {
    margin-bottom: 16px;
  }

  .contentCard {
    .ant-table-thead > tr > th {
      background: #fafafa;
    }
  }

  .contentInfo {
    .contentHeader {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 8px;

      .contentDetails {
        flex: 1;
        min-width: 0;

        .contentTitle {
          font-weight: 500;
          margin-bottom: 4px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.4;
        }

        .contentMeta {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .contentTags {
      .ant-tag {
        margin-bottom: 4px;
      }
    }
  }

  .contentStats {
    font-size: 12px;
    color: #666;

    .anticon {
      margin-right: 4px;
    }

    span {
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .timeInfo {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .myContent {
    .filterCard {
      .ant-space {
        width: 100%;
        flex-direction: column;
        align-items: stretch;

        .ant-input-affix-wrapper,
        .ant-select,
        .ant-picker {
          width: 100% !important;
          margin-bottom: 8px;
        }
      }
    }

    .contentInfo {
      .contentHeader {
        gap: 8px;

        .contentDetails {
          .contentTitle {
            font-size: 13px;
            -webkit-line-clamp: 1;
          }

          .contentMeta {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }
      }
    }

    .contentStats {
      font-size: 11px;

      span {
        margin-right: 8px;
      }
    }

    .timeInfo {
      font-size: 11px;
    }
  }
}

@media (max-width: 576px) {
  .myContent {
    .contentInfo {
      .contentHeader {
        flex-direction: column;
        align-items: flex-start;

        .contentDetails {
          width: 100%;
        }
      }
    }

    .contentStats {
      span {
        display: block;
        margin-bottom: 2px;
        margin-right: 0;
      }
    }
  }
}
