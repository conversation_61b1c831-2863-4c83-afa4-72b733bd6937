import React, { useState } from 'react';
import {
  Card,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Badge,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  Input,
  Select,
  message,
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  MessageOutlined,
  CalendarOutlined,
  TrophyOutlined,
  PlusOutlined,
  MailOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import type { TeamMember } from '@/types/profile';
import ProfileLayout from '../components/ProfileLayout';
import styles from './index.less';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 模拟团队成员数据
const mockTeamMembers: TeamMember[] = [
  {
    id: '1',
    name: '李四',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisi',
    position: '产品总监',
    department: '产品技术部',
    email: '<EMAIL>',
    phone: '138****1234',
    role: 'manager',
    status: 'online',
    joinTime: '2021-03-15',
    skills: ['产品规划', '团队管理', '战略分析'],
  },
  {
    id: '2',
    name: '王五',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wangwu',
    position: '高级UI设计师',
    department: '产品技术部',
    email: '<EMAIL>',
    phone: '139****5678',
    role: 'member',
    status: 'online',
    joinTime: '2022-06-20',
    skills: ['UI设计', '交互设计', 'Figma'],
  },
  {
    id: '3',
    name: '赵六',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhaoliu',
    position: '前端开发工程师',
    department: '产品技术部',
    email: '<EMAIL>',
    phone: '137****9012',
    role: 'member',
    status: 'busy',
    joinTime: '2022-09-10',
    skills: ['React', 'TypeScript', 'Node.js'],
  },
  {
    id: '4',
    name: '孙七',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sunqi',
    position: '产品实习生',
    department: '产品技术部',
    email: '<EMAIL>',
    role: 'intern',
    status: 'away',
    joinTime: '2023-12-01',
    skills: ['产品分析', '用户研究'],
  },
];

const Team: React.FC = () => {
  const [form] = Form.useForm();
  const [teamMembers] = useState<TeamMember[]>(mockTeamMembers);
  const [collaborateModalVisible, setCollaborateModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // 状态配置
  const statusConfig = {
    online: { color: 'success', text: '在线' },
    offline: { color: 'default', text: '离线' },
    busy: { color: 'error', text: '忙碌' },
    away: { color: 'warning', text: '离开' },
  };

  // 角色配置
  const roleConfig = {
    manager: { color: 'red', text: '管理者' },
    member: { color: 'blue', text: '成员' },
    intern: { color: 'green', text: '实习生' },
  };

  // 发起协作
  const handleCollaborate = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      console.log('发起协作:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('协作邀请已发送');
      setCollaborateModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('请完善协作信息');
    } finally {
      setLoading(false);
    }
  };

  // 联系成员
  const handleContact = (member: TeamMember, type: 'email' | 'phone') => {
    if (type === 'email') {
      window.open(`mailto:${member.email}`);
    } else {
      message.info(`联系电话：${member.phone}`);
    }
  };

  return (
    <ProfileLayout>
      <div className={styles.team}>
        {/* 团队概览 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="团队成员"
                value={teamMembers.length}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="在线成员"
                value={teamMembers.filter(m => m.status === 'online').length}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="进行中项目"
                value={8}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="团队表现"
                value={92.5}
                suffix="%"
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 团队成员列表 */}
        <Card 
          title="团队成员"
          className={styles.memberCard}
          extra={
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setCollaborateModalVisible(true)}
            >
              发起协作
            </Button>
          }
        >
          <List
            grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 4, xxl: 4 }}
            dataSource={teamMembers}
            renderItem={(member) => (
              <List.Item>
                <Card
                  hoverable
                  className={styles.memberCard}
                  actions={[
                    <Button 
                      key="email"
                      type="text" 
                      icon={<MailOutlined />}
                      onClick={() => handleContact(member, 'email')}
                    >
                      邮件
                    </Button>,
                    <Button 
                      key="phone"
                      type="text" 
                      icon={<PhoneOutlined />}
                      onClick={() => handleContact(member, 'phone')}
                    >
                      电话
                    </Button>,
                    <Button 
                      key="message"
                      type="text" 
                      icon={<MessageOutlined />}
                    >
                      消息
                    </Button>,
                  ]}
                >
                  <div className={styles.memberInfo}>
                    <div className={styles.memberHeader}>
                      <Badge
                        status={statusConfig[member.status].color as any}
                        offset={[-8, 8]}
                      >
                        <Avatar
                          size={64}
                          src={member.avatar}
                          icon={<UserOutlined />}
                        />
                      </Badge>
                    </div>
                    
                    <div className={styles.memberDetails}>
                      <Title level={5} style={{ margin: '8px 0 4px' }}>
                        {member.name}
                      </Title>
                      <Text type="secondary" className={styles.position}>
                        {member.position}
                      </Text>
                      
                      <div className={styles.memberMeta}>
                        <Tag color={roleConfig[member.role].color} size="small">
                          {roleConfig[member.role].text}
                        </Tag>
                        <Tag color={statusConfig[member.status].color} size="small">
                          {statusConfig[member.status].text}
                        </Tag>
                      </div>
                      
                      <div className={styles.memberSkills}>
                        {member.skills.map(skill => (
                          <Tag key={skill} size="small" className={styles.skillTag}>
                            {skill}
                          </Tag>
                        ))}
                      </div>
                      
                      <div className={styles.joinTime}>
                        <Text type="secondary">
                          入职时间：{member.joinTime}
                        </Text>
                      </div>
                    </div>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </Card>

        {/* 团队项目 */}
        <Card title="团队项目" className={styles.projectCard}>
          <List
            dataSource={[
              {
                id: '1',
                name: '产品运营平台v2.0',
                status: 'in_progress',
                progress: 75,
                members: ['张三', '李四', '王五'],
                deadline: '2024-02-15',
              },
              {
                id: '2',
                name: '用户增长分析系统',
                status: 'in_progress',
                progress: 45,
                members: ['张三', '赵六'],
                deadline: '2024-03-01',
              },
              {
                id: '3',
                name: '内容管理优化',
                status: 'completed',
                progress: 100,
                members: ['王五', '孙七'],
                deadline: '2024-01-10',
              },
            ]}
            renderItem={(project) => (
              <List.Item
                actions={[
                  <Button key="view" type="link">查看详情</Button>,
                ]}
              >
                <List.Item.Meta
                  title={
                    <div className={styles.projectTitle}>
                      <span>{project.name}</span>
                      <Tag color={project.status === 'completed' ? 'success' : 'processing'}>
                        {project.status === 'completed' ? '已完成' : '进行中'}
                      </Tag>
                    </div>
                  }
                  description={
                    <div className={styles.projectDesc}>
                      <div>进度：{project.progress}%</div>
                      <div>成员：{project.members.join('、')}</div>
                      <div>截止时间：{project.deadline}</div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>

        {/* 发起协作模态框 */}
        <Modal
          title="发起协作"
          open={collaborateModalVisible}
          onOk={handleCollaborate}
          onCancel={() => {
            setCollaborateModalVisible(false);
            form.resetFields();
          }}
          confirmLoading={loading}
          width={600}
        >
          <Form form={form} layout="vertical">
            <Form.Item
              name="members"
              label="协作成员"
              rules={[{ required: true, message: '请选择协作成员' }]}
            >
              <Select
                mode="multiple"
                placeholder="选择协作成员"
                style={{ width: '100%' }}
              >
                {teamMembers.map(member => (
                  <Option key={member.id} value={member.id}>
                    <Space>
                      <Avatar src={member.avatar} size="small" />
                      {member.name} - {member.position}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="type"
              label="协作类型"
              rules={[{ required: true, message: '请选择协作类型' }]}
            >
              <Select placeholder="选择协作类型">
                <Option value="meeting">会议讨论</Option>
                <Option value="project">项目协作</Option>
                <Option value="review">代码评审</Option>
                <Option value="brainstorm">头脑风暴</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="title"
              label="协作主题"
              rules={[{ required: true, message: '请输入协作主题' }]}
            >
              <Input placeholder="请输入协作主题" />
            </Form.Item>
            
            <Form.Item
              name="content"
              label="协作内容"
              rules={[{ required: true, message: '请输入协作内容' }]}
            >
              <TextArea
                rows={4}
                placeholder="请详细描述协作内容和目标..."
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProfileLayout>
  );
};

export default Team;
