.container {
  padding: 24px;

  .ant-card {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-form {
    max-width: 500px;
  }
}

.profilePage {
  background-color: #f6f8fa;
  min-height: 100vh;
}

.profileLayout {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  gap: 24px;
}

.profileSidebar {
  width: 296px;
  flex-shrink: 0;
}

.profileContent {
  flex: 1;
  min-width: 0;
}

.profileCard {
  text-align: center;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 10%);

  .avatarWrapper {
    margin: -1px -1px 0;

    .avatar {
      width: 100%;
      border-radius: 50%;
      border: 1px solid #e1e4e8;
    }
  }

  .userInfo {
    padding: 16px;

    h3 {
      margin: 0 0 8px;
      font-size: 24px;
      font-weight: 600;
    }

    .userRole {
      color: #586069;
      margin-bottom: 8px;
    }

    .userMeta {
      color: #586069;
      font-size: 14px;
    }
  }
}

.profileTabs {
  display: flex;
  border-bottom: 1px solid #e1e4e8;
  margin-bottom: 16px;

  .tabItem {
    padding: 8px 16px;
    font-size: 14px;
    color: #586069;
    cursor: pointer;
    margin-right: 16px;

    &:hover {
      color: #24292e;
    }

    &.active {
      font-weight: 600;
      color: #24292e;
      border-bottom: 2px solid #f9826c;
    }
  }
}

.infoCard {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 10%);
  margin-bottom: 24px;

  .ant-card-head {
    border-bottom: 1px solid #e1e4e8;
  }

  .ant-card-body {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .profileLayout {
    flex-direction: column;
  }

  .profileSidebar {
    width: 100%;
  }
}
