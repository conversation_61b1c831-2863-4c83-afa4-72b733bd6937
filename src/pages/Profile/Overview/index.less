.overview {
  .statsRow {
    margin-bottom: 16px;

    .growth {
      font-size: 12px;
      margin-left: 8px;
    }
  }

  .trendCard,
  .todoCard,
  .contentCard,
  .progressCard,
  .achievementCard,
  .quickActions {
    margin-bottom: 16px;
  }

  .todoTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .todoDesc {
    .todoMeta {
      margin-top: 8px;
      font-size: 12px;
    }
  }

  .contentTitle {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-size: 14px;
    font-weight: 500;
  }

  .contentMeta {
    font-size: 12px;
    color: #666;

    .anticon {
      margin-right: 4px;
    }
  }

  .progressCard {
    .progressItem {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .progressHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
      }
    }
  }

  .achievementTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .achievementDesc {
    .ant-progress {
      margin-top: 8px;
    }
  }

  .quickActions {
    .ant-btn {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overview {
    .todoTitle {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .contentTitle {
      font-size: 13px;
    }

    .contentMeta {
      font-size: 11px;
    }

    .achievementTitle {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}

@media (max-width: 576px) {
  .overview {
    .statsRow {
      .ant-col {
        margin-bottom: 8px;
      }
    }

    .progressCard {
      .progressItem {
        .progressHeader {
          font-size: 12px;
        }
      }
    }
  }
}
