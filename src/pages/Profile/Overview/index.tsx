import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Badge,
  Calendar,
  Timeline,
  Empty,
  Tooltip,
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  HeartOutlined,
  MessageOutlined,
  UserOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  RiseOutlined,
  TeamOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { Line, Column } from '@ant-design/plots';
import type { PersonalStats, TodoItem, Achievement, MyContent } from '@/types/profile';
import ProfileLayout from '../components/ProfileLayout';
import styles from './index.less';

const { Title, Text } = Typography;

// 模拟数据
const mockStats: PersonalStats = {
  contentStats: {
    totalContent: 156,
    publishedContent: 142,
    draftContent: 14,
    totalViews: 125680,
    totalLikes: 8945,
    totalComments: 2156,
    monthlyGrowth: 15.6,
  },
  customerStats: {
    totalCustomers: 89,
    activeCustomers: 67,
    newCustomers: 12,
    conversionRate: 23.5,
    totalValue: 1256000,
    monthlyValue: 156000,
  },
  taskStats: {
    totalTasks: 245,
    completedTasks: 198,
    pendingTasks: 32,
    overdueTask: 15,
    completionRate: 80.8,
    avgCompletionTime: 2.5,
  },
  teamStats: {
    teamSize: 12,
    activeProjects: 8,
    completedProjects: 25,
    teamPerformance: 92.5,
  },
};

const mockTodos: TodoItem[] = [
  {
    id: '1',
    title: '审核新用户发布的视频内容',
    description: '需要审核3个新用户发布的短视频内容',
    type: 'content_review',
    priority: 'high',
    status: 'pending',
    assignee: 'current_user',
    assigneeName: '张三',
    dueDate: '2024-01-16 18:00:00',
    createTime: '2024-01-15 14:30:00',
    updateTime: '2024-01-15 14:30:00',
    relatedId: 'content_001',
    relatedType: 'content',
    relatedTitle: '用户内容审核',
  },
  {
    id: '2',
    title: '跟进重点客户合作进展',
    description: '联系ABC公司确认合作方案细节',
    type: 'customer_follow',
    priority: 'urgent',
    status: 'pending',
    assignee: 'current_user',
    assigneeName: '张三',
    dueDate: '2024-01-16 10:00:00',
    createTime: '2024-01-15 09:15:00',
    updateTime: '2024-01-15 09:15:00',
    relatedId: 'customer_001',
    relatedType: 'customer',
    relatedTitle: 'ABC公司合作项目',
  },
];

const mockAchievements: Achievement[] = [
  {
    id: '1',
    name: '内容达人',
    description: '发布内容超过100篇',
    icon: '🏆',
    type: 'content',
    level: 'gold',
    progress: 156,
    maxProgress: 100,
    unlocked: true,
    unlockedTime: '2024-01-10 15:30:00',
    reward: { type: 'badge', value: 'content_master' },
  },
  {
    id: '2',
    name: '客户专家',
    description: '成功转化客户超过50个',
    icon: '💼',
    type: 'customer',
    level: 'silver',
    progress: 35,
    maxProgress: 50,
    unlocked: false,
  },
];

const mockRecentContent: MyContent[] = [
  {
    id: '1',
    title: '2024年产品运营策略分享',
    type: 'article',
    status: 'published',
    publishTime: '2024-01-15 10:30:00',
    createTime: '2024-01-14 16:20:00',
    updateTime: '2024-01-15 10:30:00',
    viewCount: 1256,
    likeCount: 89,
    commentCount: 23,
    shareCount: 45,
    cover: 'https://picsum.photos/300/200?random=1',
    tags: ['产品运营', '策略分享'],
    category: '运营',
  },
  {
    id: '2',
    title: '用户增长实战案例分析',
    type: 'video',
    status: 'published',
    publishTime: '2024-01-14 14:20:00',
    createTime: '2024-01-13 11:15:00',
    updateTime: '2024-01-14 14:20:00',
    viewCount: 2345,
    likeCount: 156,
    commentCount: 67,
    shareCount: 89,
    cover: 'https://picsum.photos/300/200?random=2',
    tags: ['用户增长', '案例分析'],
    category: '增长',
  },
];

const ProfileOverview: React.FC = () => {
  const [stats] = useState<PersonalStats>(mockStats);
  const [todos] = useState<TodoItem[]>(mockTodos);
  const [achievements] = useState<Achievement[]>(mockAchievements);
  const [recentContent] = useState<MyContent[]>(mockRecentContent);

  // 趋势数据
  const trendData = [
    { date: '2024-01-08', views: 1200, likes: 89 },
    { date: '2024-01-09', views: 1350, likes: 95 },
    { date: '2024-01-10', views: 1180, likes: 78 },
    { date: '2024-01-11', views: 1420, likes: 102 },
    { date: '2024-01-12', views: 1680, likes: 125 },
    { date: '2024-01-13', views: 1890, likes: 142 },
    { date: '2024-01-14', views: 2100, likes: 156 },
  ];

  // 优先级配置
  const priorityConfig = {
    low: { color: 'default', text: '低' },
    normal: { color: 'blue', text: '普通' },
    high: { color: 'orange', text: '高' },
    urgent: { color: 'red', text: '紧急' },
  };

  // 任务类型配置
  const taskTypeConfig = {
    content_review: { color: 'blue', text: '内容审核', icon: <FileTextOutlined /> },
    customer_follow: { color: 'green', text: '客户跟进', icon: <UserOutlined /> },
    activity_task: { color: 'purple', text: '活动任务', icon: <CalendarOutlined /> },
    approval: { color: 'orange', text: '审批流程', icon: <CheckCircleOutlined /> },
    other: { color: 'default', text: '其他', icon: <ExclamationCircleOutlined /> },
  };

  // 成就等级配置
  const levelConfig = {
    bronze: { color: '#CD7F32', text: '青铜' },
    silver: { color: '#C0C0C0', text: '白银' },
    gold: { color: '#FFD700', text: '黄金' },
    platinum: { color: '#E5E4E2', text: '铂金' },
  };

  // 趋势图配置
  const trendConfig = {
    data: trendData,
    xField: 'date',
    yField: 'views',
    smooth: true,
    color: '#1890ff',
    point: { size: 4, shape: 'circle' },
  };

  return (
    <ProfileLayout>
      <div className={styles.overview}>
        {/* 核心数据统计 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总内容数"
                value={stats.contentStats.totalContent}
                prefix={<FileTextOutlined />}
                suffix={
                  <span className={styles.growth}>
                    <RiseOutlined style={{ color: '#3f8600' }} />
                    {stats.contentStats.monthlyGrowth}%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="总浏览量"
                value={stats.contentStats.totalViews}
                prefix={<EyeOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="客户数量"
                value={stats.customerStats.totalCustomers}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="任务完成率"
                value={stats.taskStats.completionRate}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {/* 左侧内容 */}
          <Col xs={24} lg={16}>
            {/* 数据趋势 */}
            <Card title="内容数据趋势" className={styles.trendCard}>
              <Line {...trendConfig} height={250} />
            </Card>

            {/* 我的待办 */}
            <Card 
              title="我的待办" 
              className={styles.todoCard}
              extra={
                <Button type="link" size="small">
                  查看全部
                </Button>
              }
            >
              {todos.length > 0 ? (
                <List
                  dataSource={todos}
                  renderItem={(item) => (
                    <List.Item
                      key={item.id}
                      actions={[
                        <Button type="link" size="small">
                          处理
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            icon={taskTypeConfig[item.type].icon} 
                            style={{ backgroundColor: taskTypeConfig[item.type].color }}
                          />
                        }
                        title={
                          <div className={styles.todoTitle}>
                            <span>{item.title}</span>
                            <Space size="small">
                              <Tag color={priorityConfig[item.priority].color}>
                                {priorityConfig[item.priority].text}
                              </Tag>
                              <Tag color={taskTypeConfig[item.type].color}>
                                {taskTypeConfig[item.type].text}
                              </Tag>
                            </Space>
                          </div>
                        }
                        description={
                          <div className={styles.todoDesc}>
                            <Text type="secondary">{item.description}</Text>
                            <div className={styles.todoMeta}>
                              <Space size="small">
                                <ClockCircleOutlined />
                                <Text type="secondary">截止：{item.dueDate}</Text>
                              </Space>
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty description="暂无待办事项" />
              )}
            </Card>

            {/* 最近内容 */}
            <Card 
              title="最近发布内容" 
              className={styles.contentCard}
              extra={
                <Button type="link" size="small">
                  查看全部
                </Button>
              }
            >
              <List
                grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
                dataSource={recentContent}
                renderItem={(item) => (
                  <List.Item>
                    <Card
                      hoverable
                      cover={
                        <img
                          alt={item.title}
                          src={item.cover}
                          style={{ height: 120, objectFit: 'cover' }}
                        />
                      }
                      size="small"
                    >
                      <Card.Meta
                        title={
                          <Tooltip title={item.title}>
                            <div className={styles.contentTitle}>
                              {item.title}
                            </div>
                          </Tooltip>
                        }
                        description={
                          <div className={styles.contentMeta}>
                            <Space size="small">
                              <span><EyeOutlined /> {item.viewCount}</span>
                              <span><HeartOutlined /> {item.likeCount}</span>
                              <span><MessageOutlined /> {item.commentCount}</span>
                            </Space>
                          </div>
                        }
                      />
                    </Card>
                  </List.Item>
                )}
              />
            </Card>
          </Col>

          {/* 右侧内容 */}
          <Col xs={24} lg={8}>
            {/* 任务进度 */}
            <Card title="任务进度" className={styles.progressCard}>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>已完成任务</span>
                  <span>{stats.taskStats.completedTasks}/{stats.taskStats.totalTasks}</span>
                </div>
                <Progress 
                  percent={stats.taskStats.completionRate} 
                  strokeColor="#52c41a"
                />
              </div>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>客户转化率</span>
                  <span>{stats.customerStats.conversionRate}%</span>
                </div>
                <Progress 
                  percent={stats.customerStats.conversionRate} 
                  strokeColor="#1890ff"
                />
              </div>
              <div className={styles.progressItem}>
                <div className={styles.progressHeader}>
                  <span>团队表现</span>
                  <span>{stats.teamStats.teamPerformance}%</span>
                </div>
                <Progress 
                  percent={stats.teamStats.teamPerformance} 
                  strokeColor="#722ed1"
                />
              </div>
            </Card>

            {/* 个人成就 */}
            <Card 
              title="个人成就" 
              className={styles.achievementCard}
              extra={
                <Button type="link" size="small">
                  查看全部
                </Button>
              }
            >
              <List
                dataSource={achievements}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Badge dot={item.unlocked} status={item.unlocked ? 'success' : 'default'}>
                          <Avatar 
                            style={{ 
                              backgroundColor: item.unlocked ? levelConfig[item.level].color : '#f0f0f0',
                              color: item.unlocked ? '#fff' : '#999'
                            }}
                          >
                            {item.icon}
                          </Avatar>
                        </Badge>
                      }
                      title={
                        <div className={styles.achievementTitle}>
                          <span>{item.name}</span>
                          <Tag color={levelConfig[item.level].color}>
                            {levelConfig[item.level].text}
                          </Tag>
                        </div>
                      }
                      description={
                        <div className={styles.achievementDesc}>
                          <Text type="secondary">{item.description}</Text>
                          <Progress 
                            percent={(item.progress / item.maxProgress) * 100}
                            size="small"
                            format={() => `${item.progress}/${item.maxProgress}`}
                          />
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>

            {/* 快速操作 */}
            <Card title="快速操作" className={styles.quickActions}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button block icon={<FileTextOutlined />}>
                  创建新内容
                </Button>
                <Button block icon={<UserOutlined />}>
                  添加客户
                </Button>
                <Button block icon={<TeamOutlined />}>
                  发起协作
                </Button>
                <Button block icon={<TrophyOutlined />}>
                  查看成就
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </ProfileLayout>
  );
};

export default ProfileOverview;
