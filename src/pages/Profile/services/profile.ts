import { request } from 'umi';

export async function changePassword(params: {
  currentPassword: string;
  newPassword: string;
}) {
  return request('/api/profile/change-password', {
    method: 'POST',
    data: params,
  });
}

export async function bindMFA(params: { type: string; token?: string }) {
  return request('/api/profile/bind-mfa', {
    method: 'POST',
    data: params,
  });
}

export async function getLoginDevices() {
  return request('/api/profile/login-devices');
}

export async function revokeDevice(deviceId: string) {
  return request(`/api/profile/revoke-device/${deviceId}`, {
    method: 'POST',
  });
}
