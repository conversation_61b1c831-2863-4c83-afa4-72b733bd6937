import frostChainService, {
  MonitoringDataItem,
  MonitoringStatistics,
} from '@/services/frostChain';
import { Column, Line, Pie } from '@ant-design/charts';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Col,
  DatePicker,
  Row,
  Select,
  Space,
  Spin,
  Statistic,
  Table,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;
const { Text } = Typography;
const { Option } = Select;

// 事件类型颜色映射
const eventTypeColors: Record<string, string> = {
  click: 'blue',
  pageView: 'green',
  error: 'red',
  api: 'orange',
  performance: 'purple',
  custom: 'cyan',
};

const MonitoringReport: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [monitoringData, setMonitoringData] = useState<MonitoringDataItem[]>(
    [],
  );
  const [statistics, setStatistics] = useState<MonitoringStatistics | null>(
    null,
  );
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);
  const [eventType, setEventType] = useState<string | undefined>(undefined);
  const [deviceType, setDeviceType] = useState<string | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 加载统计数据
      const statsResponse = await frostChainService.getMonitoringStatistics({
        startTime: dateRange[0].valueOf(),
        endTime: dateRange[1].valueOf(),
        eventType,
        deviceType,
      });

      if (statsResponse.success && statsResponse.data) {
        setStatistics(statsResponse.data);
      }

      // 加载详细数据
      const dataResponse = await frostChainService.getMonitoringData({
        current: pagination.current,
        pageSize: pagination.pageSize,
        startTime: dateRange[0].valueOf(),
        endTime: dateRange[1].valueOf(),
        eventType,
        deviceType,
      });

      if (dataResponse.success && dataResponse.data) {
        setMonitoringData(dataResponse.data.list);
        setPagination({
          ...pagination,
          total: dataResponse.data.total,
        });
      }
    } catch (error) {
      console.error('Failed to load monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和筛选条件变化时重新加载数据
  useEffect(() => {
    loadData();
  }, [
    dateRange,
    eventType,
    deviceType,
    pagination.current,
    pagination.pageSize,
  ]);

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
      setPagination({ ...pagination, current: 1 });
    }
  };

  // 处理事件类型变化
  const handleEventTypeChange = (value: string | undefined) => {
    setEventType(value);
    setPagination({ ...pagination, current: 1 });
  };

  // 处理设备类型变化
  const handleDeviceTypeChange = (value: string | undefined) => {
    setDeviceType(value);
    setPagination({ ...pagination, current: 1 });
  };

  // 处理表格分页变化
  const handleTableChange = (pagination: any) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) =>
        dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      render: (type: string) => (
        <Tag color={eventTypeColors[type] || 'default'}>{type}</Tag>
      ),
    },
    {
      title: '事件名称',
      dataIndex: 'eventName',
      key: 'eventName',
    },
    {
      title: '设备类型',
      dataIndex: ['deviceInfo', 'deviceType'],
      key: 'deviceType',
    },
    {
      title: '浏览器',
      dataIndex: ['deviceInfo', 'browser'],
      key: 'browser',
    },
    {
      title: '页面',
      dataIndex: ['location', 'page'],
      key: 'page',
    },
    {
      title: '用户ID',
      dataIndex: ['userInfo', 'userId'],
      key: 'userId',
      render: (userId: string) => userId || '-',
    },
  ];

  return (
    <PageContainer
      header={{
        title: '埋点监控数据报表',
        subTitle: '基于 Frost-Chain 的数据分析',
      }}
    >
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Space>
              <Text strong>时间范围:</Text>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                allowClear={false}
              />
            </Space>
          </Col>
          <Col span={12}>
            <Space>
              <Text strong>事件类型:</Text>
              <Select
                style={{ width: 120 }}
                value={eventType}
                onChange={handleEventTypeChange}
                allowClear
                placeholder="全部"
              >
                <Option value="click">点击</Option>
                <Option value="pageView">页面浏览</Option>
                <Option value="error">错误</Option>
                <Option value="api">API</Option>
                <Option value="performance">性能</Option>
                <Option value="custom">自定义</Option>
              </Select>
              <Text strong>设备类型:</Text>
              <Select
                style={{ width: 120 }}
                value={deviceType}
                onChange={handleDeviceTypeChange}
                allowClear
                placeholder="全部"
              >
                <Option value="desktop">桌面</Option>
                <Option value="mobile">移动</Option>
                <Option value="tablet">平板</Option>
              </Select>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {statistics && (
          <>
            {/* 统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总事件数"
                    value={statistics.totalEvents}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="错误事件数"
                    value={statistics.errorCount}
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="页面浏览数"
                    value={statistics.eventTypeDistribution.pageView || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="点击事件数"
                    value={statistics.eventTypeDistribution.click || 0}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
            </Row>

            {/* 图表区域 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={12}>
                <Card title="事件类型分布">
                  <Pie
                    data={Object.entries(statistics.eventTypeDistribution).map(
                      ([type, count]) => ({
                        type,
                        value: count,
                      }),
                    )}
                    angleField="value"
                    colorField="type"
                    radius={0.8}
                    label={{
                      type: 'outer',
                      content: '{name} {percentage}',
                    }}
                    interactions={[
                      { type: 'pie-legend-active' },
                      { type: 'element-active' },
                    ]}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="热门页面">
                  <Column
                    data={statistics.topPages}
                    xField="page"
                    yField="count"
                    label={{
                      position: 'middle',
                      style: {
                        fill: '#FFFFFF',
                        opacity: 0.6,
                      },
                    }}
                    xAxis={{
                      label: {
                        autoHide: true,
                        autoRotate: false,
                      },
                    }}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={24}>
                <Card title="时间分布">
                  <Line
                    data={statistics.timeDistribution}
                    xField="hour"
                    yField="count"
                    point={{
                      size: 5,
                      shape: 'diamond',
                    }}
                    xAxis={{
                      title: {
                        text: '小时',
                      },
                    }}
                    yAxis={{
                      title: {
                        text: '事件数',
                      },
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </>
        )}

        {/* 详细数据表格 */}
        <Card title="详细数据">
          <Table
            columns={columns}
            dataSource={monitoringData}
            rowKey="id"
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            onChange={handleTableChange}
          />
        </Card>
      </Spin>
    </PageContainer>
  );
};

export default MonitoringReport;
