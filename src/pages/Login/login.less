.login-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 10%);
  margin-top: 100px;

  h2 {
    text-align: center;
    margin-bottom: 24px;
    color: #1890ff;
  }

  .form-group {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      transition: all 0.3s;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
        outline: none;
      }
    }
  }

  .login-button {
    width: 100%;
    padding: 12px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;

    &:hover {
      background-color: #40a9ff;
    }

    &:active {
      background-color: #096dd9;
    }
  }

  .auth-links {
    margin-top: 16px;
    text-align: center;
    font-size: 14px;

    a {
      color: #1890ff;
      cursor: pointer;
      text-decoration: none;
      transition: all 0.3s;

      &:hover {
        text-decoration: underline;
        color: #40a9ff;
      }
    }

    .divider {
      margin: 0 8px;
      color: #d9d9d9;
    }
  }
}

// 响应式设计
@media (max-width: 576px) {
  .login-container {
    margin-top: 40px;
    padding: 20px;
  }
}
