// 消息通知模块类型定义

// 消息类型枚举
export enum MessageType {
  SYSTEM = 'system',           // 系统消息
  COMMENT = 'comment',         // 评论消息
  LIKE = 'like',              // 点赞消息
  FOLLOW = 'follow',          // 关注消息
  PRIVATE = 'private',        // 私信消息
  SECURITY = 'security',      // 安全消息
  ACTIVITY = 'activity',      // 活动消息
  AUDIT = 'audit',           // 审核消息
  THIRD_PARTY = 'third_party', // 第三方平台消息
}

// 消息状态枚举
export enum MessageStatus {
  UNREAD = 'unread',         // 未读
  READ = 'read',             // 已读
  PROCESSED = 'processed',   // 已处理
  ARCHIVED = 'archived',     // 已归档
}

// 推送渠道枚举
export enum PushChannel {
  IN_APP = 'in_app',         // 站内信
  PUSH = 'push',             // App推送
  SMS = 'sms',               // 短信
  EMAIL = 'email',           // 邮件
  WEB_POPUP = 'web_popup',   // Web弹窗
}

// 第三方平台枚举
export enum ThirdPartyPlatform {
  LOCAL = 'local',           // 本地平台
  DOUYIN = 'douyin',         // 抖音
  XIAOHONGSHU = 'xiaohongshu', // 小红书
  WEIBO = 'weibo',           // 微博
  WECHAT = 'wechat',         // 微信
  BILIBILI = 'bilibili',     // B站
}

// 消息业务类型
export enum BusinessType {
  CONTENT_COMMENT = 'content_comment',     // 内容评论
  CONTENT_LIKE = 'content_like',           // 内容点赞
  CONTENT_SHARE = 'content_share',         // 内容分享
  CONTENT_COLLECT = 'content_collect',     // 内容收藏
  USER_FOLLOW = 'user_follow',             // 用户关注
  USER_MENTION = 'user_mention',           // 用户@
  PRIVATE_MESSAGE = 'private_message',     // 私信
  SYSTEM_NOTICE = 'system_notice',         // 系统公告
  AUDIT_RESULT = 'audit_result',           // 审核结果
  ACTIVITY_INVITE = 'activity_invite',     // 活动邀请
  SECURITY_ALERT = 'security_alert',       // 安全提醒
  PLATFORM_SYNC = 'platform_sync',         // 平台同步
}

// 消息优先级
export enum MessagePriority {
  LOW = 'low',               // 低优先级
  NORMAL = 'normal',         // 普通优先级
  HIGH = 'high',             // 高优先级
  URGENT = 'urgent',         // 紧急
}

// 消息数据接口
export interface Message {
  id: string;
  type: MessageType;
  businessType: BusinessType;
  platform: ThirdPartyPlatform;
  title: string;
  content: string;
  summary?: string;
  status: MessageStatus;
  priority: MessagePriority;
  senderId?: string;
  senderName?: string;
  senderAvatar?: string;
  receiverId: string;
  receiverName?: string;
  relatedId?: string;          // 关联内容ID
  relatedTitle?: string;       // 关联内容标题
  relatedUrl?: string;         // 关联链接
  externalUrl?: string;        // 外部平台链接
  channels: PushChannel[];     // 推送渠道
  metadata?: Record<string, any>; // 扩展数据
  createTime: string;
  readTime?: string;
  processTime?: string;
  expireTime?: string;
}

// 消息创建数据
export interface MessageCreateData {
  type: MessageType;
  businessType: BusinessType;
  platform?: ThirdPartyPlatform;
  title: string;
  content: string;
  summary?: string;
  priority?: MessagePriority;
  senderId?: string;
  receiverIds: string[];       // 支持批量发送
  relatedId?: string;
  relatedTitle?: string;
  relatedUrl?: string;
  channels: PushChannel[];
  metadata?: Record<string, any>;
  expireTime?: string;
  scheduledTime?: string;      // 定时发送
}

// 消息模板
export interface MessageTemplate {
  id: string;
  name: string;
  type: MessageType;
  businessType: BusinessType;
  title: string;
  content: string;
  variables: string[];         // 模板变量
  channels: PushChannel[];
  isActive: boolean;
  createTime: string;
  updateTime: string;
  creator: string;
  description?: string;
}

// 推送配置
export interface PushConfig {
  id: string;
  name: string;
  type: MessageType;
  businessType: BusinessType;
  isEnabled: boolean;
  channels: PushChannel[];
  conditions?: Record<string, any>; // 触发条件
  frequency?: {                // 频控配置
    maxCount: number;          // 最大次数
    timeWindow: number;        // 时间窗口(分钟)
  };
  template?: string;           // 模板ID
  createTime: string;
  updateTime: string;
}

// 第三方平台配置
export interface PlatformConfig {
  id: string;
  platform: ThirdPartyPlatform;
  name: string;
  isEnabled: boolean;
  config: {
    appId?: string;
    appSecret?: string;
    accessToken?: string;
    refreshToken?: string;
    apiEndpoint?: string;
    webhookUrl?: string;
    [key: string]: any;
  };
  status: 'connected' | 'disconnected' | 'error';
  lastSyncTime?: string;
  errorMessage?: string;
  createTime: string;
  updateTime: string;
}

// 用户推送偏好设置
export interface UserPushPreference {
  userId: string;
  preferences: {
    [key in MessageType]?: {
      enabled: boolean;
      channels: PushChannel[];
      quietHours?: {
        start: string;           // 免打扰开始时间
        end: string;             // 免打扰结束时间
      };
    };
  };
  globalSettings: {
    enablePush: boolean;
    enableEmail: boolean;
    enableSMS: boolean;
    quietMode: boolean;
  };
  updateTime: string;
}

// 消息统计数据
export interface MessageStats {
  totalSent: number;           // 总发送数
  totalRead: number;           // 总阅读数
  totalProcessed: number;      // 总处理数
  readRate: number;            // 阅读率
  processRate: number;         // 处理率
  channelStats: {              // 渠道统计
    [key in PushChannel]?: {
      sent: number;
      delivered: number;
      opened: number;
      clicked: number;
      failed: number;
    };
  };
  platformStats: {             // 平台统计
    [key in ThirdPartyPlatform]?: {
      messages: number;
      interactions: number;
      lastSync: string;
    };
  };
  typeStats: {                 // 类型统计
    [key in MessageType]?: {
      count: number;
      readRate: number;
    };
  };
  trendData: Array<{           // 趋势数据
    date: string;
    sent: number;
    read: number;
    processed: number;
  }>;
}

// 批量操作数据
export interface BatchOperation {
  action: 'read' | 'unread' | 'delete' | 'archive' | 'process';
  messageIds: string[];
  filters?: {
    type?: MessageType;
    status?: MessageStatus;
    platform?: ThirdPartyPlatform;
    dateRange?: [string, string];
  };
}

// 消息搜索参数
export interface MessageSearchParams {
  keyword?: string;
  type?: MessageType;
  status?: MessageStatus;
  platform?: ThirdPartyPlatform;
  businessType?: BusinessType;
  priority?: MessagePriority;
  senderId?: string;
  receiverId?: string;
  dateRange?: [string, string];
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 推送任务
export interface PushTask {
  id: string;
  name: string;
  type: 'immediate' | 'scheduled' | 'recurring';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  templateId?: string;
  targetUsers: string[];       // 目标用户
  targetGroups?: string[];     // 目标分组
  channels: PushChannel[];
  scheduledTime?: string;
  recurringConfig?: {          // 循环配置
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: string;
  };
  progress: {
    total: number;
    sent: number;
    delivered: number;
    failed: number;
  };
  createTime: string;
  startTime?: string;
  endTime?: string;
  creator: string;
  errorMessage?: string;
}

// API响应接口
export interface NotificationApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 分页响应接口
export interface NotificationPaginatedResponse<T = any> {
  success: boolean;
  data: {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
    unreadCount?: number;       // 未读数量
  };
  message?: string;
}

// 实时通知数据
export interface RealtimeNotification {
  type: 'new_message' | 'message_read' | 'platform_sync' | 'system_alert';
  data: Message | MessageStats | PlatformConfig;
  timestamp: string;
}
