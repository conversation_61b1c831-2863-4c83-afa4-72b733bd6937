// 个人中心模块类型定义

// 用户基本信息
export interface UserProfile {
  id: string;
  username: string;
  email: string;
  phone: string;
  avatar: string;
  realName: string;
  nickname?: string;
  gender?: 'male' | 'female' | 'other';
  birthday?: string;
  idCard?: string;
  address?: string;
  bio?: string;
  website?: string;
  socialLinks?: {
    wechat?: string;
    qq?: string;
    weibo?: string;
    github?: string;
  };
  createTime: string;
  updateTime: string;
  lastLoginTime?: string;
  status: 'active' | 'inactive' | 'suspended';
}

// 企业信息
export interface CompanyInfo {
  id: string;
  name: string;
  department: string;
  position: string;
  level: string;
  employeeId: string;
  hireDate: string;
  workLocation: string;
  manager?: {
    id: string;
    name: string;
    avatar: string;
  };
  team?: {
    id: string;
    name: string;
    memberCount: number;
  };
}

// 角色权限信息
export interface RolePermission {
  id: string;
  name: string;
  code: string;
  description: string;
  type: 'system' | 'business' | 'data';
  permissions: Permission[];
  dataScope?: {
    type: 'all' | 'department' | 'self' | 'custom';
    departments?: string[];
    users?: string[];
  };
}

export interface Permission {
  id: string;
  name: string;
  code: string;
  resource: string;
  action: string;
  description?: string;
}

// 登录设备信息
export interface LoginDevice {
  id: string;
  deviceType: 'web' | 'mobile' | 'desktop';
  deviceName: string;
  browser?: string;
  os: string;
  ip: string;
  location: string;
  loginTime: string;
  lastActiveTime: string;
  isCurrent: boolean;
  status: 'active' | 'expired';
}

// 操作日志
export interface OperationLog {
  id: string;
  action: string;
  module: string;
  description: string;
  ip: string;
  userAgent: string;
  location: string;
  result: 'success' | 'failure';
  errorMessage?: string;
  createTime: string;
  duration?: number;
}

// 待办事项
export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  type: 'content_review' | 'customer_follow' | 'activity_task' | 'approval' | 'other';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  assignee: string;
  assigneeName: string;
  dueDate?: string;
  createTime: string;
  updateTime: string;
  relatedId?: string;
  relatedType?: string;
  relatedTitle?: string;
}

// 我的内容
export interface MyContent {
  id: string;
  title: string;
  type: 'video' | 'article' | 'image';
  status: 'draft' | 'pending' | 'published' | 'rejected' | 'archived';
  publishTime?: string;
  createTime: string;
  updateTime: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  cover: string;
  tags: string[];
  category: string;
  auditReason?: string;
}

// 我的客户
export interface MyCustomer {
  id: string;
  name: string;
  company?: string;
  phone: string;
  email?: string;
  avatar?: string;
  source: string;
  status: 'potential' | 'contacted' | 'negotiating' | 'signed' | 'lost';
  assignTime: string;
  lastContactTime?: string;
  nextFollowTime?: string;
  value?: number;
  notes?: string;
  tags: string[];
}

// 团队成员
export interface TeamMember {
  id: string;
  name: string;
  avatar: string;
  position: string;
  department: string;
  email: string;
  phone?: string;
  role: 'manager' | 'member' | 'intern';
  status: 'online' | 'offline' | 'busy' | 'away';
  joinTime: string;
  skills: string[];
}

// 个人统计数据
export interface PersonalStats {
  contentStats: {
    totalContent: number;
    publishedContent: number;
    draftContent: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    monthlyGrowth: number;
  };
  customerStats: {
    totalCustomers: number;
    activeCustomers: number;
    newCustomers: number;
    conversionRate: number;
    totalValue: number;
    monthlyValue: number;
  };
  taskStats: {
    totalTasks: number;
    completedTasks: number;
    pendingTasks: number;
    overdueTask: number;
    completionRate: number;
    avgCompletionTime: number;
  };
  teamStats: {
    teamSize: number;
    activeProjects: number;
    completedProjects: number;
    teamPerformance: number;
  };
}

// 个人设置
export interface PersonalSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    desktop: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
    types: {
      system: boolean;
      content: boolean;
      customer: boolean;
      team: boolean;
      approval: boolean;
    };
  };
  privacy: {
    profileVisible: boolean;
    contactVisible: boolean;
    activityVisible: boolean;
    allowSearch: boolean;
  };
  workspace: {
    layout: 'grid' | 'list';
    density: 'compact' | 'normal' | 'comfortable';
    sidebarCollapsed: boolean;
    showQuickActions: boolean;
    customWidgets: string[];
  };
}

// 安全设置
export interface SecuritySettings {
  passwordLastChanged: string;
  mfaEnabled: boolean;
  mfaType?: 'totp' | 'sms' | 'email';
  loginNotification: boolean;
  sessionTimeout: number;
  allowMultipleLogin: boolean;
  ipWhitelist: string[];
  trustedDevices: string[];
}

// 个人成就
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'content' | 'customer' | 'team' | 'system';
  level: 'bronze' | 'silver' | 'gold' | 'platinum';
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  unlockedTime?: string;
  reward?: {
    type: 'badge' | 'points' | 'privilege';
    value: string | number;
  };
}

// 快捷操作
export interface QuickAction {
  id: string;
  name: string;
  icon: string;
  description: string;
  url: string;
  type: 'internal' | 'external';
  category: 'content' | 'customer' | 'team' | 'system' | 'tool';
  order: number;
  enabled: boolean;
}

// 工作台小部件
export interface WorkspaceWidget {
  id: string;
  name: string;
  type: 'todo' | 'stats' | 'chart' | 'list' | 'calendar' | 'weather' | 'news';
  title: string;
  description?: string;
  config: Record<string, any>;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  enabled: boolean;
  refreshInterval?: number;
}

// API响应类型
export interface ProfileApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 分页响应类型
export interface ProfilePaginatedResponse<T = any> {
  success: boolean;
  data: {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  };
  message?: string;
}

// 表单数据类型
export interface ProfileUpdateData {
  realName?: string;
  nickname?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: 'male' | 'female' | 'other';
  birthday?: string;
  address?: string;
  bio?: string;
  website?: string;
  socialLinks?: UserProfile['socialLinks'];
}

export interface PasswordChangeData {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface MFASetupData {
  type: 'totp' | 'sms' | 'email';
  secret?: string;
  code: string;
  backupCodes?: string[];
}

// 搜索参数
export interface ProfileSearchParams {
  keyword?: string;
  type?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
