/**
 * 统一环境配置中心
 * 集中管理所有环境相关配置
 */

// 获取当前环境
const ENV = process.env.UMI_ENV || process.env.NODE_ENV || 'development';

// 环境配置接口
export interface EnvConfig {
  // 环境标识
  env: string;
  // 是否为开发环境
  isDev: boolean;
  // 是否为测试环境
  isTest: boolean;
  // 是否为生产环境
  isProd: boolean;
  // 应用名称
  appName: string;
  // API基础URL
  apiBaseUrl: string;
  // API超时时间(ms)
  apiTimeout: number;
  // 是否启用API日志
  enableApiLog: boolean;
  // 是否启用调试工具
  enableDebugTools: boolean;
  // 是否启用性能监控
  enablePerformanceMonitoring: boolean;
  // 是否启用错误跟踪
  enableErrorTracking: boolean;
  // 是否启用模拟数据
  enableMock: boolean;
}

// 环境配置映射
const configs: Record<string, EnvConfig> = {
  // 开发环境配置
  development: {
    env: 'development',
    isDev: true,
    isTest: false,
    isProd: false,
    appName: 'Front-Logix-Admin (开发)',
    apiBaseUrl: process.env.API_URL || 'http://localhost:3000',
    apiTimeout: 10000,
    enableApiLog: true,
    enableDebugTools: true,
    enablePerformanceMonitoring: false,
    enableErrorTracking: false,
    enableMock: true,
  },

  // 测试环境配置
  test: {
    env: 'test',
    isDev: false,
    isTest: true,
    isProd: false,
    appName: 'Front-Logix-Admin (测试)',
    apiBaseUrl: process.env.API_URL || 'https://test-api.starrier.org',
    apiTimeout: 8000,
    enableApiLog: true,
    enableDebugTools: true,
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    enableMock: false,
  },

  // 生产环境配置
  production: {
    env: 'production',
    isDev: false,
    isTest: false,
    isProd: true,
    appName: 'Front-Logix-Admin',
    apiBaseUrl: process.env.API_URL || 'https://api.starrier.org',
    apiTimeout: 5000,
    enableApiLog: false,
    enableDebugTools: false,
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    enableMock: false,
  },
};

// 导出当前环境配置
export const envConfig: EnvConfig = configs[ENV] || configs.development;

// 导出所有环境配置(用于构建工具)
export const allEnvConfigs = configs;

export default envConfig;
