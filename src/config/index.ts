/**
 * 应用配置
 * 根据不同环境加载不同配置
 */
import { envConfig } from '@/utils/env';

// 基础配置
const baseConfig = {
  // 应用信息
  app: {
    name: envConfig.appName,
    version: '1.0.0',
  },

  // API配置
  api: {
    baseUrl: envConfig.apiBaseUrl,
    timeout: envConfig.apiTimeout,
  },

  // 认证配置
  auth: {
    tokenKey: 'auth_token',
    tokenExpires: 86400, // 24小时
  },

  // 上传配置
  upload: {
    maxSize: 5 * 1024 * 1024, // 5MB
    acceptTypes: ['.jpg', '.jpeg', '.png', '.gif'],
  },
};

// 开发环境配置
const devConfig = {
  // 开发环境特定配置
  api: {
    ...baseConfig.api,
    mockEnabled: true,
  },

  // 调试配置
  debug: {
    enabled: true,
    logLevel: 'debug',
  },
};

// 测试环境配置
const testConfig = {
  // 测试环境特定配置
  api: {
    ...baseConfig.api,
    mockEnabled: false,
  },

  // 调试配置
  debug: {
    enabled: true,
    logLevel: 'info',
  },
};

// 生产环境配置
const prodConfig = {
  // 生产环境特定配置
  api: {
    ...baseConfig.api,
    mockEnabled: false,
  },

  // 调试配置
  debug: {
    enabled: false,
    logLevel: 'error',
  },
};

// 根据环境返回对应配置
function getConfig() {
  if (envConfig.isProd) {
    return { ...baseConfig, ...prodConfig };
  }

  if (envConfig.isTest) {
    return { ...baseConfig, ...testConfig };
  }

  return { ...baseConfig, ...devConfig };
}

// 导出配置
export const config = getConfig();

export default config;
