interface UserInfo {
  name?: string;
  isAdmin?: boolean;
  roles?: string[];
  userType?: string;
  [key: string]: any;
}

// 验证用户信息格式
const validateUserInfo = (data: any): data is UserInfo => {
  return (
    typeof data === 'object' &&
    (data.name === undefined || typeof data.name === 'string') &&
    (data.isAdmin === undefined || typeof data.isAdmin === 'boolean') &&
    (data.roles === undefined || Array.isArray(data.roles)) &&
    (data.userType === undefined || typeof data.userType === 'string')
  );
};

// 设置默认账号权限
const setDefaultAdminPermission = () => {
  const defaultAdmin = {
    name: 'frost-chain-admin',
    isAdmin: true,
    roles: ['admin'],
    userType: 'admin',
  };
  try {
    localStorage.setItem('userInfo', JSON.stringify(defaultAdmin));
    console.log('默认管理员权限已设置到localStorage');
  } catch (e) {
    console.error('写入localStorage失败:', e);
  }
};

// 确保localStorage可用
const isLocalStorageAvailable = () => {
  try {
    const testKey = '__test__';
    localStorage.setItem(testKey, testKey);
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    console.error('localStorage不可用:', e);
    return false;
  }
};

export default () => {
  // 检查是否为默认账号
  const isDefaultAdmin = window.location.href.includes('frost-chain-admin');
  if (isDefaultAdmin && isLocalStorageAvailable()) {
    if (!localStorage.getItem('userInfo')) {
      setDefaultAdminPermission();
    } else {
      console.log('已存在用户权限数据');
    }
  }

  // 从localStorage获取用户信息
  let userInfo: UserInfo = {};
  try {
    const userData = localStorage.getItem('userInfo');
    if (userData) {
      const parsedData = JSON.parse(userData);
      if (validateUserInfo(parsedData)) {
        userInfo = parsedData;
        console.log('从localStorage读取的用户信息:', userInfo);
      } else {
        console.warn('用户信息格式无效，使用空对象');
        userInfo = {};
      }
    } else {
      console.log('localStorage中没有用户信息');
    }
  } catch (e) {
    console.error('解析用户信息失败:', e);
    userInfo = {};
  }

  // 调试日志
  console.group('[权限调试]');
  console.log('Cookie用户信息:', userInfo);

  // 统一权限检查逻辑
  const canAdmin = !!(
    userInfo &&
    (userInfo.isAdmin ||
      userInfo.name === 'frost-chain-admin' ||
      (userInfo.roles && userInfo.roles.includes('admin')) ||
      (userInfo.userType && userInfo.userType === 'admin'))
  );

  const canAccessCrud = true; // 临时开放所有用户访问CRUD

  console.log('权限状态:', {
    hasAdminPermission: canAdmin,
    canAccessCrud,
  });
  console.groupEnd();

  return {
    canAccessCrud,
    canAdmin,
    // 兼容旧代码
    hasAdminPermission: canAdmin,
    canSeeAdmin: canAdmin,
  };
};
