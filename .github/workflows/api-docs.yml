name: API Documentation

on:
  push:
    branches: [ main, master , tec-upgrade ]
    paths:
      - 'api-docs/**'
      - '.github/workflows/api-docs.yml'

jobs:
  deploy-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Redocly CLI
        run: npm install -g @redocly/cli

      - name: Generate Docs
        run: redocly build-docs api-docs/openapi.yaml -o docs/index.html

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          deploy_key: ${{ secrets.DEPLOY_KEY }}
          external_repository: Starrier/api-docs-frox-admin
          publish_branch: gh-pages
          publish_dir: ./docs
          commit_message: "docs: update API documentation first version"
          force_orphan: true
          keep_files: false
