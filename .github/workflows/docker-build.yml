name: Docker Build and Push to Docker Hub

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: docker.io
  # 替换为你的Docker Hub用户名
  DOCKERHUB_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
  # 镜像名称格式: docker.io/<username>/<repository>
  IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/frologi-admin

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Remove package-lock.json
        run: rm -f package-lock.json

      - name: Install dependencies
        run: |
          yarn config set ignore-engines true
          yarn install --frozen-lockfile
          yarn list react-router-dom

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=sha

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NODE_VERSION=20
