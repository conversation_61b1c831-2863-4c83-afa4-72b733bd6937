name: Deploy to Environments

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - production
  push:
    branches:
      - main
      - release/*
      - tec-upgrade

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest

    # 根据触发事件确定环境
    env:
      DEPLOY_ENV: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'test' || (startsWith(github.ref, 'refs/heads/release/') && 'production' || 'test')) }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Setup Umi
        run: yarn max setup

      - name: Build application
        run: |
          if [ "${{ env.DEPLOY_ENV }}" = "production" ]; then
            yarn build:prod
          elif [ "${{ env.DEPLOY_ENV }}" = "test" ]; then
            yarn build:test
          else
            yarn build
          fi
        env:
          NODE_ENV: ${{ env.DEPLOY_ENV }}
          UMI_ENV: ${{ env.DEPLOY_ENV }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile.multistage
          target: ${{ env.DEPLOY_ENV }}
          push: true
          tags: |
            starrier/frologi-admin:0.0.1
            ${{ env.DEPLOY_ENV == 'production' && 'starrier/frologi-admin:latest' || '' }}
            starrier/frologi-admin:${{ env.DEPLOY_ENV }}
          build-args: |
            BUILD_ENV=${{ env.DEPLOY_ENV }}

      # 测试环境部署
      - name: Deploy to Test
        if: env.DEPLOY_ENV == 'test'
        run: |
          echo "Deploying to Test environment"
          # 这里添加测试环境部署脚本
          # 例如使用 SSH 部署到测试服务器

      # 生产环境部署
      - name: Deploy to Production
        if: env.DEPLOY_ENV == 'production'
        run: |
          echo "Deploying to Production environment"
          # 这里添加生产环境部署脚本
          # 例如使用 Kubernetes 部署到生产集群

      # 部署通知
      - name: Notify deployment
        run: |
          echo "Deployed to ${{ env.DEPLOY_ENV }} environment"
          # 这里可以添加通知脚本，如发送邮件或消息到企业微信/钉钉
