import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'Front Logix Admin',
  },
  routes: [
    {
      path: '/',
      component: './RootRedirect',
      layout: false,
      hideInMenu: true,
    },
    // 认证相关页面（不显示在菜单中）
    {
      name: '登录',
      path: '/login',
      component: './Login',
      layout: false,
      hideInMenu: true,
    },
    {
      name: '注册',
      path: '/register',
      component: './Register',
      layout: false,
      hideInMenu: true,
    },
    {
      name: '重置密码',
      path: '/reset-password',
      component: './ResetPassword',
      layout: false,
      hideInMenu: true,
    },
    // 主要业务页面
    {
      name: '首页',
      path: '/home',
      component: './Home',
      icon: 'home',
    },
    // 冰块订单管理模块
    {
      name: '冰块订单管理',
      path: '/ice-order',
      icon: 'shopping',
      routes: [
        {
          name: '订单管理',
          path: '/ice-order/orders',
          component: './IceOrder/Orders',
        },
        {
          name: '智能检索',
          path: '/ice-order/search',
          component: './IceOrder/SearchSystem',
        },
        {
          name: '供应商管理',
          path: '/ice-order/suppliers',
          component: './IceOrder/SupplierManagement',
        },
        {
          name: '工厂管理',
          path: '/ice-order/factories',
          component: './IceOrder/FactoryManagement',
        },
        {
          name: '冰块类型',
          path: '/ice-order/ice-types',
          component: './IceOrder/IceTypes',
        },
        {
          name: '用户管理',
          path: '/ice-order/users',
          component: './IceOrder/Users',
        },
        {
          name: '定价管理',
          path: '/ice-order/pricing',
          component: './IceOrder/PricingManagement',
        },
        {
          name: '数据看板',
          path: '/ice-order/dashboard',
          component: './IceOrder/Dashboard',
        },
      ],
    },
    // 产品管理
    {
      name: '产品管理',
      path: '/product-management',
      component: './ProductManagement',
      icon: 'appstore',
    },
    // 监控报表
    {
      name: '监控报表',
      path: '/monitoring-report',
      component: './MonitoringReport',
      icon: 'lineChart',
    },
    // 消息通知
    {
      name: '消息通知',
      path: '/notification',
      icon: 'bell',
      routes: [
        {
          name: '消息中心',
          path: '/notification/center',
          component: './Notification/Center',
        },
        {
          name: '推送管理',
          path: '/notification/push',
          component: './Notification/Push',
        },
        {
          name: '模板管理',
          path: '/notification/template',
          component: './Notification/Template',
        },
        {
          name: '平台配置',
          path: '/notification/platform',
          component: './Notification/Platform',
        },
        {
          name: '统计分析',
          path: '/notification/analytics',
          component: './Notification/Analytics',
        },
      ],
    },
    // 运营管理
    {
      name: '运营管理',
      path: '/operation',
      icon: 'team',
      routes: [
        {
          name: '内容首页',
          path: '/operation/content-home',
          component: './Operation/ContentHome',
        },
        {
          name: '内容管理',
          path: '/operation/content-management',
          component: './Operation/ContentManagement',
        },
        {
          name: '内容创建',
          path: '/operation/content-create',
          routes: [
            {
              name: '短视频创建',
              path: '/operation/content-create/video',
              component: './Operation/ContentCreate/VideoCreate',
            },
            {
              name: '图文创建',
              path: '/operation/content-create/article',
              component: './Operation/ContentCreate/ArticleCreate',
            },
          ],
        },
        {
          name: '发布分发',
          path: '/operation/distribution',
          component: './Operation/Distribution',
        },
        {
          name: '数据分析',
          path: '/operation/analytics',
          routes: [
            {
              name: '数据仪表盘',
              path: '/operation/analytics/dashboard',
              component: './Operation/Analytics/Dashboard',
            },
            {
              name: '内容分析',
              path: '/operation/analytics/content',
              component: './Operation/Analytics/ContentAnalytics',
            },
            {
              name: '用户分析',
              path: '/operation/analytics/user',
              component: './Operation/Analytics/UserAnalytics',
            },
            {
              name: '活动分析',
              path: '/operation/analytics/activity',
              component: './Operation/Analytics/ActivityAnalytics',
            },
          ],
        },
      ],
    },
    // 系统管理
    {
      name: '系统管理',
      path: '/system',
      icon: 'setting',
      routes: [
        {
          name: '权限管理',
          path: '/system/permissions',
          component: './PermissionManagement',
        },
        {
          name: '操作日志',
          path: '/system/operation-log',
          component: './System/OperationLog',
        },
      ],
    },
    // 开发示例（可选显示）
    {
      name: '开发示例',
      path: '/examples',
      icon: 'code',
      routes: [
        {
          name: '权限演示',
          path: '/examples/access',
          component: './Access',
        },
        {
          name: 'CRUD 示例',
          path: '/examples/table',
          component: './Table',
        },
        {
          name: 'CRUD 示例2',
          path: '/examples/crud-example',
          component: './CrudExample',
        },
      ],
    },
    // 个人中心（不显示在主菜单中）
    {
      name: '个人中心',
      path: '/profile',
      hideInMenu: true,
      component: './Profile/components/ProfileLayout',
      routes: [
        {
          path: '/profile',
          redirect: '/profile/overview',
        },
        {
          name: '个人概览',
          path: '/profile/overview',
          component: './Profile/Overview',
        },
        {
          name: '基本信息',
          path: '/profile/basic',
          component: './Profile/BasicInfo',
        },
        {
          name: '账号安全',
          path: '/profile/security',
          component: './Profile/Security',
        },
        {
          name: '权限管理',
          path: '/profile/permissions',
          component: './Profile/Permissions',
        },
        {
          name: '工作台',
          path: '/profile/workspace',
          component: './Profile/Workspace',
        },
        {
          name: '我的内容',
          path: '/profile/content',
          component: './Profile/MyContent',
        },
        {
          name: '团队协作',
          path: '/profile/team',
          component: './Profile/Team',
        },
        {
          name: '个人设置',
          path: '/profile/settings',
          component: './Profile/Settings',
        },
      ],
    },
    // 测试页面（开发调试用）
    {
      name: '重定向测试',
      path: '/test-redirect',
      component: './TestRedirect',
      hideInMenu: true,
    },
    // 错误页面
    {
      name: '404',
      path: '/404',
      component: './NotFound',
      layout: false,
      hideInMenu: true,
    },
    {
      path: '*',
      redirect: '/404',
    },
  ],
  npmClient: 'yarn',
});
