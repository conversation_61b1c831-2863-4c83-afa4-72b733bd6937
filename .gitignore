# 依赖
/node_modules

# 本地环境文件
/.env.local
/.env.*.local

# 配置文件
/.umirc.local.ts
/config/config.local.ts

# 构建文件
/src/.umi
/src/.umi-production
/src/.umi-test
/.umi
/.umi-production
/.umi-test
/dist
/.mfsu
.swc

# 日志文件
/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 编辑器配置
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 测试覆盖率报告
coverage/
.nyc_output/
*.lcov

# 性能分析文件
*.cpuprofile
*.heapprofile

/api-server