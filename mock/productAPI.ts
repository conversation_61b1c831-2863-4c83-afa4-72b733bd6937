import { Request, Response } from 'express';
import { parse } from 'url';

// 初始产品数据
let products = [
  {
    id: 1,
    title: "工业冰",
    description: "我们的工业冰产品专为各种工业应用而设计，具有高纯度和耐用性，可满足最严格的工业标准。",
    features: [
      "高纯度，无杂质",
      "定制尺寸和形状",
      "快速冷却效果",
      "长时间保持低温",
      "环保生产工艺"
    ],
    applications: [
      "食品加工冷却",
      "混凝土温度控制",
      "医疗设备冷却",
      "化工过程温度调节",
      "冷链物流"
    ],
    images: [
      { id: 1, src: "/img/industrial-ice-1.jpg", alt: "工业冰块" },
      { id: 2, src: "/img/industrial-ice-2.jpg", alt: "工业冰应用" }
    ],
    specifications: {
      "纯度": "99.9%",
      "标准尺寸": "25mm × 25mm × 25mm",
      "融化时间": "标准条件下约4-6小时",
      "包装方式": "25kg/袋，1吨/托盘"
    }
  },
  {
    id: 2,
    title: "食用冰",
    description: "我们的食用冰产品采用纯净水制作，符合食品级标准，适用于各种餐饮和饮料应用。",
    features: [
      "食品级纯净水制作",
      "晶莹剔透，无气泡",
      "多种形状可选",
      "融化均匀，不影响饮品口感",
      "符合食品安全标准"
    ],
    applications: [
      "餐厅和酒吧饮品",
      "咖啡厅冰饮",
      "酒店服务",
      "超市和便利店销售",
      "活动和宴会服务"
    ],
    images: [
      { id: 1, src: "/img/food-ice-1.jpg", alt: "食用冰块" },
      { id: 2, src: "/img/food-ice-2.jpg", alt: "食用冰应用" }
    ],
    specifications: {
      "水源": "纯净水/矿泉水",
      "形状": "方块、圆柱、球形、管状",
      "包装": "2kg/袋，10kg/箱",
      "储存条件": "-18°C以下"
    }
  },
  {
    id: 3,
    title: "工业冰块机",
    description: "高效率工业级制冰设备，适用于大规模冰块生产。",
    features: [
      "高产能 - 每天可生产5吨冰块",
      "节能设计 - 比传统设备节省30%能耗",
      "智能控制 - 全自动操作系统"
    ],
    applications: [
      "食品加工厂",
      "饮料生产线",
      "水产品保鲜"
    ],
    images: [
      { id: 1, src: "/img/industrial-ice-1.jpg", alt: "工业冰块机正面图" },
      { id: 2, src: "/img/industrial-ice-2.jpg", alt: "工业冰块机侧面图" }
    ],
    specifications: {
      "产能": "5吨/天",
      "功率": "7.5kW",
      "尺寸": "2.5m × 1.8m × 2.2m",
      "重量": "850kg",
      "制冷剂": "R290（环保型）"
    }
  }
];

export default {
  // 获取产品列表
  'GET /api/products': (req: Request, res: Response) => {
    res.json({
      status: 200,
      message: "获取成功",
      data: products
    });
  },

  // 获取产品详情
  'GET /api/products/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const product = products.find(item => item.id === parseInt(id));
    
    if (product) {
      res.json({
        status: 200,
        message: "获取成功",
        data: product
      });
    } else {
      res.status(404).json({
        status: 404,
        message: "产品不存在",
        data: null
      });
    }
  },

  // 创建产品
  'POST /api/products': (req: Request, res: Response) => {
    const newProduct = req.body;
    const newId = Math.max(...products.map(p => p.id), 0) + 1;
    
    const productWithId = {
      ...newProduct,
      id: newId
    };
    
    products.push(productWithId);
    
    res.status(201).json({
      status: 201,
      message: "创建成功",
      data: productWithId
    });
  },

  // 更新产品
  'PUT /api/products/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    const index = products.findIndex(item => item.id === parseInt(id));
    
    if (index !== -1) {
      products[index] = {
        ...products[index],
        ...updateData
      };
      
      res.json({
        status: 200,
        message: "更新成功",
        data: products[index]
      });
    } else {
      res.status(404).json({
        status: 404,
        message: "产品不存在",
        data: null
      });
    }
  },

  // 删除产品
  'DELETE /api/products/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const index = products.findIndex(item => item.id === parseInt(id));
    
    if (index !== -1) {
      products.splice(index, 1);
      
      res.json({
        status: 200,
        message: "删除成功"
      });
    } else {
      res.status(404).json({
        status: 404,
        message: "产品不存在"
      });
    }
  }
};
