import { Request, Response } from 'express';
import { parse } from 'url';

// 生成模拟数据
const generateMockData = () => {
  const eventTypes = [
    'click',
    'pageView',
    'error',
    'api',
    'performance',
    'custom',
  ];
  const deviceTypes = ['desktop', 'mobile', 'tablet'];
  const browsers = ['Chrome', 'Safari', 'Firefox', 'Edge'];
  const pages = [
    '/home',
    '/products',
    '/about',
    '/contact',
    '/dashboard',
    '/profile',
  ];

  const mockData = [];

  // 生成过去7天的数据
  for (let i = 0; i < 200; i++) {
    const timestamp =
      Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000);
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];

    let eventName = '';
    let eventData = {};

    switch (eventType) {
      case 'click':
        eventName = ['button_click', 'link_click', 'menu_click'][
          Math.floor(Math.random() * 3)
        ];
        eventData = {
          elementId: `element-${Math.floor(Math.random() * 100)}`,
          elementText: `Text ${Math.floor(Math.random() * 50)}`,
        };
        break;
      case 'pageView':
        eventName = 'page_view';
        eventData = {
          duration: Math.floor(Math.random() * 300) + 5,
        };
        break;
      case 'error':
        eventName = ['js_error', 'api_error', 'resource_error'][
          Math.floor(Math.random() * 3)
        ];
        eventData = {
          message: `Error message ${Math.floor(Math.random() * 20)}`,
          stack: `Error stack ${Math.floor(Math.random() * 10)}`,
        };
        break;
      case 'api':
        eventName = ['api_request', 'api_response'][
          Math.floor(Math.random() * 2)
        ];
        eventData = {
          url: `/api/endpoint-${Math.floor(Math.random() * 10)}`,
          method: ['GET', 'POST', 'PUT', 'DELETE'][
            Math.floor(Math.random() * 4)
          ],
          status: [200, 201, 400, 401, 403, 404, 500][
            Math.floor(Math.random() * 7)
          ],
        };
        break;
      case 'performance':
        eventName = 'page_load';
        eventData = {
          loadTime: Math.floor(Math.random() * 5000) + 100,
          domContentLoaded: Math.floor(Math.random() * 2000) + 50,
          firstPaint: Math.floor(Math.random() * 1000) + 20,
        };
        break;
      case 'custom':
        eventName = ['feature_usage', 'user_preference', 'search'][
          Math.floor(Math.random() * 3)
        ];
        eventData = {
          customData: `Custom data ${Math.floor(Math.random() * 30)}`,
        };
        break;
    }

    const deviceType =
      deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
    const browser = browsers[Math.floor(Math.random() * browsers.length)];
    const page = pages[Math.floor(Math.random() * pages.length)];

    mockData.push({
      id: `event-${i}-${Date.now()}`,
      timestamp,
      eventType,
      eventName,
      eventData,
      deviceInfo: {
        deviceId: `device-${Math.floor(Math.random() * 1000)}`,
        deviceType,
        osVersion: `OS ${Math.floor(Math.random() * 5) + 10}`,
        browser,
        browserVersion: `${Math.floor(Math.random() * 20) + 80}.0.${Math.floor(
          Math.random() * 5000,
        )}`,
      },
      userInfo: {
        userId:
          Math.random() > 0.3
            ? `user-${Math.floor(Math.random() * 100)}`
            : undefined,
        sessionId: `session-${Math.floor(Math.random() * 1000)}`,
      },
      location: {
        page,
        url: `https://example.com${page}?param=${Math.floor(
          Math.random() * 100,
        )}`,
        referrer:
          Math.random() > 0.5
            ? `https://referrer.com/page-${Math.floor(Math.random() * 10)}`
            : undefined,
      },
    });
  }

  return mockData;
};

const mockData = generateMockData();

// 计算统计数据
const calculateStatistics = (data: any[], params: any) => {
  // 过滤数据
  let filteredData = [...data];

  if (params.startTime) {
    filteredData = filteredData.filter(
      (item) => item.timestamp >= params.startTime,
    );
  }

  if (params.endTime) {
    filteredData = filteredData.filter(
      (item) => item.timestamp <= params.endTime,
    );
  }

  if (params.eventType) {
    filteredData = filteredData.filter(
      (item) => item.eventType === params.eventType,
    );
  }

  if (params.deviceType) {
    filteredData = filteredData.filter(
      (item) => item.deviceInfo.deviceType === params.deviceType,
    );
  }

  // 计算统计信息
  const totalEvents = filteredData.length;

  // 事件类型分布
  const eventTypeDistribution: Record<string, number> = {};
  filteredData.forEach((item) => {
    if (!eventTypeDistribution[item.eventType]) {
      eventTypeDistribution[item.eventType] = 0;
    }
    eventTypeDistribution[item.eventType]++;
  });

  // 页面访问排名
  const pageCount: Record<string, number> = {};
  filteredData.forEach((item) => {
    if (item.location && item.location.page) {
      if (!pageCount[item.location.page]) {
        pageCount[item.location.page] = 0;
      }
      pageCount[item.location.page]++;
    }
  });

  const topPages = Object.entries(pageCount)
    .map(([page, count]) => ({ page, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // 时间分布
  const hourCount: Record<number, number> = {};
  for (let i = 0; i < 24; i++) {
    hourCount[i] = 0;
  }

  filteredData.forEach((item) => {
    const hour = new Date(item.timestamp).getHours();
    hourCount[hour]++;
  });

  const timeDistribution = Object.entries(hourCount).map(([hour, count]) => ({
    hour: parseInt(hour),
    count,
  }));

  // 错误数量
  const errorCount = filteredData.filter(
    (item) => item.eventType === 'error',
  ).length;

  return {
    totalEvents,
    eventTypeDistribution,
    topPages,
    timeDistribution,
    errorCount,
  };
};

export default {
  'GET /api/frost-chain/monitoring-data': (req: Request, res: Response) => {
    const { query } = parse(req.url, true);

    let filteredData = [...mockData];

    // 应用过滤条件
    if (query.startTime) {
      filteredData = filteredData.filter(
        (item) => item.timestamp >= Number(query.startTime),
      );
    }

    if (query.endTime) {
      filteredData = filteredData.filter(
        (item) => item.timestamp <= Number(query.endTime),
      );
    }

    if (query.eventType) {
      filteredData = filteredData.filter(
        (item) => item.eventType === query.eventType,
      );
    }

    if (query.eventName) {
      filteredData = filteredData.filter(
        (item) => item.eventName === query.eventName,
      );
    }

    if (query.deviceType) {
      filteredData = filteredData.filter(
        (item) => item.deviceInfo.deviceType === query.deviceType,
      );
    }

    if (query.userId) {
      filteredData = filteredData.filter(
        (item) => item.userInfo?.userId === query.userId,
      );
    }

    if (query.page) {
      filteredData = filteredData.filter(
        (item) => item.location?.page === query.page,
      );
    }

    // 排序 - 按时间戳降序
    filteredData.sort((a, b) => b.timestamp - a.timestamp);

    // 分页
    const current = Number(query.current) || 1;
    const pageSize = Number(query.pageSize) || 10;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        list: paginatedData,
        total: filteredData.length,
        current,
        pageSize,
      },
    });
  },

  'GET /api/frost-chain/monitoring-statistics': (
    req: Request,
    res: Response,
  ) => {
    const { query } = parse(req.url, true);

    const statistics = calculateStatistics(mockData, query);

    res.json({
      success: true,
      data: statistics,
    });
  },
};
