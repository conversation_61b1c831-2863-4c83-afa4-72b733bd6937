import { Request, Response } from 'express';
import { parse } from 'url';
import { OrderStatus } from '../src/services/iceOrder/types';

// 用户数据
let users = [
  {
    id: 1,
    username: '张三',
    email: '<PERSON><PERSON><PERSON>@example.com',
    phone: '13800138001',
  },
  {
    id: 2,
    username: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
  },
  {
    id: 3,
    username: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
  },
];

// 冰块类型数据
let iceTypes = [
  {
    id: 1,
    name: '方块冰',
    description: '标准方形冰块，适合各种饮品',
  },
  {
    id: 2,
    name: '碎冰',
    description: '小颗粒碎冰，适合刨冰和冰沙',
  },
  {
    id: 3,
    name: '圆柱冰',
    description: '圆柱形冰块，适合威士忌等高档酒类',
  },
  {
    id: 4,
    name: '球形冰',
    description: '完美球形冰块，缓慢融化，适合品鉴酒类',
  },
];

// 订单数据
let orders = [
  {
    id: 1,
    userId: 1,
    username: '张三',
    items: [
      {
        id: 1,
        iceTypeId: 1,
        iceTypeName: '方块冰',
        quantity: 100,
        size: '2x2x2cm',
      },
      {
        id: 2,
        iceTypeId: 2,
        iceTypeName: '碎冰',
        quantity: 50,
        size: '小颗粒',
      },
    ],
    deliveryDateTime: '2023-06-15T14:00:00',
    deliveryAddress: '北京市朝阳区建国路88号',
    contactPerson: '张三',
    contactPhone: '13800138001',
    status: OrderStatus.CONFIRMED,
    createdAt: '2023-06-13T10:30:00',
    updatedAt: '2023-06-13T10:30:00',
  },
  {
    id: 2,
    userId: 2,
    username: '李四',
    items: [
      {
        id: 3,
        iceTypeId: 3,
        iceTypeName: '圆柱冰',
        quantity: 30,
        size: '3x6cm',
      },
    ],
    deliveryDateTime: '2023-06-16T10:00:00',
    deliveryAddress: '上海市浦东新区陆家嘴1号',
    contactPerson: '李四',
    contactPhone: '13800138002',
    status: OrderStatus.PENDING,
    createdAt: '2023-06-14T09:15:00',
    updatedAt: '2023-06-14T09:15:00',
  },
  {
    id: 3,
    userId: 3,
    username: '王五',
    items: [
      {
        id: 4,
        iceTypeId: 4,
        iceTypeName: '球形冰',
        quantity: 20,
        size: '直径5cm',
      },
      {
        id: 5,
        iceTypeId: 1,
        iceTypeName: '方块冰',
        quantity: 50,
        size: '3x3x3cm',
      },
    ],
    deliveryDateTime: '2023-06-17T16:30:00',
    deliveryAddress: '广州市天河区天河路123号',
    contactPerson: '王五',
    contactPhone: '13800138003',
    status: OrderStatus.PROCESSING,
    createdAt: '2023-06-14T14:20:00',
    updatedAt: '2023-06-15T09:00:00',
  },
];

// 用户API
const userAPI = {
  // 获取用户列表
  'GET /api/users': (req: Request, res: Response) => {
    res.json({
      status: 200,
      message: '获取成功',
      data: users,
    });
  },

  // 获取用户详情
  'GET /api/users/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const user = users.find((item) => item.id === parseInt(id));

    if (user) {
      res.json({
        status: 200,
        message: '获取成功',
        data: user,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '用户不存在',
        data: null,
      });
    }
  },

  // 创建用户
  'POST /api/users': (req: Request, res: Response) => {
    const newUser = req.body;
    const newId = Math.max(...users.map((u) => u.id), 0) + 1;

    const userWithId = {
      ...newUser,
      id: newId,
    };

    users.push(userWithId);

    res.status(201).json({
      status: 201,
      message: '创建成功',
      data: userWithId,
    });
  },

  // 更新用户
  'PUT /api/users/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    const index = users.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      users[index] = {
        ...users[index],
        ...updateData,
      };

      res.json({
        status: 200,
        message: '更新成功',
        data: users[index],
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '用户不存在',
        data: null,
      });
    }
  },

  // 删除用户
  'DELETE /api/users/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const index = users.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      const deletedUser = users[index];
      users.splice(index, 1);

      res.json({
        status: 200,
        message: '删除成功',
        data: deletedUser,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '用户不存在',
        data: null,
      });
    }
  },
};

// 冰块类型API
const iceTypeAPI = {
  // 获取冰块类型列表
  'GET /api/ice-types': (req: Request, res: Response) => {
    res.json({
      status: 200,
      message: '获取成功',
      data: iceTypes,
    });
  },

  // 获取冰块类型详情
  'GET /api/ice-types/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const iceType = iceTypes.find((item) => item.id === parseInt(id));

    if (iceType) {
      res.json({
        status: 200,
        message: '获取成功',
        data: iceType,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '冰块类型不存在',
        data: null,
      });
    }
  },

  // 创建冰块类型
  'POST /api/ice-types': (req: Request, res: Response) => {
    const newIceType = req.body;
    const newId = Math.max(...iceTypes.map((t) => t.id), 0) + 1;

    const iceTypeWithId = {
      ...newIceType,
      id: newId,
    };

    iceTypes.push(iceTypeWithId);

    res.status(201).json({
      status: 201,
      message: '创建成功',
      data: iceTypeWithId,
    });
  },

  // 更新冰块类型
  'PUT /api/ice-types/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    const index = iceTypes.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      iceTypes[index] = {
        ...iceTypes[index],
        ...updateData,
      };

      res.json({
        status: 200,
        message: '更新成功',
        data: iceTypes[index],
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '冰块类型不存在',
        data: null,
      });
    }
  },

  // 删除冰块类型
  'DELETE /api/ice-types/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const index = iceTypes.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      const deletedIceType = iceTypes[index];
      iceTypes.splice(index, 1);

      res.json({
        status: 200,
        message: '删除成功',
        data: deletedIceType,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '冰块类型不存在',
        data: null,
      });
    }
  },
};

// 订单API
const orderAPI = {
  // 获取订单列表
  'GET /api/orders': (req: Request, res: Response) => {
    const { query } = parse(req.url, true);
    let filteredOrders = [...orders];

    // 按用户ID过滤
    if (query.userId) {
      filteredOrders = filteredOrders.filter(
        (order) => order.userId === parseInt(query.userId as string),
      );
    }

    res.json({
      status: 200,
      message: '获取成功',
      data: filteredOrders,
    });
  },

  // 获取订单详情
  'GET /api/orders/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const order = orders.find((item) => item.id === parseInt(id));

    if (order) {
      res.json({
        status: 200,
        message: '获取成功',
        data: order,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '订单不存在',
        data: null,
      });
    }
  },

  // 创建订单
  'POST /api/orders': (req: Request, res: Response) => {
    const newOrder = req.body;
    const newId = Math.max(...orders.map((o) => o.id), 0) + 1;

    // 查找用户信息
    const user = users.find((u) => u.id === newOrder.userId);
    if (!user) {
      return res.status(400).json({
        status: 400,
        message: '用户不存在',
        data: null,
      });
    }

    // 处理订单项
    const processedItems = newOrder.items.map((item: any, index: number) => {
      const iceType = iceTypes.find((t) => t.id === item.iceTypeId);
      return {
        id: index + 1,
        iceTypeId: item.iceTypeId,
        iceTypeName: iceType ? iceType.name : '未知冰块类型',
        quantity: item.quantity,
        size: item.size,
      };
    });

    const now = new Date().toISOString();
    const orderWithId = {
      ...newOrder,
      id: newId,
      username: user.username,
      items: processedItems,
      status: newOrder.status || OrderStatus.PENDING,
      createdAt: now,
      updatedAt: now,
    };

    orders.push(orderWithId);

    res.status(201).json({
      status: 201,
      message: '创建成功',
      data: orderWithId,
    });
  },

  // 更新订单
  'PUT /api/orders/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const updateData = req.body;
    const index = orders.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      // 如果更新了用户ID，需要更新用户名
      if (updateData.userId) {
        const user = users.find((u) => u.id === updateData.userId);
        if (user) {
          updateData.username = user.username;
        }
      }

      // 如果更新了订单项，需要处理冰块类型名称
      if (updateData.items) {
        updateData.items = updateData.items.map((item: any, idx: number) => {
          const iceType = iceTypes.find((t) => t.id === item.iceTypeId);
          return {
            id: idx + 1,
            iceTypeId: item.iceTypeId,
            iceTypeName: iceType ? iceType.name : '未知冰块类型',
            quantity: item.quantity,
            size: item.size,
          };
        });
      }

      orders[index] = {
        ...orders[index],
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      res.json({
        status: 200,
        message: '更新成功',
        data: orders[index],
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '订单不存在',
        data: null,
      });
    }
  },

  // 更新订单状态
  'PUT /api/orders/:id/status': (req: Request, res: Response) => {
    const { id } = req.params;
    const { status } = req.query;
    const index = orders.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      if (Object.values(OrderStatus).includes(status as OrderStatus)) {
        orders[index] = {
          ...orders[index],
          status: status as OrderStatus,
          updatedAt: new Date().toISOString(),
        };

        res.json({
          status: 200,
          message: '状态更新成功',
          data: orders[index],
        });
      } else {
        res.status(400).json({
          status: 400,
          message: '无效的订单状态',
          data: null,
        });
      }
    } else {
      res.status(404).json({
        status: 404,
        message: '订单不存在',
        data: null,
      });
    }
  },

  // 删除订单
  'DELETE /api/orders/:id': (req: Request, res: Response) => {
    const { id } = req.params;
    const index = orders.findIndex((item) => item.id === parseInt(id));

    if (index !== -1) {
      const deletedOrder = orders[index];
      orders.splice(index, 1);

      res.json({
        status: 200,
        message: '删除成功',
        data: deletedOrder,
      });
    } else {
      res.status(404).json({
        status: 404,
        message: '订单不存在',
        data: null,
      });
    }
  },
};

// 合并所有API
export default {
  ...userAPI,
  ...iceTypeAPI,
  ...orderAPI,
};
