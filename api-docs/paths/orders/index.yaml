/api/orders:
  get:
    tags: [Orders]
    summary: 获取订单列表
    parameters:
      - in: query
        name: userId
        schema:
          type: integer
          format: int64
        description: 过滤指定用户的订单
    responses:
      200:
        description: 成功获取订单列表
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/orders.yaml#/OrderList'
  post:
    tags: [Orders]
    summary: 创建订单
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/orders.yaml#/CreateOrderRequest'
    responses:
      200:
        description: 订单创建成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/orders.yaml#/OrderDetail'

/api/orders/{id}:
  get:
    tags: [Orders]
    summary: 获取订单详情
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 订单ID
    responses:
      200:
        description: 成功获取订单详情
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/orders.yaml#/OrderDetail'
  put:
    tags: [Orders]
    summary: 更新订单
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 订单ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/orders.yaml#/Order'
    responses:
      200:
        description: 订单更新成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/orders.yaml#/OrderDetail'
  delete:
    tags: [Orders]
    summary: 删除订单
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 订单ID
    responses:
      200:
        description: 订单删除成功

/api/orders/{id}/status:
  put:
    tags: [Orders]
    summary: 更新订单状态
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 订单ID
      - in: query
        name: status
        required: true
        schema:
          $ref: '../../components/schemas/orders.yaml#/OrderStatus'
        description: 新状态值
    responses:
      200:
        description: 订单状态更新成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/orders.yaml#/OrderDetail'
