/api/permission/users:
  get:
    tags: [Permission]
    summary: 获取权限用户列表
    description: 获取系统中的用户列表，支持分页和过滤
    parameters:
      - in: query
        name: page
        schema:
          type: integer
          default: 1
        description: 页码
      - in: query
        name: pageSize
        schema:
          type: integer
          default: 10
        description: 每页数量
      - in: query
        name: username
        schema:
          type: string
        description: 用户名模糊搜索
      - in: query
        name: status
        schema:
          type: string
          enum: [active, disabled]
        description: 按状态过滤
    responses:
      200:
        description: 成功获取用户列表
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/permission.yaml#/UserListResponse'
  post:
    tags: [Permission]
    summary: 创建权限用户
    description: 创建新用户
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/permission.yaml#/CreateUserRequest'
    responses:
      200:
        description: 用户创建成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/permission.yaml#/PermissionUser'

/api/permission/roles:
  get:
    tags: [Permission]
    summary: 获取角色列表
    description: 获取系统中的角色列表，支持分页
    parameters:
      - in: query
        name: page
        schema:
          type: integer
          default: 1
        description: 页码
      - in: query
        name: pageSize
        schema:
          type: integer
          default: 10
        description: 每页数量
    responses:
      200:
        description: 成功获取角色列表
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/permission.yaml#/RoleListResponse'

/api/permission/roles/{id}:
  put:
    tags: [Permission]
    summary: 更新角色
    description: 更新指定角色的信息，支持部分更新
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
        description: 角色ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/permission.yaml#/Role'
    responses:
      200:
        description: 角色更新成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/permission.yaml#/Role'

/api/permission/permissions:
  get:
    tags: [Permission]
    summary: 获取权限列表
    description: 获取系统中的所有权限
    responses:
      200:
        description: 成功获取权限列表
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/permission.yaml#/PermissionListResponse'
