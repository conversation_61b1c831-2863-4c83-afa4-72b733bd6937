/api/users:
  get:
    tags: [Users]
    summary: 获取用户列表
    description: 获取系统中的用户列表，支持分页和筛选
    parameters:
      - in: query
        name: page
        schema:
          type: integer
          default: 1
        description: 页码
      - in: query
        name: pageSize
        schema:
          type: integer
          default: 10
        description: 每页数量
      - in: query
        name: username
        schema:
          type: string
        description: 用户名模糊搜索
      - in: query
        name: email
        schema:
          type: string
        description: 邮箱模糊搜索
    responses:
      200:
        description: 成功获取用户列表
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/UserList'
  post:
    tags: [Users]
    summary: 创建用户
    description: 创建新用户
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/users.yaml#/CreateUserRequest'
    responses:
      200:
        description: 用户创建成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/UserDetail'

/api/users/{id}:
  get:
    tags: [Users]
    summary: 获取用户详情
    description: 获取指定用户的详细信息
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 用户ID
    responses:
      200:
        description: 成功获取用户详情
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/UserDetail'
  put:
    tags: [Users]
    summary: 更新用户信息
    description: 更新指定用户的信息
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 用户ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/users.yaml#/UpdateUserRequest'
    responses:
      200:
        description: 用户信息更新成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/UserDetail'
  delete:
    tags: [Users]
    summary: 删除用户
    description: 删除指定用户
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: integer
          format: int64
        description: 用户ID
    responses:
      200:
        description: 用户删除成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/CommonResponse'

/api/users/batch:
  delete:
    tags: [Users]
    summary: 批量删除用户
    description: 批量删除多个用户
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              ids:
                type: array
                items:
                  type: integer
                  format: int64
                description: 用户ID数组
    responses:
      200:
        description: 批量删除成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/users.yaml#/CommonResponse'
