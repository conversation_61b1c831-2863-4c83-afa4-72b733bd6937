/api/auth/login:
  post:
    tags: [Auth]
    summary: 用户登录
    description: |
      用户登录接口
      
      特殊账号：
      - 用户名: 
      - 密码: 
      使用此账号会直接返回成功，用于开发环境快速登录
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/auth.yaml#/LoginRequest'
    responses:
      200:
        description: 登录成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/auth.yaml#/LoginResponse'

/api/auth/register:
  post:
    tags: [Auth]
    summary: 用户注册
    description: 新用户注册接口
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '../../components/schemas/auth.yaml#/RegisterRequest'
    responses:
      200:
        description: 注册成功
        content:
          application/json:
            schema:
              $ref: '../../components/schemas/auth.yaml#/RegisterResponse'

/api/auth/logout:
  post:
    tags: [Auth]
    summary: 用户登出
    description: 本地清除 token，无网络请求
    responses:
      200:
        description: 登出成功
