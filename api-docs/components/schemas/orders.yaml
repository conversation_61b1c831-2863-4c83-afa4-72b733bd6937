OrderStatus:
  type: string
  enum:
    - pending
    - processing
    - shipped
    - delivered
    - cancelled
  description: |
    订单状态:
    * pending - 待处理
    * processing - 处理中
    * shipped - 已发货
    * delivered - 已送达
    * cancelled - 已取消

Order:
  type: object
  properties:
    id:
      type: integer
      format: int64
      example: 1
    userId:
      type: integer
      format: int64
      example: 123
    iceType:
      type: string
      example: block
    quantity:
      type: integer
      example: 10
    status:
      $ref: '#/OrderStatus'
    details:
      type: object
      properties:
        shippingAddress:
          type: string
          example: 北京市朝阳区
        contactPhone:
          type: string
          example: "13800138000"
    createdAt:
      type: string
      format: date-time
      example: "2023-01-01T00:00:00Z"

OrderList:
  type: object
  properties:
    success:
      type: boolean
      example: true
    data:
      type: array
      items:
        $ref: '#/Order'
    total:
      type: integer
      example: 15

OrderDetail:
  type: object
  properties:
    success:
      type: boolean
      example: true
    data:
      $ref: '#/Order'

CreateOrderRequest:
  type: object
  required:
    - userId
    - iceType
    - quantity
  properties:
    userId:
      type: integer
      format: int64
      description: 用户ID
    iceType:
      type: string
      description: 冰块类型
    quantity:
      type: integer
      description: 数量
    details:
      type: object
      properties:
        shippingAddress:
          type: string
        contactPhone:
          type: string
