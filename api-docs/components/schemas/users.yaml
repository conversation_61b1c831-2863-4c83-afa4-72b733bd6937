User:
  type: object
  properties:
    id:
      type: integer
      format: int64
      example: 1
    username:
      type: string
      example: "john_doe"
    email:
      type: string
      format: email
      example: "<EMAIL>"
    phone:
      type: string
      example: "13800138000"
    address:
      type: string
      example: "北京市朝阳区"
    createdAt:
      type: string
      format: date-time
      example: "2023-01-01T00:00:00Z"
    updatedAt:
      type: string
      format: date-time
      example: "2023-01-01T00:00:00Z"

UserList:
  type: object
  properties:
    success:
      type: boolean
      example: true
    data:
      type: array
      items:
        $ref: '#/User'
    total:
      type: integer
      example: 100
    page:
      type: integer
      example: 1
    pageSize:
      type: integer
      example: 10

UserDetail:
  type: object
  properties:
    success:
      type: boolean
      example: true
    data:
      $ref: '#/User'

CreateUserRequest:
  type: object
  required:
    - username
    - email
  properties:
    username:
      type: string
      description: 用户名
    email:
      type: string
      format: email
      description: 邮箱
    phone:
      type: string
      description: 电话号码
    address:
      type: string
      description: 地址

UpdateUserRequest:
  type: object
  properties:
    username:
      type: string
      description: 用户名
    email:
      type: string
      format: email
      description: 邮箱
    phone:
      type: string
      description: 电话号码
    address:
      type: string
      description: 地址

CommonResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: "操作成功"
