LoginRequest:
  type: object
  required:
    - username
    - password
  properties:
    username:
      type: string
      description: 用户名
      example: frost-chain-admin
    password:
      type: string
      description: 密码
      example: frost-chain-admin

LoginResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    token:
      type: string
      example: jwt-token-string

RegisterRequest:
  type: object
  required:
    - username
    - password
    - confirmPassword
  properties:
    username:
      type: string
      description: 用户名
    password:
      type: string
      description: 密码
    confirmPassword:
      type: string
      description: 确认密码

RegisterResponse:
  type: object
  properties:
    success:
      type: boolean
      example: true
    message:
      type: string
      example: 注册成功
