PermissionUser:
  type: object
  properties:
    id:
      type: string
      description: 用户ID
      example: "1"
    username:
      type: string
      description: 用户名
      example: "admin"
    email:
      type: string
      description: 邮箱
      example: "<EMAIL>"
    roles:
      type: array
      description: 角色ID数组
      items:
        type: string
      example: ["1"]
    status:
      type: string
      description: 用户状态
      enum: [active, disabled]
      example: "active"

Role:
  type: object
  properties:
    id:
      type: string
      description: 角色ID
      example: "1"
    name:
      type: string
      description: 角色名称
      example: "管理员"
    description:
      type: string
      description: 角色描述
      example: "系统管理员"
    permissions:
      type: array
      description: 权限ID数组
      items:
        type: string
      example: ["1", "2"]

Permission:
  type: object
  properties:
    id:
      type: string
      description: 权限ID
      example: "1"
    name:
      type: string
      description: 权限名称
      example: "创建用户"
    code:
      type: string
      description: 权限代码
      example: "user:create"
    description:
      type: string
      description: 权限描述
      example: "允许创建新用户"

UserListResponse:
  type: object
  properties:
    data:
      type: array
      items:
        $ref: '#/PermissionUser'
    total:
      type: integer
      example: 10

RoleListResponse:
  type: object
  properties:
    data:
      type: array
      items:
        $ref: '#/Role'
    total:
      type: integer
      example: 5

PermissionListResponse:
  type: object
  properties:
    data:
      type: array
      items:
        $ref: '#/Permission'
    total:
      type: integer
      example: 20

CreateUserRequest:
  type: object
  required:
    - username
    - email
  properties:
    username:
      type: string
      description: 用户名
    email:
      type: string
      description: 邮箱
    roles:
      type: array
      description: 角色ID数组
      items:
        type: string
    status:
      type: string
      description: 用户状态
      enum: [active, disabled]
      default: active
