openapi: 3.0.0
info:
  title: Frost Logix Admin API
  description: API documentation for Front Logix Admin system
  version: 1.0.0
  contact:
    name: Starrier Team
    email: <EMAIL>

servers:
  - url: https://api-docs.starrier.org
    description: Production server
  - url: http://localhost:3000
    description: Development server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: 用户名
          example: frost-chain-admin
        password:
          type: string
          description: 密码
          example: frost-chain-admin
    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        token:
          type: string
          example: jwt-token-string
    RegisterRequest:
      type: object
      required:
        - username
        - password
        - confirmPassword
      properties:
        username:
          type: string
          description: 用户名
        password:
          type: string
          description: 密码
        confirmPassword:
          type: string
          description: 确认密码
    RegisterResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: 注册成功
    OrderStatus:
      type: string
      enum:
        - pending
        - processing
        - shipped
        - delivered
        - cancelled
      description: |
        订单状态:
        * pending - 待处理
        * processing - 处理中
        * shipped - 已发货
        * delivered - 已送达
        * cancelled - 已取消
    Order:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        userId:
          type: integer
          format: int64
          example: 123
        iceType:
          type: string
          example: block
        quantity:
          type: integer
          example: 10
        status:
          $ref: '#/components/schemas/OrderStatus'
        details:
          type: object
          properties:
            shippingAddress:
              type: string
              example: 北京市朝阳区
            contactPhone:
              type: string
              example: "13800138000"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00Z"
    OrderList:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/Order'
        total:
          type: integer
          example: 15
    OrderDetail:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/Order'
    CreateOrderRequest:
      type: object
      required:
        - userId
        - iceType
        - quantity
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
        iceType:
          type: string
          description: 冰块类型
        quantity:
          type: integer
          description: 数量
        details:
          type: object
          properties:
            shippingAddress:
              type: string
            contactPhone:
              type: string
    PermissionUser:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
          example: "1"
        username:
          type: string
          description: 用户名
          example: "admin"
        email:
          type: string
          description: 邮箱
          example: "<EMAIL>"
        roles:
          type: array
          description: 角色ID数组
          items:
            type: string
          example: ["1"]
        status:
          type: string
          description: 用户状态
          enum: [active, disabled]
          example: "active"
    Role:
      type: object
      properties:
        id:
          type: string
          description: 角色ID
          example: "1"
        name:
          type: string
          description: 角色名称
          example: "管理员"
        description:
          type: string
          description: 角色描述
          example: "系统管理员"
        permissions:
          type: array
          description: 权限ID数组
          items:
            type: string
          example: ["1", "2"]
    Permission:
      type: object
      properties:
        id:
          type: string
          description: 权限ID
          example: "1"
        name:
          type: string
          description: 权限名称
          example: "创建用户"
        code:
          type: string
          description: 权限代码
          example: "user:create"
        description:
          type: string
          description: 权限描述
          example: "允许创建新用户"
    UserListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PermissionUser'
        total:
          type: integer
          example: 10
    RoleListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        total:
          type: integer
          example: 5
    PermissionListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        total:
          type: integer
          example: 20
    CreateUserRequest:
      type: object
      required:
        - username
        - email
      properties:
        username:
          type: string
          description: 用户名
        email:
          type: string
          description: 邮箱
        roles:
          type: array
          description: 角色ID数组
          items:
            type: string
        status:
          type: string
          description: 用户状态
          enum: [active, disabled]
          default: active
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 用户ID
          example: 1
        username:
          type: string
          description: 用户名
          example: "user1"
        email:
          type: string
          description: 邮箱
          example: "<EMAIL>"
        phone:
          type: string
          description: 手机号(可选)
          example: "13800138000"
        status:
          type: string
          description: 用户状态
          enum: [active, inactive]
          example: "active"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2023-01-01T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2023-01-02T00:00:00Z"
    UserListResponseData:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            list:
              type: array
              items:
                $ref: '#/components/schemas/User'
            total:
              type: integer
              example: 100
            currentPage:
              type: integer
              example: 1
    UserDetailResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          $ref: '#/components/schemas/User'
    CreateUserRequestBody:
      type: object
      required:
        - username
        - email
      properties:
        username:
          type: string
          description: 用户名
          example: "newuser"
        email:
          type: string
          description: 邮箱
          example: "<EMAIL>"
        phone:
          type: string
          description: 手机号
          example: "13800138000"
        status:
          type: string
          description: 状态
          enum: [active, inactive]
          default: active
    DeleteUserResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "用户删除成功"

paths:
  /api/auth/login:
    post:
      tags: [Auth]
      summary: 用户登录
      description: |
        用户登录接口
        
        特殊账号：
        - 用户名: frost-chain-admin
        - 密码: frost-chain-admin
        使用此账号会直接返回成功，用于开发环境快速登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        200:
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'

  /api/auth/register:
    post:
      tags: [Auth]
      summary: 用户注册
      description: 新用户注册接口
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        200:
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'

  /api/auth/logout:
    post:
      tags: [Auth]
      summary: 用户登出
      description: 本地清除 token，无网络请求
      responses:
        200:
          description: 登出成功

  /api/orders:
    get:
      tags: [Orders]
      summary: 获取订单列表
      parameters:
        - in: query
          name: userId
          schema:
            type: integer
            format: int64
          description: 过滤指定用户的订单
      responses:
        200:
          description: 成功获取订单列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderList'
    post:
      tags: [Orders]
      summary: 创建订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        200:
          description: 订单创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetail'

  /api/orders/{id}:
    get:
      tags: [Orders]
      summary: 获取订单详情
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 订单ID
      responses:
        200:
          description: 成功获取订单详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetail'
    put:
      tags: [Orders]
      summary: 更新订单
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 订单ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Order'
      responses:
        200:
          description: 订单更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetail'
    delete:
      tags: [Orders]
      summary: 删除订单
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 订单ID
      responses:
        200:
          description: 订单删除成功

  /api/orders/{id}/status:
    put:
      tags: [Orders]
      summary: 更新订单状态
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 订单ID
        - in: query
          name: status
          required: true
          schema:
            $ref: '#/components/schemas/OrderStatus'
          description: 新状态值
      responses:
        200:
          description: 订单状态更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetail'

  /api/permission/users:
    get:
      tags: [Permission]
      summary: 获取权限用户列表
      description: 获取系统中的用户列表，支持分页和过滤
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: 页码
        - in: query
          name: pageSize
          schema:
            type: integer
            default: 10
          description: 每页数量
        - in: query
          name: username
          schema:
            type: string
          description: 用户名模糊搜索
        - in: query
          name: status
          schema:
            type: string
            enum: [active, disabled]
          description: 按状态过滤
      responses:
        200:
          description: 成功获取用户列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'
    post:
      tags: [Permission]
      summary: 创建权限用户
      description: 创建新用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        200:
          description: 用户创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PermissionUser'

  /api/permission/roles:
    get:
      tags: [Permission]
      summary: 获取角色列表
      description: 获取系统中的角色列表，支持分页
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: 页码
        - in: query
          name: pageSize
          schema:
            type: integer
            default: 10
          description: 每页数量
      responses:
        200:
          description: 成功获取角色列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleListResponse'

  /api/permission/roles/{id}:
    put:
      tags: [Permission]
      summary: 更新角色
      description: 更新指定角色的信息，支持部分更新
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: 角色ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Role'
      responses:
        200:
          description: 角色更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'

  /api/permission/permissions:
    get:
      tags: [Permission]
      summary: 获取权限列表
      description: 获取系统中的所有权限
      responses:
        200:
          description: 成功获取权限列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PermissionListResponse'

  /api/users:
    get:
      tags: [Users]
      summary: 获取用户列表
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            default: 1
          description: 页码
        - in: query
          name: pageSize
          schema:
            type: integer
            default: 10
          description: 每页数量
        - in: query
          name: username
          schema:
            type: string
          description: 用户名模糊搜索
        - in: query
          name: status
          schema:
            type: string
            enum: [active, inactive]
          description: 按状态过滤
      responses:
        200:
          description: 成功获取用户列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponseData'
    post:
      tags: [Users]
      summary: 创建用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequestBody'
      responses:
        200:
          description: 用户创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailResponse'

  /api/users/{id}:
    get:
      tags: [Users]
      summary: 获取用户详情
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 用户ID
      responses:
        200:
          description: 成功获取用户详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailResponse'
    put:
      tags: [Users]
      summary: 更新用户
      description: 支持部分更新，可只传需要修改的字段
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 用户ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequestBody'
      responses:
        200:
          description: 用户更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailResponse'
    delete:
      tags: [Users]
      summary: 删除用户
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
          description: 用户ID
      responses:
        200:
          description: 用户删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteUserResponse'
