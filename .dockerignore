# 版本控制
.git
.github
.gitignore

# 依赖
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log
node_modules/.cache
.npm
.yarn
.pnpm-store
*.log
*.gz

# 构建产物
/dist
/.umi
/.umi-production
/.umi-test
/src/.umi
/src/.umi-production
/src/.umi-test
/.mfsu

# 开发工具
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 本地环境文件
.env.local
.env.*.local

# 文档
README.md
CHANGELOG.md
docs

# 测试
coverage
/cypress
/e2e
*.spec.js
*.test.js

# 其他
.DS_Store
Thumbs.db
