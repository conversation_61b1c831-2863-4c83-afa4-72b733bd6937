# Front-Logix-Admin 接口文档

## 概述

本文档详细描述了 Front-Logix-Admin 系统中产品管理模块所需的后端接口规范。这些接口用于支持前端页面对产品信息的增删改查操作，供后端开发人员参考实现。

## 基础信息

- **基础 URL**: `/api`
- **数据格式**: JSON
- **认证方式**: Bearer Token
- **请求头**:
  ```
  Authorization: Bearer {token}
  Content-Type: application/json
  ```

## 通用响应格式

所有接口返回的数据结构如下：

```json
{
  "status": 200, // 状态码，200表示成功，其他值表示失败
  "message": "成功", // 状态描述
  "data": {} // 响应数据，可能是对象、数组或null
}
```

## 错误码说明

| 状态码 | 说明                |
| ------ | ------------------- |
| 200    | 请求成功            |
| 400    | 请求参数错误        |
| 401    | 未授权或 token 过期 |
| 403    | 权限不足            |
| 404    | 资源不存在          |
| 500    | 服务器内部错误      |

## 接口详情

### 1. 获取产品列表

获取所有产品的列表信息。

- **URL**: `/products`
- **方法**: `GET`
- **URL 参数**:

  - `page`: 页码，默认为 1
  - `pageSize`: 每页条数，默认为 10
  - `keyword`: 搜索关键词（可选）
  - `sortField`: 排序字段（可选）
  - `sortOrder`: 排序方式，`ascend`或`descend`（可选）

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "获取成功",
    "data": {
      "list": [
        {
          "id": 1,
          "title": "工业冰",
          "description": "我们的工业冰产品专为各种工业应用而设计，具有高纯度和耐用性，可满足最严格的工业标准。",
          "features": [
            "高纯度，无杂质",
            "定制尺寸和形状",
            "快速冷却效果",
            "长时间保持低温",
            "环保生产工艺"
          ],
          "applications": [
            "食品加工冷却",
            "混凝土温度控制",
            "医疗设备冷却",
            "化工过程温度调节",
            "冷链物流"
          ],
          "images": [
            {
              "id": 1,
              "src": "/img/industrial-ice-1.jpg",
              "alt": "工业冰块"
            },
            {
              "id": 2,
              "src": "/img/industrial-ice-2.jpg",
              "alt": "工业冰应用"
            }
          ],
          "specifications": {
            "纯度": "99.9%",
            "标准尺寸": "25mm × 25mm × 25mm",
            "融化时间": "标准条件下约4-6小时",
            "包装方式": "25kg/袋，1吨/托盘"
          }
        }
        // 更多产品...
      ],
      "total": 100,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 500,
    "message": "服务器内部错误",
    "data": null
  }
  ```

### 2. 获取产品详情

获取单个产品的详细信息。

- **URL**: `/products/:id`
- **方法**: `GET`
- **URL 参数**:

  - `id`: 产品 ID

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "获取成功",
    "data": {
      "id": 1,
      "title": "工业冰",
      "description": "我们的工业冰产品专为各种工业应用而设计，具有高纯度和耐用性，可满足最严格的工业标准。",
      "features": [
        "高纯度，无杂质",
        "定制尺寸和形状",
        "快速冷却效果",
        "长时间保持低温",
        "环保生产工艺"
      ],
      "applications": [
        "食品加工冷却",
        "混凝土温度控制",
        "医疗设备冷却",
        "化工过程温度调节",
        "冷链物流"
      ],
      "images": [
        {
          "id": 1,
          "src": "/img/industrial-ice-1.jpg",
          "alt": "工业冰块"
        },
        {
          "id": 2,
          "src": "/img/industrial-ice-2.jpg",
          "alt": "工业冰应用"
        }
      ],
      "specifications": {
        "纯度": "99.9%",
        "标准尺寸": "25mm × 25mm × 25mm",
        "融化时间": "标准条件下约4-6小时",
        "包装方式": "25kg/袋，1吨/托盘"
      }
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 404,
    "message": "产品不存在",
    "data": null
  }
  ```

### 3. 创建产品

创建一个新的产品。

- **URL**: `/products`
- **方法**: `POST`
- **请求体**:

  ```json
  {
    "title": "食用冰",
    "description": "我们的食用冰产品采用纯净水制作，符合食品级标准，适用于各种餐饮和饮料应用。",
    "features": [
      "食品级纯净水制作",
      "晶莹剔透，无气泡",
      "多种形状可选",
      "融化均匀，不影响饮品口感",
      "符合食品安全标准"
    ],
    "applications": [
      "餐厅和酒吧饮品",
      "咖啡厅冰饮",
      "酒店服务",
      "超市和便利店销售",
      "活动和宴会服务"
    ],
    "images": [
      {
        "src": "/img/food-ice-1.jpg",
        "alt": "食用冰块"
      },
      {
        "src": "/img/food-ice-2.jpg",
        "alt": "食用冰应用"
      }
    ],
    "specifications": {
      "水源": "纯净水/矿泉水",
      "形状": "方块、圆柱、球形、管状",
      "包装": "2kg/袋，10kg/箱",
      "储存条件": "-18°C以下"
    }
  }
  ```

- **成功响应**:

  ```json
  {
    "status": 201,
    "message": "创建成功",
    "data": {
      "id": 4,
      "title": "食用冰",
      "description": "我们的食用冰产品采用纯净水制作，符合食品级标准，适用于各种餐饮和饮料应用。",
      "features": [
        "食品级纯净水制作",
        "晶莹剔透，无气泡",
        "多种形状可选",
        "融化均匀，不影响饮品口感",
        "符合食品安全标准"
      ],
      "applications": [
        "餐厅和酒吧饮品",
        "咖啡厅冰饮",
        "酒店服务",
        "超市和便利店销售",
        "活动和宴会服务"
      ],
      "images": [
        {
          "id": 7,
          "src": "/img/food-ice-1.jpg",
          "alt": "食用冰块"
        },
        {
          "id": 8,
          "src": "/img/food-ice-2.jpg",
          "alt": "食用冰应用"
        }
      ],
      "specifications": {
        "水源": "纯净水/矿泉水",
        "形状": "方块、圆柱、球形、管状",
        "包装": "2kg/袋，10kg/箱",
        "储存条件": "-18°C以下"
      }
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 400,
    "message": "请求参数错误：产品标题不能为空",
    "data": null
  }
  ```

### 4. 更新产品

更新现有产品的信息。

- **URL**: `/products/:id`
- **方法**: `PUT`
- **URL 参数**:
  - `id`: 产品 ID
- **请求体**:

  ```json
  {
    "title": "高级食用冰",
    "description": "升级版食用冰产品，采用矿泉水制作，口感更佳。",
    "features": [
      "矿泉水制作",
      "晶莹剔透，无气泡",
      "多种形状可选",
      "融化均匀，不影响饮品口感",
      "符合食品安全标准",
      "更长保鲜时间"
    ],
    "applications": [
      "高端餐厅和酒吧饮品",
      "精品咖啡厅冰饮",
      "豪华酒店服务",
      "超市和便利店销售",
      "高端活动和宴会服务"
    ],
    "images": [
      {
        "id": 7,
        "src": "/img/food-ice-1.jpg",
        "alt": "高级食用冰块"
      },
      {
        "id": 8,
        "src": "/img/food-ice-2.jpg",
        "alt": "高级食用冰应用"
      },
      {
        "src": "/img/food-ice-3.jpg",
        "alt": "高级食用冰特写"
      }
    ],
    "specifications": {
      "水源": "天然矿泉水",
      "形状": "方块、圆柱、球形、管状、钻石形",
      "包装": "2kg/袋，10kg/箱，定制礼盒",
      "储存条件": "-18°C以下"
    }
  }
  ```

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "更新成功",
    "data": {
      "id": 4,
      "title": "高级食用冰",
      "description": "升级版食用冰产品，采用矿泉水制作，口感更佳。",
      "features": [
        "矿泉水制作",
        "晶莹剔透，无气泡",
        "多种形状可选",
        "融化均匀，不影响饮品口感",
        "符合食品安全标准",
        "更长保鲜时间"
      ],
      "applications": [
        "高端餐厅和酒吧饮品",
        "精品咖啡厅冰饮",
        "豪华酒店服务",
        "超市和便利店销售",
        "高端活动和宴会服务"
      ],
      "images": [
        {
          "id": 7,
          "src": "/img/food-ice-1.jpg",
          "alt": "高级食用冰块"
        },
        {
          "id": 8,
          "src": "/img/food-ice-2.jpg",
          "alt": "高级食用冰应用"
        },
        {
          "id": 9,
          "src": "/img/food-ice-3.jpg",
          "alt": "高级食用冰特写"
        }
      ],
      "specifications": {
        "水源": "天然矿泉水",
        "形状": "方块、圆柱、球形、管状、钻石形",
        "包装": "2kg/袋，10kg/箱，定制礼盒",
        "储存条件": "-18°C以下"
      }
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 404,
    "message": "产品不存在",
    "data": null
  }
  ```

### 5. 删除产品

删除指定的产品。

- **URL**: `/products/:id`
- **方法**: `DELETE`
- **URL 参数**:

  - `id`: 产品 ID

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "删除成功",
    "data": null
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 404,
    "message": "产品不存在",
    "data": null
  }
  ```

### 6. 上传产品图片

上传产品图片。

- **URL**: `/upload/product-image`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`
- **请求参数**:

  - `file`: 图片文件

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "上传成功",
    "data": {
      "id": 10,
      "src": "/img/uploads/product-image-1234567890.jpg",
      "alt": "product-image-1234567890.jpg"
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 400,
    "message": "上传失败：文件格式不支持，仅支持jpg、jpeg、png、gif格式",
    "data": null
  }
  ```

### 7. 批量删除产品

批量删除多个产品。

- **URL**: `/products/batch`
- **方法**: `DELETE`
- **请求体**:

  ```json
  {
    "ids": [1, 2, 3]
  }
  ```

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "批量删除成功",
    "data": {
      "successCount": 3,
      "failCount": 0
    }
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 400,
    "message": "请求参数错误：ids不能为空",
    "data": null
  }
  ```

### 8. 产品排序

更新产品的排序顺序。

- **URL**: `/products/sort`
- **方法**: `PUT`
- **请求体**:

  ```json
  {
    "sortList": [
      { "id": 3, "order": 1 },
      { "id": 1, "order": 2 },
      { "id": 2, "order": 3 }
    ]
  }
  ```

- **成功响应**:

  ```json
  {
    "status": 200,
    "message": "排序更新成功",
    "data": null
  }
  ```

- **错误响应**:
  ```json
  {
    "status": 400,
    "message": "请求参数错误：sortList不能为空",
    "data": null
  }
  ```

## 数据模型

### 产品(Product)

| 字段           | 类型                | 描述                         |
| -------------- | ------------------- | ---------------------------- |
| id             | Integer             | 产品 ID，自增主键            |
| title          | String              | 产品标题，不超过 100 个字符  |
| description    | String              | 产品描述，不超过 1000 个字符 |
| features       | Array<String>       | 产品特性列表                 |
| applications   | Array<String>       | 应用场景列表                 |
| images         | Array<ProductImage> | 产品图片列表                 |
| specifications | Object              | 产品规格，键值对形式         |
| order          | Integer             | 排序顺序，值越小越靠前       |
| createdAt      | DateTime            | 创建时间                     |
| updatedAt      | DateTime            | 更新时间                     |

### 产品图片(ProductImage)

| 字段      | 类型     | 描述                       |
| --------- | -------- | -------------------------- |
| id        | Integer  | 图片 ID，自增主键          |
| src       | String   | 图片 URL 路径              |
| alt       | String   | 图片替代文本               |
| productId | Integer  | 关联的产品 ID              |
| order     | Integer  | 图片排序顺序，值越小越靠前 |
| createdAt | DateTime | 创建时间                   |

## 接口安全性

1. **认证与授权**

   - 所有接口都需要通过 Bearer Token 认证
   - 不同角色用户对接口的访问权限不同

2. **输入验证**

   - 所有用户输入都需要进行验证，防止注入攻击
   - 图片上传需要验证文件类型和大小

3. **限流措施**
   - 对接口访问频率进行限制，防止恶意请求
   - 建议实现 IP 级别和用户级别的限流

## 开发建议

1. **错误处理**

   - 提供详细的错误信息，便于前端展示和调试
   - 对敏感错误信息进行脱敏处理

2. **日志记录**

   - 记录关键操作日志，如创建、更新、删除产品
   - 记录异常情况和错误信息

3. **事务处理**

   - 对涉及多表操作的接口使用数据库事务
   - 确保数据一致性和完整性

4. **缓存策略**
   - 对频繁访问的数据实施缓存策略
   - 产品列表和详情可以考虑使用缓存

## 接口变更记录

| 版本 | 日期       | 变更内容               | 负责人 |
| ---- | ---------- | ---------------------- | ------ |
| 1.0  | 2023-07-01 | 初始版本               | 张三   |
| 1.1  | 2023-08-15 | 增加批量删除和排序接口 | 李四   |
| 1.2  | 2023-09-20 | 优化产品图片上传接口   | 王五   |

---

如有任何疑问或需要进一步说明，请联系前端开发团队。
