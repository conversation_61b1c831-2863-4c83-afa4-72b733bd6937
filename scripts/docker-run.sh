#!/bin/bash

# 运行 Docker 容器的脚本
# 用法: ./scripts/docker-run.sh [环境]
# 示例: ./scripts/docker-run.sh production

# 设置默认环境
ENV=${1:-production}
REGISTRY="docker.io"
# 替换为你的Docker Hub用户名
DOCKERHUB_USERNAME="starrier"
REPOSITORY="$DOCKERHUB_USERNAME/frologi-admin"
CONTAINER_NAME="frologi-admin-$ENV"

# 验证环境参数
if [[ "$ENV" != "development" && "$ENV" != "test" && "$ENV" != "production" ]]; then
  echo "错误: 环境必须是 development, test 或 production"
  exit 1
fi

# 设置端口映射
case $ENV in
  development)
    PORT=8001
    ;;
  test)
    PORT=8002
    ;;
  production)
    PORT=8003
    ;;
esac

# 设置API URL
case $ENV in
  development)
    API_URL="http://localhost:3000"
    ;;
  test)
    API_URL="https://test-api.starrier.org"
    ;;
  production)
    API_URL="https://api.starrier.org"
    ;;
esac

# 检查容器是否已存在，如果存在则停止并删除
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
  echo "停止并删除已存在的容器: $CONTAINER_NAME"
  docker stop $CONTAINER_NAME
  docker rm $CONTAINER_NAME
fi

# 运行容器
echo "启动 $ENV 环境的容器..."
if [ "$ENV" == "development" ]; then
  # 开发环境使用卷挂载
  docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:8001 \
    -v $(pwd):/app \
    -v /app/node_modules \
    -e NODE_ENV=$ENV \
    -e UMI_ENV=$ENV \
    -e API_URL=$API_URL \
    $REGISTRY/$REPOSITORY:$ENV-latest
else
  # 测试和生产环境
  docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:80 \
    -e NODE_ENV=$ENV \
    -e API_URL=$API_URL \
    $REGISTRY/$REPOSITORY:$ENV-latest
fi

# 检查运行结果
if [ $? -eq 0 ]; then
  echo "容器启动成功!"
  echo "访问地址: http://localhost:$PORT"
  
  # 显示容器日志
  echo "容器日志:"
  docker logs -f $CONTAINER_NAME
else
  echo "容器启动失败!"
  exit 1
fi
