#!/bin/bash

# 批量迁移 enhanced 服务文件，从兼容层改为直接使用 axios

echo "开始迁移 enhanced 服务文件..."

# 要迁移的文件列表
files=(
  "src/services/iceOrder/enhanced/riskControlService.ts"
  "src/services/iceOrder/enhanced/enhancedIceTypeService.ts"
  "src/services/iceOrder/enhanced/reportService.ts"
  "src/services/iceOrder/enhanced/factoryService.ts"
  "src/services/iceOrder/enhanced/enhancedOrderService.ts"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "迁移文件: $file"
    
    # 替换导入语句
    sed -i '' 's/import { request } from '\''@\/utils\/requestCompat'\'';/import { get, post, put, del } from '\''@\/utils\/apiClient'\'';/' "$file"
    
    # 替换简单的 request 调用
    # GET 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\'',[[:space:]]*params,[[:space:]]*});/return get(\2, params);/g' "$file"
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\'',[[:space:]]*});/return get(\2);/g' "$file"
    
    # POST 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''POST'\'',[[:space:]]*data,[[:space:]]*});/return post(\2, data);/g' "$file"
    
    # PUT 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''PUT'\'',[[:space:]]*data,[[:space:]]*});/return put(\2, data);/g' "$file"
    
    # DELETE 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''DELETE'\'',[[:space:]]*});/return del(\2);/g' "$file"
    
    echo "✅ 完成: $file"
  else
    echo "❌ 文件不存在: $file"
  fi
done

echo "所有 enhanced 服务文件迁移完成！"
