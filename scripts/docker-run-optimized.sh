#!/bin/bash

# 运行优化的 Docker 镜像的脚本
# 用法: ./scripts/docker-run-optimized.sh [环境]
# 示例: ./scripts/docker-run-optimized.sh production

# 设置默认环境
ENV=${1:-production}

# 从.env文件读取应用名称
APP_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r')

# 将应用名称转换为小写并替换空格和特殊字符
IMAGE_NAME=$(echo "${APP_NAME}" | tr '[:upper:]' '[:lower:]' | tr ' -' '_')

# 设置容器名称
CONTAINER_NAME="${IMAGE_NAME}-optimized-${ENV}"

# 检查容器是否已存在，如果存在则停止并删除
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
  echo "停止并删除已存在的容器: $CONTAINER_NAME"
  docker stop $CONTAINER_NAME
  docker rm $CONTAINER_NAME
fi

# 设置端口映射
case $ENV in
  development)
    PORT=8081
    ;;
  test)
    PORT=8082
    ;;
  production)
    PORT=8083
    ;;
  *)
    PORT=8080
    ;;
esac

# 设置API URL
case $ENV in
  development)
    API_URL="http://localhost:3000"
    ;;
  test)
    API_URL="https://test-api.starrier.org"
    ;;
  production)
    API_URL="https://api.starrier.org"
    ;;
  *)
    API_URL="http://localhost:3000"
    ;;
esac

# 运行容器
echo "启动优化的 $ENV 环境的容器..."
docker run -d \
  --name $CONTAINER_NAME \
  -p $PORT:80 \
  -e API_URL=$API_URL \
  --restart unless-stopped \
  --memory=256m \
  --cpus=0.5 \
  $IMAGE_NAME:$ENV

# 检查运行结果
if [ $? -eq 0 ]; then
  echo "容器启动成功!"
  echo "访问地址: http://localhost:$PORT"
  
  # 显示容器信息
  echo "容器信息:"
  docker ps -f name=$CONTAINER_NAME
  
  # 显示容器大小
  echo "容器大小:"
  docker ps --size -f name=$CONTAINER_NAME
  
  # 显示容器日志
  echo "容器日志:"
  docker logs -f $CONTAINER_NAME
else
  echo "容器启动失败!"
  exit 1
fi
