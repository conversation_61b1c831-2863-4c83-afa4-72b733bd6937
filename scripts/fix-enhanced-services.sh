#!/bin/bash

# 修复 enhanced 服务文件中的 request 调用

echo "开始修复 enhanced 服务文件..."

# 要修复的文件列表
files=(
  "src/services/iceOrder/enhanced/factoryService.ts"
  "src/services/iceOrder/enhanced/reportService.ts"
  "src/services/iceOrder/enhanced/riskControlService.ts"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "修复文件: $file"
    
    # 替换 request 调用为对应的 HTTP 方法
    # GET 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\'',[[:space:]]*params,[[:space:]]*});/return get(\2, params);/g' "$file"
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\'',[[:space:]]*});/return get(\2);/g' "$file"
    
    # POST 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''POST'\'',[[:space:]]*data,[[:space:]]*});/return post(\2, data);/g' "$file"
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''POST'\'',[[:space:]]*});/return post(\2, {});/g' "$file"
    
    # PUT 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''PUT'\'',[[:space:]]*data,[[:space:]]*});/return put(\2, data);/g' "$file"
    
    # DELETE 请求
    sed -i '' 's/return request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''DELETE'\'',[[:space:]]*});/return del(\2);/g' "$file"
    
    # 添加返回类型注解
    sed -i '' 's/export async function \([^(]*\)(\([^)]*\)) {/export async function \1(\2): Promise<ApiResponse<any>> {/g' "$file"
    
    echo "✅ 完成: $file"
  else
    echo "❌ 文件不存在: $file"
  fi
done

echo "所有 enhanced 服务文件修复完成！"
