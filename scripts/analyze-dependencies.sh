#!/bin/bash

# 依赖分析和优化脚本

echo "===== 依赖分析和优化 ====="

# 显示当前依赖大小
echo "当前 node_modules 大小:"
du -sh node_modules

# 查找最大的依赖
echo -e "\n最大的 10 个依赖:"
find node_modules -type d -maxdepth 2 -not -path "*/\.*" | xargs du -sh 2>/dev/null | sort -hr | head -10

# 查找重复的依赖
echo -e "\n查找重复的依赖:"
npx depcheck

# 检查未使用的依赖
echo -e "\n检查未使用的依赖:"
npx depcheck --ignores="@types/*,prettier-*"

# 检查可以移动到 devDependencies 的依赖
echo -e "\n可以移动到 devDependencies 的依赖:"
npx depcheck --ignores="@types/*,prettier-*" | grep "* " | grep -v "devDependencies"

# 建议
echo -e "\n===== 优化建议 ====="
echo "1. 考虑使用 pnpm 替代 yarn/npm 来减少依赖大小"
echo "2. 移除未使用的依赖"
echo "3. 将开发依赖从 dependencies 移动到 devDependencies"
echo "4. 考虑使用 CDN 加载大型第三方库"
echo "5. 使用动态导入（懒加载）加载大型组件"
echo "6. 考虑使用更轻量级的替代库"
