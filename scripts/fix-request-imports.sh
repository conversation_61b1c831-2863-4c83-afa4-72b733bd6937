#!/bin/bash

# 修复所有使用 umi-request 的文件
# 将 import { request } from '@umijs/max' 替换为 axios 客户端

echo "开始修复 request 导入..."

# 要修复的文件列表
files=(
  "src/services/iceOrder/enhanced/riskControlService.ts"
  "src/services/iceOrder/enhanced/enhancedIceTypeService.ts"
  "src/services/iceOrder/enhanced/reportService.ts"
  "src/services/iceOrder/enhanced/enhancedUserService.ts"
  "src/services/iceOrder/enhanced/enhancedOrderService.ts"
  "src/services/iceOrder/enhanced/factoryService.ts"
  "src/services/product/index.ts"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "修复文件: $file"
    
    # 替换导入语句
    sed -i '' 's/import { request } from '\''@umijs\/max'\'';/import { get, post, put, del } from '\''@\/utils\/apiClient'\'';/' "$file"
    
    # 替换 request 调用为对应的 axios 方法
    # GET 请求
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\'',[[:space:]]*params,/get(\2, params/g' "$file"
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''GET'\''/get(\2/g' "$file"
    
    # POST 请求
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''POST'\'',[[:space:]]*data,/post(\2, data/g' "$file"
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''POST'\''/post(\2/g' "$file"
    
    # PUT 请求
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''PUT'\'',[[:space:]]*data,/put(\2, data/g' "$file"
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''PUT'\''/put(\2/g' "$file"
    
    # DELETE 请求
    sed -i '' 's/request<\([^>]*\)>(\([^,]*\), {[[:space:]]*method: '\''DELETE'\''/del(\2/g' "$file"
    
    echo "✅ 完成: $file"
  else
    echo "❌ 文件不存在: $file"
  fi
done

echo "所有文件修复完成！"
