#!/bin/bash

# 优化的 Docker 构建脚本
# 用法: ./scripts/docker-build-optimized.sh [环境]
# 示例: ./scripts/docker-build-optimized.sh production

# 设置默认环境
ENV=${1:-production}

# 从.env文件读取应用名称
APP_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r')

# 将应用名称转换为小写并替换空格和特殊字符
IMAGE_NAME=$(echo "${APP_NAME}" | tr '[:upper:]' '[:lower:]' | tr ' -' '_')

# 设置镜像信息
REGISTRY="docker.io"
REPOSITORY="starrier/${IMAGE_NAME}"
TAG="0.0.1"
LOCAL_REPOSITORY="${IMAGE_NAME}"

# 验证环境参数
if [[ "$ENV" != "development" && "$ENV" != "test" && "$ENV" != "production" ]]; then
  echo "错误: 环境必须是 development, test 或 production"
  exit 1
fi

# 启用 BuildKit
export DOCKER_BUILDKIT=1

# 构建镜像
echo "开始构建 $ENV 环境的优化 Docker 镜像..."

# 构建本地镜像
echo "构建本地镜像..."
docker build \
  --build-arg BUILD_ENV=$ENV \
  -t $LOCAL_REPOSITORY:$ENV \
  -t $LOCAL_REPOSITORY:latest \
  -f Dockerfile.optimized \
  --no-cache \
  --progress=plain \
  .

# 检查构建结果
if [ $? -eq 0 ]; then
  echo "构建成功!"
  
  # 显示镜像大小
  echo "镜像大小:"
  docker images $LOCAL_REPOSITORY:$ENV --format "{{.Size}}"
  
  # 询问是否构建远程仓库镜像
  read -p "是否构建远程仓库镜像? (y/n): " BUILD_REMOTE_INPUT
  if [[ "$BUILD_REMOTE_INPUT" == "y" || "$BUILD_REMOTE_INPUT" == "Y" ]]; then
    echo "为远程仓库添加标签..."
    docker tag $LOCAL_REPOSITORY:$ENV $REGISTRY/$REPOSITORY:$ENV-$TAG
    docker tag $LOCAL_REPOSITORY:$ENV $REGISTRY/$REPOSITORY:$ENV-latest
    docker tag $LOCAL_REPOSITORY:latest $REGISTRY/$REPOSITORY:$TAG
    
    if [[ "$ENV" == "production" ]]; then
      docker tag $LOCAL_REPOSITORY:latest $REGISTRY/$REPOSITORY:latest
    fi
    
    # 询问是否推送镜像
    read -p "是否推送镜像到仓库? (y/n): " PUSH_IMAGE
    if [[ "$PUSH_IMAGE" == "y" || "$PUSH_IMAGE" == "Y" ]]; then
      echo "推送镜像到仓库..."
      docker push $REGISTRY/$REPOSITORY:$ENV-$TAG
      docker push $REGISTRY/$REPOSITORY:$ENV-latest
      docker push $REGISTRY/$REPOSITORY:$TAG
      
      # 如果是生产环境，也推送latest标签
      if [[ "$ENV" == "production" ]]; then
        docker push $REGISTRY/$REPOSITORY:latest
      fi
      
      echo "推送完成!"
    else
      echo "跳过推送镜像."
    fi
  fi
  
  # 显示本地运行命令
  echo ""
  echo "本地运行命令:"
  echo "  docker run -p 8080:80 $LOCAL_REPOSITORY:$ENV"
  echo "或者:"
  echo "  docker run -p 8080:80 $LOCAL_REPOSITORY:latest"
else
  echo "构建失败!"
  exit 1
fi
