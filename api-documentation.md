# 寒链项目 API 接口文档

## 目录

1. [概述](#概述)
2. [通用规范](#通用规范)
3. [认证接口](#认证接口)
4. [产品接口](#产品接口)
5. [特性接口](#特性接口)
6. [设置接口](#设置接口)
7. [分析接口](#分析接口)

## 概述

本文档描述了寒链项目前端与后端之间的 API 接口规范。所有接口均采用 RESTful 风格设计，使用 JSON 格式进行数据交换。

### 基础 URL

- 开发环境：`http://localhost:3000`
- 生产环境：`https://api.starrier.org`

### 通用响应格式

```json
{
  "status": 200,
  "message": "操作成功",
  "data": { ... }
}
```

## 通用规范

### 状态码

| 状态码 | 描述       |
| ------ | ---------- |
| 200    | 成功       |
| 400    | 请求错误   |
| 401    | 未授权     |
| 403    | 禁止访问   |
| 404    | 资源不存在 |
| 500    | 服务器错误 |

### 认证方式

API 使用 Bearer Token 认证：

```
Authorization: Bearer {token}
```

## 认证接口

### 用户登录

- **URL**: `/auth/signin`
- **方法**: `POST`
- **描述**: 用户登录并获取认证令牌

#### 请求参数

```json
{
  "username": "admin",
  "password": "password123"
}
```

#### 响应结果

```json
{
  "status": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": "1",
      "username": "admin",
      "email": "<EMAIL>",
      "avatar": "https://randomuser.me/api/portraits/men/1.jpg",
      "role": "admin",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

### 用户注册

- **URL**: `/auth/signup`
- **方法**: `POST`
- **描述**: 注册新用户

#### 请求参数

```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 响应结果

```json
{
  "status": 200,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": "2",
      "username": "newuser",
      "email": "<EMAIL>",
      "avatar": null,
      "role": "user",
      "createdAt": "2023-06-15T10:30:00Z",
      "updatedAt": "2023-06-15T10:30:00Z"
    }
  }
}
```

### 登出

- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户登出，使当前令牌失效
- **认证**: 需要

#### 响应结果

```json
{
  "status": 200,
  "message": "登出成功",
  "data": null
}
```

### 刷新令牌

- **URL**: `/auth/refresh`
- **方法**: `POST`
- **描述**: 刷新认证令牌
- **认证**: 需要

#### 响应结果

```json
{
  "status": 200,
  "message": "令牌刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  }
}
```

## 产品接口

### 获取产品列表

- **URL**: `/api/products`
- **方法**: `GET`
- **描述**: 获取所有产品列表

#### 响应结果

```json
{
  "status": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "工业冰",
      "description": "我们的工业冰产品专为各种工业应用而设计，具有高纯度和耐用性，可满足最严格的工业标准。",
      "features": [
        "高纯度，无杂质",
        "定制尺寸和形状",
        "快速冷却效果",
        "长时间保持低温",
        "环保生产工艺"
      ],
      "applications": [
        "食品加工冷却",
        "混凝土温度控制",
        "医疗设备冷却",
        "化工过程温度调节",
        "冷链物流"
      ],
      "images": [
        { "id": 1, "src": "/img/industrial-ice-1.jpg", "alt": "工业冰块" },
        { "id": 2, "src": "/img/industrial-ice-2.jpg", "alt": "工业冰应用" }
      ],
      "specifications": {
        "纯度": "99.9%",
        "标准尺寸": "25mm × 25mm × 25mm",
        "融化时间": "标准条件下约4-6小时",
        "包装方式": "25kg/袋，1吨/托盘"
      }
    },
    {
      "id": 2,
      "title": "食用冰",
      "description": "我们的食用冰产品采用纯净水制作，符合食品级标准，适用于各种餐饮和饮料应用。",
      "features": [
        "食品级纯净水制作",
        "晶莹剔透，无气泡",
        "多种形状可选",
        "融化均匀，不影响饮品口感",
        "符合食品安全标准"
      ],
      "applications": [
        "餐厅和酒吧饮品",
        "咖啡厅冰饮",
        "酒店服务",
        "超市和便利店销售",
        "活动和宴会服务"
      ],
      "images": [
        { "id": 1, "src": "/img/food-ice-1.jpg", "alt": "食用冰块" },
        { "id": 2, "src": "/img/food-ice-2.jpg", "alt": "食用冰应用" }
      ],
      "specifications": {
        "水源": "纯净水/矿泉水",
        "形状": "方块、圆柱、球形、管状",
        "包装": "2kg/袋，10kg/箱",
        "储存条件": "-18°C以下"
      }
    }
  ]
}
```

### 获取产品详情

- **URL**: `/api/products/:id`
- **方法**: `GET`
- **描述**: 获取指定 ID 的产品详情

#### 路径参数

| 参数 | 类型   | 描述    |
| ---- | ------ | ------- |
| id   | number | 产品 ID |

#### 响应结果

```json
{
  "status": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "工业冰",
    "description": "我们的工业冰产品专为各种工业应用而设计，具有高纯度和耐用性，可满足最严格的工业标准。",
    "features": [
      "高纯度，无杂质",
      "定制尺寸和形状",
      "快速冷却效果",
      "长时间保持低温",
      "环保生产工艺"
    ],
    "applications": [
      "食品加工冷却",
      "混凝土温度控制",
      "医疗设备冷却",
      "化工过程温度调节",
      "冷链物流"
    ],
    "images": [
      { "id": 1, "src": "/img/industrial-ice-1.jpg", "alt": "工业冰块" },
      { "id": 2, "src": "/img/industrial-ice-2.jpg", "alt": "工业冰应用" }
    ],
    "specifications": {
      "纯度": "99.9%",
      "标准尺寸": "25mm × 25mm × 25mm",
      "融化时间": "标准条件下约4-6小时",
      "包装方式": "25kg/袋，1吨/托盘"
    }
  }
}
```

### 获取产品信息

- **URL**: `/api/products/info`
- **方法**: `GET`
- **描述**: 获取产品信息列表，用于产品展示页面

#### 响应结果

```json
[
  {
    "id": 1,
    "title": "工业冰块机",
    "description": "高效率工业级制冰设备，适用于大规模冰块生产。",
    "features": [
      "高产能 - 每天可生产5吨冰块",
      "节能设计 - 比传统设备节省30%能耗",
      "智能控制 - 全自动操作系统"
    ],
    "applications": ["食品加工厂", "饮料生产线", "水产品保鲜"],
    "images": [
      {
        "id": 1,
        "src": "/img/industrial-ice-1.jpg",
        "alt": "工业冰块机正面图"
      },
      { "id": 2, "src": "/img/industrial-ice-2.jpg", "alt": "工业冰块机侧面图" }
    ],
    "specifications": {
      "产能": "5吨/天",
      "功率": "7.5kW",
      "尺寸": "2.5m × 1.8m × 2.2m",
      "重量": "850kg",
      "制冷剂": "R290（环保型）"
    }
  },
  {
    "id": 2,
    "title": "商用方冰机",
    "description": "适合餐厅、酒店的中型方冰制作设备，冰块清澈方正。",
    "features": [
      "快速制冰 - 15分钟一个周期",
      "方形冰块 - 标准2.5cm立方体",
      "内置净水系统"
    ],
    "applications": ["酒店", "餐厅", "酒吧"],
    "images": [
      { "id": 1, "src": "/img/commercial-ice-1.jpg", "alt": "商用方冰机展示图" }
    ],
    "specifications": {
      "产能": "100kg/天",
      "功率": "1.2kW",
      "尺寸": "60cm × 70cm × 80cm",
      "冰块尺寸": "2.5cm × 2.5cm × 2.5cm",
      "水箱容量": "10L"
    }
  },
  {
    "id": 3,
    "title": "雪花冰机",
    "description": "专业雪花状碎冰制作设备，适用于饮品店和甜品店。",
    "features": [
      "细腻雪花冰 - 口感绵密",
      "可调节冰质 - 从松软到紧实",
      "静音设计 - 低于45分贝"
    ],
    "applications": ["饮品店", "甜品店", "自助餐厅"],
    "images": [
      { "id": 1, "src": "/img/snow-ice-1.jpg", "alt": "雪花冰机产品图" }
    ],
    "specifications": {
      "产能": "120kg/天",
      "功率": "0.9kW",
      "尺寸": "50cm × 55cm × 75cm",
      "冰质调节": "5档可调",
      "噪音水平": "<45dB"
    }
  }
]
```

## 特性接口

### 获取特性列表

- **URL**: `/api/feature`
- **方法**: `GET`
- **描述**: 获取产品特性列表，用于首页展示

#### 响应结果

```json
{
  "status": 200,
  "message": "获取成功",
  "data": [
    {
      "title": "高性能",
      "text": "我们的冰块机采用先进技术，提供卓越的制冰性能和效率。"
    },
    {
      "title": "安全可靠",
      "text": "所有产品均符合严格的安全标准，确保长期稳定运行。"
    },
    {
      "title": "24/7 服务",
      "text": "我们提供全天候技术支持和服务，随时解决您的问题。"
    },
    {
      "title": "智能配置",
      "text": "智能控制系统让您轻松管理制冰过程，提高工作效率。"
    }
  ]
}
```

## 设置接口

### 获取用户设置

- **URL**: `/api/settings`
- **方法**: `GET`
- **描述**: 获取当前用户的应用设置
- **认证**: 需要

#### 响应结果

```json
{
  "status": 200,
  "message": "获取成功",
  "data": {
    "theme": "system",
    "language": "zh_CN",
    "notifications": true
  }
}
```

### 更新用户设置

- **URL**: `/api/settings`
- **方法**: `PUT`
- **描述**: 更新当前用户的应用设置
- **认证**: 需要

#### 请求参数

```json
{
  "theme": "dark",
  "language": "zh_CN",
  "notifications": false
}
```

#### 响应结果

```json
{
  "status": 200,
  "message": "更新成功",
  "data": {
    "theme": "dark",
    "language": "zh_CN",
    "notifications": false
  }
}
```

## 分析接口

### 上报分析数据

- **URL**: `/api/v1/analyse`
- **方法**: `POST`
- **描述**: 上报用户行为分析数据

#### 请求参数

```json
{
  "session": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "start_time": 1623456789000,
    "duration": 300000,
    "is_new_user": true,
    "referrer": "https://www.google.com",
    "entry_page": "/home"
  },
  "device": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "screen_width": 1920,
    "screen_height": 1080,
    "viewport_width": 1800,
    "viewport_height": 900,
    "pixel_ratio": 1
  },
  "app": {
    "sdk_version": "1.0.0",
    "environment": "production"
  },
  "events": [
    {
      "event_type": "page_view",
      "timestamp": 1623456789000,
      "event_id": "ev-123456",
      "page_path": "/home",
      "page_title": "首页"
    },
    {
      "event_type": "click",
      "timestamp": 1623456799000,
      "event_id": "ev-123457",
      "element_type": "button",
      "element_id": "product-1"
    }
  ],
  "metadata": {
    "timestamp": 1623456899000,
    "client_type": "web",
    "event_count": 2
  }
}
```

#### 响应结果

```json
{
  "success": true,
  "message": "数据接收成功",
  "request_id": "req-789012",
  "timestamp": 1623456900000
}
```

### 上报事件数据

- **URL**: `/api/v1/events`
- **方法**: `POST`
- **描述**: 上报用户事件数据

#### 请求参数

```json
{
  "events": [
    {
      "event_type": "page_view",
      "timestamp": 1623456789000,
      "event_id": "ev-123456",
      "page_path": "/home",
      "page_title": "首页"
    },
    {
      "event_type": "click",
      "timestamp": 1623456799000,
      "event_id": "ev-123457",
      "element_type": "button",
      "element_id": "product-1"
    }
  ],
  "metadata": {
    "timestamp": 1623456899000,
    "sdk_version": "1.0.0",
    "environment": "production",
    "client_type": "web",
    "event_count": 2
  }
}
```

#### 响应结果

```json
{
  "success": true,
  "message": "事件数据接收成功",
  "request_id": "req-789013",
  "timestamp": 1623456900000
}
```

### 上报会话数据

- **URL**: `/api/v1/session`
- **方法**: `POST`
- **描述**: 上报用户会话数据

#### 请求参数

```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": 1623456789000,
  "is_new_user": true,
  "referrer": "https://www.google.com",
  "entry_page": "/home",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  "screen_size": "1920x1080",
  "viewport_size": "1800x900",
  "device_type": "desktop",
  "browser": "Chrome",
  "os": "Windows"
}
```

#### 响应结果

```json
{
  "success": true,
  "message": "会话数据接收成功",
  "request_id": "req-789014",
  "timestamp": 1623456900000
}
```

### 批量收集分析数据

- **URL**: `/api/v1/analyse/collect`
- **方法**: `POST`
- **描述**: 批量收集用户行为分析数据

#### 请求参数

```json
{
  "events": [
    {
      "uvId": "550e8400-e29b-41d4-a716-446655440000",
      "timestamp": 1623456789000,
      "path": "/home",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      "eventType": "pageview",
      "sdkVersion": "1.2.0"
    },
    {
      "uvId": "550e8400-e29b-41d4-a716-446655440000",
      "timestamp": 1623456799000,
      "path": "/products",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      "eventType": "pageview",
      "sdkVersion": "1.2.0"
    }
  ],
  "metadata": {
    "timestamp": 1623456899000,
    "uvId": "550e8400-e29b-41d4-a716-446655440000",
    "sdkVersion": "1.2.0",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "currentPath": "/products"
  }
}
```

#### 响应结果

```json
{
  "success": true,
  "message": "数据收集成功",
  "request_id": "req-789015",
  "timestamp": 1623456900000
}
```

### 检查分析服务状态

- **URL**: `/api/v1/analyse/status`
- **方法**: `GET`
- **描述**: 检查分析服务是否可用

#### 响应结果

```json
{
  "status": "ok",
  "version": "1.0.0",
  "timestamp": 1623456900000
}
```
