version: '3.8'

services:
  # 前端应用
  frontend:
    image: ${IMAGE_NAME:-front_logix_admin}:${ENV:-latest}
    container_name: ${IMAGE_NAME:-front_logix_admin}-${ENV:-latest}
    ports:
      - "8080:80"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 10s
