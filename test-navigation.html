<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #40a9ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .error { background: #fff2f0; border: 1px solid #ffccc7; color: #ff4d4f; }
        .info { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
    </style>
</head>
<body>
    <h1>🧪 根路径导航逻辑测试</h1>

    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>这个页面帮助您测试根路径导航逻辑是否正常工作。</p>
        <p><strong>正确的服务器地址</strong>: <code>http://localhost:8000</code> ✅</p>
        <p><strong>现在端口已修正</strong>: 服务器运行在 8000 端口</p>
    </div>

    <div class="test-section">
        <h2>🔧 测试步骤</h2>

        <h3>步骤 1: 清除浏览器存储</h3>
        <button class="button" onclick="clearStorage()">清除 localStorage</button>
        <div id="clearStatus"></div>

        <h3>步骤 2: 检查当前登录状态</h3>
        <button class="button" onclick="checkLoginStatus()">检查登录状态</button>
        <div id="loginStatus"></div>

        <h3>步骤 3: 测试导航</h3>
        <a href="http://localhost:8000/" class="button" target="_blank">访问根路径 (新窗口)</a>
        <a href="http://localhost:8000/login" class="button" target="_blank">访问登录页 (新窗口)</a>
        <a href="http://localhost:8000/home" class="button" target="_blank">访问首页 (新窗口)</a>
    </div>

    <div class="test-section">
        <h2>🐛 常见问题</h2>
        <ul>
            <li><strong>端口已修正</strong>: 现在服务器运行在 <code>http://localhost:8000</code> ✅</li>
            <li><strong>缓存问题</strong>: 清除浏览器缓存和 localStorage</li>
            <li><strong>服务器未启动</strong>: 确保开发服务器正在运行</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📊 预期行为</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #fafafa;">
                <th style="border: 1px solid #d9d9d9; padding: 8px;">访问地址</th>
                <th style="border: 1px solid #d9d9d9; padding: 8px;">登录状态</th>
                <th style="border: 1px solid #d9d9d9; padding: 8px;">预期跳转</th>
            </tr>
            <tr>
                <td style="border: 1px solid #d9d9d9; padding: 8px;"><code>http://localhost:8000/</code></td>
                <td style="border: 1px solid #d9d9d9; padding: 8px;">未登录</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px;">→ <code>/login</code></td>
            </tr>
            <tr>
                <td style="border: 1px solid #d9d9d9; padding: 8px;"><code>http://localhost:8000/</code></td>
                <td style="border: 1px solid #d9d9d9; padding: 8px;">已登录</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px;">→ <code>/home</code></td>
            </tr>
        </table>
    </div>

    <script>
        function clearStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                document.getElementById('clearStatus').innerHTML =
                    '<div class="status success">✅ 存储已清除</div>';
            } catch (error) {
                document.getElementById('clearStatus').innerHTML =
                    '<div class="status error">❌ 清除失败: ' + error.message + '</div>';
            }
        }

        function checkLoginStatus() {
            try {
                const token = localStorage.getItem('front_logix_token') ||
                             localStorage.getItem('token') ||
                             localStorage.getItem('auth_token');

                const userInfoStr = localStorage.getItem('userInfo');
                let userInfo = null;

                if (userInfoStr) {
                    try {
                        userInfo = JSON.parse(userInfoStr);
                    } catch (e) {
                        // ignore
                    }
                }

                const hasValidToken = !!(token && token.trim() !== '');
                const hasValidUserInfo = !!(userInfo && (userInfo.name || userInfo.id || userInfo.username));
                const isLoggedIn = hasValidToken && hasValidUserInfo;

                let statusHtml = '<div class="status info">';
                statusHtml += '<strong>登录状态检查结果:</strong><br>';
                statusHtml += '• Token: ' + (hasValidToken ? '✅ 有效' : '❌ 无效或不存在') + '<br>';
                statusHtml += '• 用户信息: ' + (hasValidUserInfo ? '✅ 有效' : '❌ 无效或不存在') + '<br>';
                statusHtml += '• 整体状态: ' + (isLoggedIn ? '✅ 已登录' : '❌ 未登录');
                statusHtml += '</div>';

                if (userInfo) {
                    statusHtml += '<div class="status info">';
                    statusHtml += '<strong>用户信息:</strong><br>';
                    statusHtml += '<pre>' + JSON.stringify(userInfo, null, 2) + '</pre>';
                    statusHtml += '</div>';
                }

                document.getElementById('loginStatus').innerHTML = statusHtml;
            } catch (error) {
                document.getElementById('loginStatus').innerHTML =
                    '<div class="status error">❌ 检查失败: ' + error.message + '</div>';
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>
