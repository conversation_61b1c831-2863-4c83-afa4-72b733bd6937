# 冰块订单管理系统

本系统提供了冰块订单管理功能，支持用户、冰块类型和订单的增删改查操作。

## 功能特点

1. **用户管理**：

   - 添加、编辑、删除用户
   - 查看用户列表

2. **冰块类型管理**：

   - 添加、编辑、删除冰块类型
   - 查看冰块类型列表

3. **订单管理**：
   - 创建、编辑、删除订单
   - 查看订单列表和详情
   - 支持多种冰块类型、数量和尺寸
   - 记录送达日期时间、地址、联系人和联系电话
   - 跟踪订单状态（待处理、已确认、处理中、已发货、已送达、已取消）

## 技术栈

- 后端：Spring Boot, Spring Data JPA, H2 数据库
- 前端：HTML, CSS, JavaScript, Bootstrap 5

## API 接口

### 用户 API

- `GET /api/users` - 获取所有用户
- `GET /api/users/{id}` - 获取指定 ID 的用户
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新指定 ID 的用户
- `DELETE /api/users/{id}` - 删除指定 ID 的用户

### 冰块类型 API

- `GET /api/ice-types` - 获取所有冰块类型
- `GET /api/ice-types/{id}` - 获取指定 ID 的冰块类型
- `POST /api/ice-types` - 创建新冰块类型
- `PUT /api/ice-types/{id}` - 更新指定 ID 的冰块类型
- `DELETE /api/ice-types/{id}` - 删除指定 ID 的冰块类型

### 订单 API

- `GET /api/orders` - 获取所有订单
- `GET /api/orders?userId={userId}` - 获取指定用户的订单
- `GET /api/orders/{id}` - 获取指定 ID 的订单
- `POST /api/orders` - 创建新订单
- `PUT /api/orders/{id}` - 更新指定 ID 的订单
- `PUT /api/orders/{id}/status?status={status}` - 更新指定 ID 订单的状态
- `DELETE /api/orders/{id}` - 删除指定 ID 的订单

## 数据模型

### 用户 (User)

```json
{
  "id": 1,
  "username": "张三",
  "email": "<EMAIL>",
  "phone": "13800138001"
}
```

### 冰块类型 (IceType)

```json
{
  "id": 1,
  "name": "方块冰",
  "description": "标准方形冰块，适合各种饮品"
}
```

### 订单 (IceOrder)

```json
{
  "id": 1,
  "userId": 1,
  "username": "张三",
  "items": [
    {
      "id": 1,
      "iceTypeId": 1,
      "iceTypeName": "方块冰",
      "quantity": 100,
      "size": "2x2x2cm"
    },
    {
      "id": 2,
      "iceTypeId": 2,
      "iceTypeName": "碎冰",
      "quantity": 50,
      "size": "小颗粒"
    }
  ],
  "deliveryDateTime": "2023-06-15T14:00:00",
  "deliveryAddress": "北京市朝阳区建国路88号",
  "contactPerson": "张三",
  "contactPhone": "13800138001",
  "status": "CONFIRMED",
  "createdAt": "2023-06-13T10:30:00",
  "updatedAt": "2023-06-13T10:30:00"
}
```

## 使用说明

1. 启动应用程序
2. 访问 http://localhost:8080/ 打开管理界面
3. 使用界面进行用户、冰块类型和订单的管理

## 开发环境

- JDK 21
- Maven
- Spring Boot 3.x
- H2 数据库（内存模式）

## 配置说明

应用程序使用 H2 内存数据库，启动时会自动创建表结构并添加一些示例数据。

H2 控制台可通过 http://localhost:8080/h2-console 访问：

- JDBC URL: `jdbc:h2:mem:icedb`
- 用户名: `sa`
- 密码: (留空)
